package com.stt.android.data.routes

import android.content.Context
import androidx.work.ListenableWorker
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.data.Local
import com.stt.android.data.source.local.routes.RouteDao
import com.stt.android.domain.routes.NavigateLoader
import com.stt.android.remote.routes.RouteRemoteApi
import dagger.Lazy
import javax.inject.Inject

/**
 * A job that takes care of syncing local changes to routes to the server and pulling changes from
 * the server. This job handles creation, deletion and update of routes based on their state in
 * the local database.
 */
class RouteRemoteSyncJob(
    private val workManager: Lazy<WorkManager>,
    private val routeRemoteApi: RouteRemoteApi,
    private val remoteMapper: RouteRemoteMapper,
    private val routeLocalDataSource: RouteDataSource,
    private val routeDao: RouteDao,
    private val foregroundInfoBuilder: ForegroundInfoBuilder,
    appContext: Context,
    params: WorkerParameters,
    private val navigateLoader: NavigateLoader,
) : BaseRouteRemoteSyncJob(
    workManager,
    routeRemoteApi,
    remoteMapper,
    routeLocalDataSource,
    routeDao,
    foregroundInfoBuilder,
    appContext,
    params
) {
    class Factory
    @Inject constructor(
        @Local private val routeLocalDataSource: RouteDataSource,
        private val routeRemoteApi: RouteRemoteApi,
        private val remoteMapper: RouteRemoteMapper,
        private val routeDao: RouteDao,
        private val foregroundInfoBuilder: ForegroundInfoBuilder,
        private val workManager: Lazy<WorkManager>,
        private val navigateLoader: NavigateLoader
    ) : CoroutineWorkerAssistedFactory {
        override fun create(context: Context, params: WorkerParameters): ListenableWorker {
            return RouteRemoteSyncJob(
                routeLocalDataSource = routeLocalDataSource,
                routeRemoteApi = routeRemoteApi,
                remoteMapper = remoteMapper,
                routeDao = routeDao,
                foregroundInfoBuilder = foregroundInfoBuilder,
                workManager = workManager,
                appContext = context,
                params = params,
                navigateLoader = navigateLoader
            )
        }
    }

    override fun isNavigateOngoing(): Boolean {
        val latestStatus = navigateLoader.getNavigateStateFlow().value
        return latestStatus?.isNavigateOngoing() ?: false
    }
}
