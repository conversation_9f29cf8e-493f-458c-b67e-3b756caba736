package com.stt.android.data.routes

import android.content.Context
import androidx.work.Constraints
import androidx.work.CoroutineWorker
import androidx.work.Data
import androidx.work.ExistingWorkPolicy
import androidx.work.ForegroundInfo
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequest
import androidx.work.OutOfQuotaPolicy
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.source.local.routes.RouteDao
import com.stt.android.data.utils.asWorkReportData
import com.stt.android.domain.routes.Route
import com.stt.android.exceptions.remote.ClientError
import com.stt.android.exceptions.remote.ServerError
import com.stt.android.remote.routes.RouteRemoteApi
import com.stt.android.worker.ifNotAlreadyScheduled
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toCollection
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withContext
import dagger.Lazy
import timber.log.Timber

open class BaseRouteRemoteSyncJob(
    private val workManager: Lazy<WorkManager>,
    private val routeRemoteApi: RouteRemoteApi,
    private val remoteMapper: RouteRemoteMapper,
    private val routeLocalDataSource: RouteDataSource,
    private val routeDao: RouteDao,
    private val foregroundInfoBuilder: ForegroundInfoBuilder,
    appContext: Context,
    params: WorkerParameters
) : CoroutineWorker(appContext, params) {


    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        runSuspendCatching {
            // Push local changes to backend
            val itemsToSync = routeLocalDataSource.fetchUnsynced()
            syncToRemote(itemsToSync)

            // Pull updates from backend
            fetchRoutesFromRemote()

            Result.success()
        }.getOrElse { e ->
            Timber.w(e, "Error syncing routes with remote")
            when (e) {
                is ServerError.ServiceUnavailable -> Result.retry()
                else -> Result.failure(e.asWorkReportData())
            }
        }
    }

    private suspend fun fetchRoutesFromRemote() = withContext(Dispatchers.IO) {
        val locallyStoredRoutes = routeLocalDataSource
            .fetchRoutes(includeSegments = false)
            .first()
            .filterNot { it.isInProgress }

        val fetchAllWithSegments = locallyStoredRoutes.isEmpty() && inputData.getBoolean(
            ARG_FULL_FETCH_IF_LOCAL_IS_EMPTY,
            DEFAULT_FULL_FETCH_IF_LOCAL_IS_EMPTY
        )

        val storedRoutes = routeRemoteApi
            .fetchRoutesPaged(includeSegments = fetchAllWithSegments)
            .map { it.map(remoteMapper.toDomainEntity()) }
            .map { remoteRoutes ->
                val unsyncedRemoteRoutes = if (!fetchAllWithSegments) {
                    val syncOnlyWatchEnabled = inputData.getBoolean(
                        ARG_ONLY_WATCH_ENABLED_IN_PARTIAL_FETCH,
                        DEFAULT_ONLY_WATCH_ENABLED_IN_PARTIAL_FETCH
                    )
                    val routesToConsiderForSync =
                        if (syncOnlyWatchEnabled) remoteRoutes.filter { it.watchEnabled } else remoteRoutes

                    filterNewestRoutesFromRemote(
                        locallyStoredRoutes,
                        routesToConsiderForSync
                    ).mapNotNull {
                        runSuspendCatching {
                            remoteMapper.toDomainEntity()
                                .invoke(routeRemoteApi.fetchRouteByKey(it.key))
                        }.getOrElse { e ->
                            Timber.w(e, "Error fetching route ${it.key}")
                            null
                        }
                    }
                } else {
                    remoteRoutes
                }

                if (isActive) {
                    storeRemoteRoutesToLocal(unsyncedRemoteRoutes, routeLocalDataSource)
                } else {
                    emptyList()
                }
            }
            .toCollection(mutableListOf())
            .flatten()

        val syncWithWatch = inputData.getBoolean(
            ARG_SYNC_WITH_WATCH,
            DEFAULT_SYNC_WITH_WATCH
        )

        if (isActive && syncWithWatch && storedRoutes.any { it.watchEnabled }) {
            RouteSyncWithWatchJob.schedule(workManager.get(), navigateOngoing = isNavigateOngoing())
        }
    }

    protected open fun isNavigateOngoing(): Boolean = false

    private suspend fun syncToRemote(itemsToSync: List<Route>) {
        val remoteRoutes = routeRemoteApi.fetchRoutesPaged(includeSegments = false)
            .toCollection(mutableListOf())
            .flatten()

        // Map all remote routes by watchRouteId
        val remoteRoutesWatchRouteIdMap = remoteRoutes.filter { it.syncedToWatch }
            .associateBy { it.watchRouteId }

        var deleteCount = 0
        var updateCount = 0
        var createCount = 0

        for (localRoute in itemsToSync) {
            // In case where sync from watch happened before sync from server,
            // local routes will have watchRouteId set with empty keys. So in this case, we map
            // these routes to equivalent remote routes by watchRouteId to avoid creating duplicates.
            val route = if (localRoute.syncedOnlyToWatch) {
                // Get the server key by watchRouteId from remote routes map
                val remoteRoute =
                    remoteRoutesWatchRouteIdMap[localRoute.watchRouteId]
                localRoute.copy(key = remoteRoute?.key ?: "")
            } else {
                // The routes hasn't been synced to the server yet
                localRoute
            }

            when {
                // Deleted case
                route.deleted -> {
                    runSuspendCatching {
                        routeRemoteApi.deleteRoute(route.key)
                        deleteCount++
                    }.onSuccess {
                        routeLocalDataSource.updateOrCreate(route.copy(locallyChanged = false))
                    }.onFailure {
                        // An error may occur when trying to delete an already deleted route
                        // on the server. Currently we don't have any means to know if a route
                        // was already deleted.
                        if (it is ClientError.NotFound) {
                            routeDao.delete(route.id)
                            deleteCount++
                        }
                    }
                }

                // Updated case
                route.key.isNotEmpty() -> {
                    val remoteRoute = remoteMapper.toDataEntity()(localRoute)
                    runSuspendCatching {
                        routeRemoteApi.updateRoute(remoteRoute)
                        updateCount++
                    }.onSuccess {
                        routeLocalDataSource.updateOrCreate(route.copy(locallyChanged = false))
                    }.onFailure {
                        Timber.w(it, "Failed to update route to backend (key=${route.key} ID=${route.id})")
                    }
                }

                // Created case
                else -> {
                    runSuspendCatching {
                        val remoteRoute = remoteMapper.toDataEntity()(localRoute)
                        val routeWithKey = routeRemoteApi.createRoute(remoteRoute)
                        routeLocalDataSource.updateOrCreate(
                            route.copy(
                                key = routeWithKey.key,
                                locallyChanged = false,
                            )
                        )
                        createCount++
                    }.onFailure(::upsertErrorHandler)
                }
            }
        }

        Timber.d("Routes synced successfully to server (deleted $deleteCount, updated $updateCount, created $createCount)")
    }

    private fun upsertErrorHandler(error: Throwable) {
        // Prevent HTTP 400 error from breaking the sync of other routes
        if (error is ClientError.BadRequest) {
            // pass
        } else {
            Timber.w(error, "Route operation failed")
            throw error
        }
    }


    override suspend fun getForegroundInfo(): ForegroundInfo =
        foregroundInfoBuilder.buildForegroundInfo(applicationContext)


    companion object {
        const val TAG = "RouteRemoteSyncJob"

        private const val ARG_FULL_FETCH_IF_LOCAL_IS_EMPTY = "fullFetchIfLocalIsEmpty"
        private const val DEFAULT_FULL_FETCH_IF_LOCAL_IS_EMPTY = false

        private const val ARG_ONLY_WATCH_ENABLED_IN_PARTIAL_FETCH = "onlyWatchEnabledInPartialFetch"
        private const val DEFAULT_ONLY_WATCH_ENABLED_IN_PARTIAL_FETCH = true

        private const val ARG_SYNC_WITH_WATCH = "syncWithWatch"
        private const val DEFAULT_SYNC_WITH_WATCH = true

        fun schedule(
            workManager: WorkManager,
            immediate: Boolean = false,
            syncWithWatch: Boolean = DEFAULT_SYNC_WITH_WATCH,
            fullFetchIfLocalIsEmpty: Boolean = DEFAULT_FULL_FETCH_IF_LOCAL_IS_EMPTY,
            onlyWatchEnabledInPartialFetch: Boolean = DEFAULT_ONLY_WATCH_ENABLED_IN_PARTIAL_FETCH,
        ) {
            workManager.ifNotAlreadyScheduled(TAG) {
                workManager.enqueueUniqueWork(
                    TAG,
                    ExistingWorkPolicy.APPEND_OR_REPLACE,
                    OneTimeWorkRequest.Builder(RouteRemoteSyncJob::class.java)
                        .addTag(TAG)
                        .setInputData(
                            Data.Builder()
                                .putBoolean(ARG_SYNC_WITH_WATCH, syncWithWatch)
                                .putBoolean(ARG_FULL_FETCH_IF_LOCAL_IS_EMPTY, fullFetchIfLocalIsEmpty)
                                .putBoolean(ARG_ONLY_WATCH_ENABLED_IN_PARTIAL_FETCH, onlyWatchEnabledInPartialFetch)
                                .build()
                        )
                        .setConstraints(
                            Constraints.Builder()
                                .setRequiredNetworkType(NetworkType.CONNECTED)
                                .build()
                        )
                        .apply {
                            if (immediate) {
                                setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST)
                            }
                        }
                        .build()
                )
            }
        }

        @JvmStatic
        fun cancelAll(workManager: WorkManager) {
            workManager.cancelAllWorkByTag(TAG)
        }
    }

    interface ForegroundInfoBuilder {
        fun buildForegroundInfo(context: Context): ForegroundInfo
    }
}
