package com.stt.android.domain.diary.models

import java.time.LocalDate

data class RecoveryStateGraphData(
    val date: LocalDate,
    val recoveryStateData: RecoveryStateData,
)

data class RecoveryStateData(
    val recoveryScore: Int,
    val recoveryZone: RecoveryZone,
    val contributors: RecoveryStateContributors? = null,
)

enum class RecoveryZone {
    OPTIMAL, GOOD, FAIR, POOR, LIMITED, NO_DATA
}

data class RecoveryStateContributors(
    val sleepDuration: RecoveryStateContributor.SleepDuration?,
    val hrv: RecoveryStateContributor.Hrv?,
    val restHr: RecoveryStateContributor.RestHr?,
    val minHr: RecoveryStateContributor.MinHr?,
    val trainingFatigue: RecoveryStateContributor.TrainingFatigue?,
    val resources: RecoveryStateContributor.Resources?,
) {
    constructor(
        last1DaySleepDuration: Double,
        last7DaysSleepDuration: Double,
        last1DaySleepDurationTotalSeconds: Long,
        last7DaysSleepDurationTotalSeconds: Long,
        last1DayHrv: Int,
        last7DaysHrv: Int,
        last1DayRestHR: Int,
        last7DaysRestHR: Int,
        last1DayMinHR: Int,
        last7DaysMinHR: Int,
        yesterdayTSS: Int,
        todayTSS: Int,
        todayTSB: Int,
        last7DaysFeeling: Double,
        todayResources: Int,
        hrvLowRange: Int = 0,
        hrvHighRange: Int = 0,
        feelingDistribution: Map<Int, Double> = emptyMap(),
        ctlPercent: Float = 0f,
        yesterdayCtlPercent: Float = 0f,
    ) : this(
        if (last1DaySleepDuration > 0.0 || last7DaysSleepDuration > 0.0) {
            RecoveryStateContributor.SleepDuration(
                lastNight = last1DaySleepDuration,
                avg = last7DaysSleepDuration,
                lastNightTotalSeconds = last1DaySleepDurationTotalSeconds,
                avgTotalSeconds = last7DaysSleepDurationTotalSeconds
            )
        } else null,
        if (last1DayHrv > 0 || last7DaysHrv > 0) {
            RecoveryStateContributor.Hrv(last1DayHrv, last7DaysHrv, hrvLowRange, hrvHighRange)
        } else null,
        if (last1DayRestHR > 0 || last7DaysRestHR > 0) {
            RecoveryStateContributor.RestHr(last1DayRestHR, last7DaysRestHR)
        } else null,
        if (last1DayMinHR > 0 || last7DaysMinHR > 0) {
            RecoveryStateContributor.MinHr(last1DayMinHR, last7DaysMinHR)
        } else null,
        if (yesterdayTSS > 0 || todayTSS > 0 || last7DaysFeeling > 0) {
            RecoveryStateContributor.TrainingFatigue(
                yesterdayTSS,
                todayTSS,
                todayTSB,
                last7DaysFeeling,
                feelingDistribution,
                ctlPercent,
                yesterdayCtlPercent,
            )
        } else null,
        todayResources.takeIf { it > 0 }?.let { RecoveryStateContributor.Resources(it) }
    )
}

sealed class RecoveryStateContributor {
    data class SleepDuration(
        val lastNight: Double,
        val avg: Double,
        val lastNightTotalSeconds: Long,
        val avgTotalSeconds: Long,
    ) : RecoveryStateContributor()

    data class Hrv(
        val lastNight: Int,
        val avg: Int,
        val lowRange: Int = 0,
        val highRange: Int = 0,
    ) : RecoveryStateContributor()

    data class RestHr(
        val today: Int,
        val avg: Int,
    ) : RecoveryStateContributor()

    data class MinHr(
        val today: Int,
        val avg: Int,
    ) : RecoveryStateContributor()

    data class TrainingFatigue(
        val yesterdayTSS: Int,
        val todayTSS: Int,
        val todayTSB: Int,
        val last7DaysFeeling: Double,
        val feelingDistribution: Map<Int, Double> = emptyMap(),
        val ctlPercent: Float = 0f,
        val yesterdayCtlPercent: Float = 0f,
    ) : RecoveryStateContributor()

    data class Resources(
        val today: Int,
    ) : RecoveryStateContributor()
}
