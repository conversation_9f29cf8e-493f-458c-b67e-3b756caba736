package com.stt.android.diary.recovery.composables

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.stt.android.compose.modifiers.clickable
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.ActionBottomSheet
import com.stt.android.diary.recovery.model.getNameRes
import com.stt.android.diary.recovery.v2.RecoveryV2Event
import com.stt.android.diary.recovery.v2.RecoveryV2State
import com.stt.android.diary.recovery.v2.SleepChartSelectionType
import com.stt.android.compose.ui.R

@Composable
internal fun SleepComparisonGraphSelectionBottomSheet(
    state: RecoveryV2State.Loaded,
    onEvent: (RecoveryV2Event) -> Unit,
    modifier: Modifier = Modifier,
) {
    val chartStateData = state.recoveryChartStateData
    
    if (chartStateData !is com.stt.android.diary.recovery.v2.RecoveryChartStateData.Loaded) return
    
    val chartContributors = chartStateData.recoveryChartContributors ?: return
    val sleepComparisonChartData = chartContributors.sleepComparisonChartData ?: return

    when {
        state.showPrimaryComparisonGraphSelection -> SleepComparisonPrimaryGraphSelectionBottomSheet(
            currentType = sleepComparisonChartData.leftSelectionType,
            onEvent = onEvent,
            modifier = modifier,
        )

        state.showSecondaryComparisonGraphSelection -> SleepComparisonSecondaryGraphSelectionBottomSheet(
            currentType = sleepComparisonChartData.rightSelectionType,
            onEvent = onEvent,
            modifier = modifier,
        )

        else -> Unit
    }
}

@Composable
private fun SleepComparisonPrimaryGraphSelectionBottomSheet(
    currentType: SleepChartSelectionType,
    onEvent: (RecoveryV2Event) -> Unit,
    modifier: Modifier = Modifier,
) = SleepComparisonSelectionBottomSheet(
    selectedType = currentType,
    list = listOf(
        SleepChartSelectionType.TOTAL_TIME,
        SleepChartSelectionType.SLEEP_REGULARITY,
        SleepChartSelectionType.SLEEP_DURATION,
        SleepChartSelectionType.NAP_DURATION
    ),
    onSelected = {
        onEvent(RecoveryV2Event.UpdateSleepLeftSelectionType(it))
        onEvent(RecoveryV2Event.HideSleepComparisonPrimaryGraphSelection)
    },
    onDismiss = {
        onEvent(RecoveryV2Event.HideSleepComparisonPrimaryGraphSelection)
    },
    modifier = modifier,
)

@Composable
private fun SleepComparisonSecondaryGraphSelectionBottomSheet(
    currentType: SleepChartSelectionType,
    onEvent: (RecoveryV2Event) -> Unit,
    modifier: Modifier = Modifier
) = SleepComparisonSelectionBottomSheet(
    selectedType = currentType,
    list = listOf(
        SleepChartSelectionType.MIN_SLEEP_HR,
        SleepChartSelectionType.AVG_SLEEP_HR,
        SleepChartSelectionType.MAX_SLEEP_SPO2,
        SleepChartSelectionType.TRAINING_DURATION,
        SleepChartSelectionType.WAKE_UP_RESOURCES,
        SleepChartSelectionType.NONE
    ),
    onSelected = {
        onEvent(RecoveryV2Event.UpdateSleepRightSelectionType(it))
        onEvent(RecoveryV2Event.HideSleepComparisonSecondaryGraphSelection)
    },
    onDismiss = {
        onEvent(RecoveryV2Event.HideSleepComparisonSecondaryGraphSelection)
    },
    modifier = modifier,
)

@Composable
private fun SleepComparisonSelectionBottomSheet(
    selectedType: SleepChartSelectionType,
    list: List<SleepChartSelectionType>,
    onSelected: (SleepChartSelectionType) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    ActionBottomSheet(
        onDismiss = onDismiss,
        modifier = modifier,
        actionTitle = stringResource(R.string.segmented_control_cancel).uppercase(),
        onActionClick = onDismiss,
    ) {
        val configuration = LocalConfiguration.current
        val isLandscape = configuration.orientation == android.content.res.Configuration.ORIENTATION_LANDSCAPE
        
        if (isLandscape) {
            LazyColumn(
                modifier = Modifier.heightIn(max = 250.dp)
            ) {
                items(list) { type ->
                    SleepComparisonTypeSelection(
                        type = type,
                        selected = type == selectedType,
                        onSelected = onSelected,
                    )
                    HorizontalDivider(color = MaterialTheme.colorScheme.lightGrey)
                }
            }
        } else {
            list.forEach { type ->
                SleepComparisonTypeSelection(
                    type = type,
                    selected = type == selectedType,
                    onSelected = onSelected,
                )
                HorizontalDivider(color = MaterialTheme.colorScheme.lightGrey)
            }
        }
    }
}

@Composable
private fun SleepComparisonTypeSelection(
    type: SleepChartSelectionType,
    selected: Boolean,
    onSelected: (SleepChartSelectionType) -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .clickable(onClick = { onSelected(type) })
            .fillMaxWidth()
            .defaultMinSize(minHeight = 56.dp)
            .padding(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            modifier = Modifier.weight(1f),
            text = stringResource(type.getNameRes()),
            color = MaterialTheme.colorScheme.onSurface,
            style = MaterialTheme.typography.bodyLarge,
        )
        RadioButton(
            selected = selected,
            onClick = null,
        )
    }
} 
