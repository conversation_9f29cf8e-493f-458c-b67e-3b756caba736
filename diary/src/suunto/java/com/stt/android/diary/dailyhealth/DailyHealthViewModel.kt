package com.stt.android.diary.dailyhealth

import android.content.Context
import androidx.compose.ui.text.AnnotatedString
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.model.valueTypeRes
import com.stt.android.chart.impl.usecases.CalculateDateRangeUseCase
import com.stt.android.diary.dailyhealth.usecase.CreateHealthItemsUseCase
import com.stt.android.diary.dailyhealth.usecase.CreateTimeInfoUseCase
import com.stt.android.diary.dailyhealth.usecase.CreateChartHighlightUseCase
import com.stt.android.chart.impl.screen.ChartHighlightViewData
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.domain.diary.training.TrainingPeriodsCalculationUseCase
import com.stt.android.diary.trainingv2.TrainingDatePeriodFormatter
import com.stt.android.diary.trainingv2.TrainingDateRange
import com.stt.android.diary.recovery.v2.DatePickerData
import com.stt.android.ui.utils.DateFormatter
import com.stt.android.controllers.UserSettingsController
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.time.LocalDate
import javax.inject.Inject
import com.stt.android.R as BaseR
import com.stt.android.home.diary.R as DiaryR

@HiltViewModel
class DailyHealthViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    @ApplicationContext private val appContext: Context,
    private val createHealthItemsUseCase: CreateHealthItemsUseCase,
    private val calculateDateRangeUseCase: CalculateDateRangeUseCase,
    private val createTimeInfoUseCase: CreateTimeInfoUseCase,
    private val createChartHighlightUseCase: CreateChartHighlightUseCase,
    private val trainingPeriodsCalculationUseCase: TrainingPeriodsCalculationUseCase,
    private val trainingDatePeriodFormatter: TrainingDatePeriodFormatter,
    private val dateFormatter: DateFormatter,
    private val userSettingsController: UserSettingsController,
) : ViewModel() {

    private val _viewData = MutableStateFlow<DailyHealthViewData>(DailyHealthViewData.Initial)
    val viewData: StateFlow<DailyHealthViewData> = _viewData.asStateFlow()
    
    private val _currentDate = MutableStateFlow(LocalDate.now())
    private val _currentTimeGranularity: StateFlow<ChartGranularity> = savedStateHandle
        .getStateFlow(KEY_CHART_GRANULARITY, ChartGranularity.DAILY)
    private val _pageNumber = MutableStateFlow(0)
    private val hasNext = MutableStateFlow(false)
    private val hasPrevious = MutableStateFlow(true)
    
    private var showHighlightJob: Job? = null

    private fun chartGranularityToGraphTimeRange(granularity: ChartGranularity): GraphTimeRange = when (granularity) {
        ChartGranularity.WEEKLY -> GraphTimeRange.CURRENT_WEEK
        ChartGranularity.MONTHLY -> GraphTimeRange.CURRENT_MONTH
        ChartGranularity.SIX_MONTHS -> GraphTimeRange.SIX_MONTHS
        ChartGranularity.SEVEN_DAYS -> GraphTimeRange.SEVEN_DAYS
        ChartGranularity.THIRTY_DAYS -> GraphTimeRange.THIRTY_DAYS
        ChartGranularity.YEARLY -> GraphTimeRange.CURRENT_YEAR
        ChartGranularity.SIX_WEEKS -> GraphTimeRange.SIX_WEEKS
        ChartGranularity.EIGHT_YEARS -> GraphTimeRange.EIGHT_YEARS
        else -> throw IllegalArgumentException("Unsupported chart granularity: $granularity")
    }

    private fun getPeriodsByPageNumber(
        pageNumber: Int,
        timeRange: GraphTimeRange
    ): ClosedRange<LocalDate> {
        val periods = trainingPeriodsCalculationUseCase(
            pageNumber = pageNumber,
            firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek,
            timeRange = timeRange,
        )
        return periods.firstPeriod
    }

    init {
        combine(
            _currentDate,
            _currentTimeGranularity,
            _pageNumber,
            ::updateViewData
        )
        .launchIn(viewModelScope)
    }

    private suspend fun updateViewData(
        date: LocalDate,
        currentTimeGranularity: ChartGranularity,
        pageNumber: Int
    ) {
        val (timeRange, actualDate) = if (currentTimeGranularity == ChartGranularity.DAILY) {
            val range = calculateDateRangeUseCase(
                chartGranularity = currentTimeGranularity,
                chartPageIndex = 0,
                chartPageCount = 1,
                today = date
            )
            val hasNextValue = !date.isEqual(LocalDate.now())
            hasNext.value = hasNextValue
            hasPrevious.value = true
            
            range to date
        } else {
            val graphTimeRange = chartGranularityToGraphTimeRange(currentTimeGranularity)
            val range = getPeriodsByPageNumber(pageNumber, graphTimeRange)
            
            val hasNextValue = !range.contains(LocalDate.now())
            hasNext.value = hasNextValue
            hasPrevious.value = true
            
            range to range.endInclusive
        }
        
        val datePickerData = if (currentTimeGranularity == ChartGranularity.DAILY) {
            val displayTitle = when {
                actualDate.isEqual(LocalDate.now()) -> appContext.getString(DiaryR.string.date_picker_today)
                actualDate.isEqual(LocalDate.now().minusDays(1)) -> appContext.getString(DiaryR.string.date_picker_yesterday)
                else -> dateFormatter.formatRelativeDate(actualDate)
            }
            DatePickerData(
                currentDate = actualDate,
                displayTitle = displayTitle,
                canNavigateForward = hasNext.value,
                canNavigateBack = hasPrevious.value,
                trainingDateRange = TrainingDateRange.CustomRange(displayTitle)
            )
        } else {
            val displayTitle = trainingDatePeriodFormatter.format(timeRange, chartGranularityToGraphTimeRange(currentTimeGranularity))
            DatePickerData(
                currentDate = actualDate,
                displayTitle = displayTitle,
                canNavigateForward = hasNext.value,
                canNavigateBack = hasPrevious.value,
                trainingDateRange = TrainingDateRange.CustomRange(displayTitle)
            )
        }
        
        val (mainTimeGranularities, extraTimeGranularities) = getDefaultTimeGranularities()
        val placeholderHealthItems = createPlaceholderHealthItems(currentTimeGranularity, timeRange)
        
        _viewData.value = DailyHealthViewData.Loaded(
            mainTimeGranularities = mainTimeGranularities,
            extraTimeGranularities = extraTimeGranularities,
            currentTimeGranularity = currentTimeGranularity,
            showExtraTimeGranularitySelection = false,
            healthItems = placeholderHealthItems,
            datePickerData = datePickerData,
            isLoading = true,
        )
        
        val healthItems = createHealthItemsUseCase(
            chartGranularity = currentTimeGranularity,
            from = timeRange.start,
            to = timeRange.endInclusive
        ).first()
        
        _viewData.update { current ->
            if (current is DailyHealthViewData.Loaded) {
                current.copy(healthItems = healthItems, isLoading = false)
            } else current
        }
    }

    private fun createPlaceholderHealthItems(
        timeGranularity: ChartGranularity, 
        timeRange: ClosedRange<LocalDate>
    ): ImmutableList<HealthItem> {
        return persistentListOf(
            createPlaceholderHealthItem(ChartType.HEART_RATE, timeGranularity, ChartContent.HEART_RATE, timeRange.start, timeRange.endInclusive),
            createPlaceholderHealthItem(ChartType.STEPS, timeGranularity, ChartContent.STEPS, timeRange.start, timeRange.endInclusive),
            createPlaceholderHealthItem(ChartType.CALORIES, timeGranularity, ChartContent.CALORIES, timeRange.start, timeRange.endInclusive)
        )
    }

    private fun createPlaceholderHealthItem(
        chartType: ChartType, 
        timeGranularity: ChartGranularity,
        chartContent: ChartContent,
        from: LocalDate,
        to: LocalDate
    ): HealthItem {
        val label = appContext.getString(chartContent.valueTypeRes(timeGranularity))
        val timeInfo = createTimeInfoUseCase(timeGranularity, from, to)
        
        return when (chartType) {
            ChartType.STEPS -> HealthItem(
                chartType = chartType,
                iconRes = DiaryR.drawable.ic_steps_fill,
                titleRes = BaseR.string.dashboard_widget_steps_name,
                valueInfo = ValueInfo(
                    label = label,
                    value = AnnotatedString("--"),
                    timeInfo = timeInfo
                ),
                chartData = null,
                hasInfoButton = false
            )
            ChartType.CALORIES -> HealthItem(
                chartType = chartType,
                iconRes = DiaryR.drawable.ic_calories_fill,
                titleRes = BaseR.string.dashboard_widget_calories_name,
                valueInfo = ValueInfo(
                    label = label,
                    value = AnnotatedString("--"),
                    timeInfo = timeInfo
                ),
                chartData = null,
                hasInfoButton = true
            )
            ChartType.HEART_RATE -> HealthItem(
                chartType = chartType,
                iconRes = DiaryR.drawable.ic_heart_fill,
                titleRes = DiaryR.string.training_hub_intensity_type_heart_rate,
                valueInfo = ValueInfo(
                    label = label,
                    value = AnnotatedString("--"),
                    timeInfo = timeInfo
                ),
                chartData = null,
                hasInfoButton = true
            )
        }
    }

    fun onEvent(event: DailyHealthEvent) {
        when (event) {
            is DailyHealthEvent.UpdateTimeGranularity -> {
                updateTimeGranularity(event.timeGranularity)
            }
            is DailyHealthEvent.ShowExtraTimeGranularitySelection -> {
                showExtraTimeGranularitySelection()
            }
            is DailyHealthEvent.HideExtraTimeGranularitySelection -> {
                hideExtraTimeGranularitySelection()
            }
            DailyHealthEvent.HideHighlight -> hideHighlight()
            is DailyHealthEvent.ShowHighlight -> showHighlight(event)
            DailyHealthEvent.NavigateNext -> navigateNext()
            DailyHealthEvent.NavigatePrevious -> navigatePrevious()
            DailyHealthEvent.BackToCurrent -> backToCurrent()
        }
    }

    private fun updateTimeGranularity(granularity: ChartGranularity) {
        savedStateHandle[KEY_CHART_GRANULARITY] = granularity
        _currentDate.value = LocalDate.now()
        _pageNumber.value = 0
    }

    private fun showExtraTimeGranularitySelection() {
        _viewData.update { current ->
            if (current is DailyHealthViewData.Loaded) {
                current.copy(showExtraTimeGranularitySelection = true)
            } else current
        }
    }

    private fun hideExtraTimeGranularitySelection() {
        _viewData.update { current ->
            if (current is DailyHealthViewData.Loaded) {
                current.copy(showExtraTimeGranularitySelection = false)
            } else current
        }
    }

    private fun showHighlight(event: DailyHealthEvent.ShowHighlight) {
        showHighlightJob?.cancel()
        showHighlightJob = viewModelScope.launch {
            val currentData = _viewData.value as? DailyHealthViewData.Loaded ?: return@launch
            val targetHealthItem = currentData.healthItems.find { it.chartType == event.chartType } ?: return@launch
            
            val highlightData = createChartHighlightUseCase(
                healthItem = targetHealthItem,
                chartGranularity = currentData.currentTimeGranularity,
                entryX = event.entryX
            )
            
            val updatedHealthItems = currentData.healthItems.map { healthItem ->
                if (healthItem.chartType == event.chartType) {
                    healthItem.copy(chartHighlight = highlightData)
                } else {
                    healthItem
                }
            }.toImmutableList()
            
            _viewData.value = currentData.copy(
                healthItems = updatedHealthItems
            )
        }
    }

    private fun hideHighlight() {
        showHighlightJob?.cancel()
        
        val currentData = _viewData.value as? DailyHealthViewData.Loaded ?: return
        val updatedHealthItems = currentData.healthItems.map { healthItem ->
            healthItem.copy(chartHighlight = ChartHighlightViewData.None)
        }.toImmutableList()
        
        _viewData.value = currentData.copy(
            healthItems = updatedHealthItems
        )
    }

    private fun navigateNext() {
        when (savedStateHandle.chartGranularity) {
            ChartGranularity.DAILY -> {
                _currentDate.update { currentDate ->
                    val newDate = currentDate.plusDays(1)
                    if (newDate.isAfter(LocalDate.now())) LocalDate.now() else newDate
                }
            }
            else -> {
                _pageNumber.update { (it + 1).coerceAtMost(0) }
            }
        }
    }

    private fun navigatePrevious() {
        when (savedStateHandle.chartGranularity) {
            ChartGranularity.DAILY -> {
                _currentDate.update { it.minusDays(1) }
            }
            else -> {
                _pageNumber.update { it - 1 }
            }
        }
    }

    private fun backToCurrent() {
        when (savedStateHandle.chartGranularity) {
            ChartGranularity.DAILY -> {
                _currentDate.value = LocalDate.now()
            }
            else -> {
                _pageNumber.value = 0
            }
        }
    }

    private companion object {
        private const val KEY_CHART_GRANULARITY = "daily_health_chart_granularity"
        
        val SavedStateHandle.chartGranularity: ChartGranularity get() =
            get<ChartGranularity>(KEY_CHART_GRANULARITY) ?: ChartGranularity.DAILY

        fun getDefaultTimeGranularities(): Pair<ImmutableList<ChartGranularity>, ImmutableList<ChartGranularity>> {
            val mainTimeGranularities = persistentListOf(
                ChartGranularity.DAILY,
                ChartGranularity.WEEKLY,
                ChartGranularity.MONTHLY,
                ChartGranularity.YEARLY,
            )
            val extraTimeGranularities = persistentListOf(
                ChartGranularity.SEVEN_DAYS,
                ChartGranularity.THIRTY_DAYS,
                ChartGranularity.SIX_WEEKS,
                ChartGranularity.SIX_MONTHS,
                ChartGranularity.EIGHT_YEARS,
            )
            return Pair(mainTimeGranularities, extraTimeGranularities)
        }
    }
} 
