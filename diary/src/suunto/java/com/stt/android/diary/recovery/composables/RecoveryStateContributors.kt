package com.stt.android.diary.recovery.composables

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.GenericStackedProgress
import com.stt.android.compose.widgets.StackedProgressItem
import com.stt.android.data.TimeUtils
import com.stt.android.diary.recovery.model.getColorRes
import com.stt.android.diary.recovery.model.getEmptyContentRes
import com.stt.android.diary.recovery.model.getHeaderRes
import com.stt.android.diary.recovery.model.getIconRes
import com.stt.android.diary.recovery.v2.ContributorType
import com.stt.android.diary.recovery.v2.TimeLabels
import com.stt.android.domain.diary.models.RecoveryStateContributor
import com.stt.android.domain.diary.models.RecoveryStateContributors
import com.stt.android.home.diary.InfoBottomSheet
import com.stt.android.home.diary.R
import kotlin.math.max
import kotlin.math.min
import com.stt.android.R as BaseR
import com.stt.android.chart.impl.R as ChartR
import com.stt.android.core.R as CR

private data class ContributorItem(
    val contributor: RecoveryStateContributor?,
    val type: ContributorType,
    val defaultOrder: Int,
    val onClick: (() -> Unit)?,
    val onInfoClick: (() -> Unit)?
)

fun LazyListScope.recoveryStateContributorsItems(
    contributors: RecoveryStateContributors,
    timeLabels: TimeLabels,
    onInfoClick: (InfoBottomSheet) -> Unit,
    onContributorClick: (ContributorType) -> Unit,
) {
    item {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .padding(
                    start = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.xsmall,
                ),
        ) {
            Text(
                text = stringResource(R.string.recovery_state_contributors),
                style = MaterialTheme.typography.bodyXLargeBold,
                modifier = Modifier
                    .weight(1f)
                    .padding(vertical = MaterialTheme.spacing.medium),
            )
        }
    }

    val contributorItems = buildList {
        add(ContributorItem(
            contributor = contributors.sleepDuration,
            type = ContributorType.SLEEP,
            defaultOrder = 0,
            onClick = { onContributorClick(ContributorType.SLEEP) },
            onInfoClick = null
        ))
        add(ContributorItem(
            contributor = contributors.hrv,
            type = ContributorType.HRV,
            defaultOrder = 1,
            onClick = null,
            onInfoClick = { onInfoClick(InfoBottomSheet.RECOVERY_INFO_HRV) }
        ))
        // current not need show heart reate
//        if (contributors.restHr != null) {
//            add(ContributorItem(
//                contributor = contributors.restHr,
//                type = ContributorType.REST_HR,
//                defaultOrder = 2,
//                onClick = null,
//                onInfoClick = { onInfoClick(InfoBottomSheet.RECOVERY_INFO_RHR) }
//            ))
//        } else {
//            add(ContributorItem(
//                contributor = contributors.minHr,
//                type = ContributorType.MIN_HR,
//                defaultOrder = 2,
//                onClick = null,
//                onInfoClick = { onInfoClick(InfoBottomSheet.RECOVERY_INFO_MIN_HR) }
//            ))
//        }
        add(ContributorItem(
            contributor = contributors.trainingFatigue,
            type = ContributorType.TRAINING_FATIGUE,
            defaultOrder = 3,
            onClick = null,
            onInfoClick = { onInfoClick(InfoBottomSheet.RECOVERY_INFO_TRAINING_FATIGUE) }
        ))
        
        contributors.resources?.let { resources ->
            add(ContributorItem(
                contributor = resources,
                type = ContributorType.RESOURCES,
                defaultOrder = 4,
                onClick = { onContributorClick(ContributorType.RESOURCES) },
                onInfoClick = null
            ))
        }
    }

    val sortedItems = contributorItems.sortedWith(compareBy<ContributorItem> {
        if (it.contributor != null) 0 else 1 
    }.thenBy { it.defaultOrder })

    sortedItems.forEach { contributorItem ->
        item {
            RecoveryStateContributor(
                contributor = contributorItem.contributor,
                type = contributorItem.type,
                timeLabels = timeLabels,
                onClick = contributorItem.onClick,
                onInfoClick = contributorItem.onInfoClick
            )
        }
    }
}



@Composable
private fun RecoveryStateContributor(
    contributor: RecoveryStateContributor?,
    type: ContributorType,
    timeLabels: TimeLabels,
    modifier: Modifier = Modifier,
    onClick: (() -> Unit)? = null,
    onInfoClick: (() -> Unit)? = null,
) {
    if (contributor == null) {
        EmptyRecoveryStateContributor(type = type,
            onInfoClick = onInfoClick,
            onClick= onClick,
            modifier = modifier)
    } else {
        CommonRecoveryStateContributor(
            headerRes = type.getHeaderRes(),
            iconRes = type.getIconRes(),
            colorRes = type.getColorRes(),
            onClick = onClick,
            onInfoClick = onInfoClick,
            modifier = modifier,
        ) {
            RecoveryStateContributorContent(contributor,timeLabels)
        }
    }
}

@Composable
private fun EmptyRecoveryStateContributor(
    type: ContributorType,
    onInfoClick: (() -> Unit)?,
    onClick: (() -> Unit)?,
    modifier: Modifier = Modifier,
) {
    val headerRes = type.getHeaderRes()
    val iconRes = type.getIconRes()
    val colorRes = type.getColorRes()
    
    CommonRecoveryStateContributor(
        headerRes = headerRes,
        iconRes = iconRes,
        colorRes = colorRes,
        onClick = onClick,
        onInfoClick = onInfoClick,
        modifier = modifier,
    ) {
        Column(modifier = Modifier.fillMaxWidth().padding(vertical = MaterialTheme.spacing.smaller)) {
            val emptyTextRes = type.getEmptyContentRes()
            Text(
                text = if (emptyTextRes != null) stringResource(emptyTextRes) else "",
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.nearBlack,
            )
        }
    }
}

@Composable
private fun RecoveryStateContributorContent(
    contributor: RecoveryStateContributor,
    timeLabels: TimeLabels,
    modifier: Modifier = Modifier,
) {
    when (contributor) {
        is RecoveryStateContributor.SleepDuration -> SleepDurationContent(contributor, modifier)
        is RecoveryStateContributor.Hrv -> HrvContent(contributor, modifier)
        is RecoveryStateContributor.RestHr -> RestHrContent(contributor, timeLabels, modifier)
        is RecoveryStateContributor.MinHr -> MinHrContent(contributor, timeLabels, modifier)
        is RecoveryStateContributor.TrainingFatigue -> TrainingFatigueContent(contributor, timeLabels, modifier)
        is RecoveryStateContributor.Resources -> ResourcesContent(contributor, modifier)
    }
}

@Composable
private fun SleepDurationContent(
    contributor: RecoveryStateContributor.SleepDuration,
    modifier: Modifier = Modifier,
) {
    Row(
        horizontalArrangement = Arrangement.SpaceAround,
        modifier = modifier.fillMaxWidth(),
    ) {

        Column(modifier = Modifier.weight(1f)) {
            if (contributor.lastNight > 0) {
                Text(
                    text = formatSleepDuration(contributor.lastNightTotalSeconds),
                    style = MaterialTheme.typography.bodyLargeBold
                )
            } else {
                Text(
                    text = "--:-- ${stringResource(CR.string.hour)}",
                    style = MaterialTheme.typography.bodyLargeBold
                )
            }
            Text(
                text = stringResource(BaseR.string.widget_last_night),
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.secondary,
            )
        }
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = formatSleepDuration(contributor.avgTotalSeconds),
                style = MaterialTheme.typography.bodyLargeBold,
            )

            Text(
                text = stringResource(R.string.hrv_seven_day_avg_short),
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.secondary,
            )
        }
    }
}

@Composable
private fun formatSleepDuration(totalSeconds: Long): String {
    val hourUnit = stringResource(CR.string.hour)
    if (totalSeconds <= 0) return "--:-- $hourUnit"
    
    val timeString = TimeUtils.durationInSecondsToHoursAndMinutes(totalSeconds, roundMinutes = true)
    return "$timeString $hourUnit"
}

@Composable
private fun RestHrContent(
    contributor: RecoveryStateContributor.RestHr,
    timeLabels: TimeLabels,
    modifier: Modifier = Modifier,
) {
    val heartUnit = stringResource(BaseR.string.heart_unit)
    
    Row(
        horizontalArrangement = Arrangement.SpaceAround,
        modifier = modifier.fillMaxWidth(),
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = formatHeartRate(contributor.today, heartUnit),
                style = MaterialTheme.typography.bodyLargeBold
            )
            Text(
                text = "${stringResource(R.string.resting_hr)} ${timeLabels.currentLabel}",
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.secondary,
            )
        }
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = formatHeartRate(contributor.avg, heartUnit),
                style = MaterialTheme.typography.bodyLargeBold,
            )
            Text(
                text = stringResource(R.string.seven_day_avg_resting_hr),
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.secondary,
            )
        }
    }
}

@Composable
private fun MinHrContent(
    contributor: RecoveryStateContributor.MinHr,
    timeLabels: TimeLabels,
    modifier: Modifier = Modifier,
) {
    val heartUnit = stringResource(BaseR.string.heart_unit)
    
    Row(
        horizontalArrangement = Arrangement.SpaceAround,
        modifier = modifier.fillMaxWidth(),
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = formatHeartRate(contributor.today, heartUnit),
                style = MaterialTheme.typography.bodyLargeBold
            )
            Text(
                text = "${stringResource(R.string.min_hr)} ${timeLabels.currentLabel}",
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.secondary,
            )
        }
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = formatHeartRate(contributor.avg, heartUnit),
                style = MaterialTheme.typography.bodyLargeBold,
            )
            Text(
                text = stringResource(R.string.seven_day_avg_min_hr),
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.secondary,
            )
        }
    }
}

private fun formatHeartRate(value: Int, unit: String): String {
    return if (value > 0) "$value $unit" else "-- $unit"
}

private fun createTssProgressItems(
    value: Float,
    zone1Color: Color,
    zone2Color: Color,
    zone3Color: Color,
    zone4Color: Color,
    zone5Color: Color
): List<StackedProgressItem> {
    val inZone1 = value in 0f..50f
    val inZone2 = value in 50f..70f
    val inZone3 = value in 70f..90f
    val inZone4 = value in 90f..100f
    val inZone5 = value in 100f..150f
    
    return listOf(
        StackedProgressItem(0f, 50f, if (inZone1) zone1Color else zone1Color.copy(alpha = 0.15f)),
        StackedProgressItem(50f, 70f, if (inZone2) zone2Color else zone2Color.copy(alpha = 0.15f)),
        StackedProgressItem(70f, 90f, if (inZone3) zone3Color else zone3Color.copy(alpha = 0.15f)),
        StackedProgressItem(90f, 100f, if (inZone4) zone4Color else zone4Color.copy(alpha = 0.15f)),
        StackedProgressItem(100f, 150f, if (inZone5) zone5Color else zone5Color.copy(alpha = 0.15f))
    )
}

private fun createTsbProgressItems(
    value: Float,
    zone1Color: Color,
    zone2Color: Color,
    zone3Color: Color,
    zone4Color: Color
): List<StackedProgressItem> {
    val inZone1 = value < -30f
    val inZone2 = value in -30f..-9f
    val inZone3 = value in -10f..14f
    val inZone4 = value >= 15f
    
    return listOf(
        StackedProgressItem(-50f, -30f, if (inZone1) zone1Color else zone1Color.copy(alpha = 0.15f)),
        StackedProgressItem(-30f, -9f, if (inZone2) zone2Color else zone2Color.copy(alpha = 0.15f)),
        StackedProgressItem(-10f, 14f, if (inZone3) zone3Color else zone3Color.copy(alpha = 0.15f)),
        StackedProgressItem(15f, 35f, if (inZone4) zone4Color else zone4Color.copy(alpha = 0.15f))
    )
}

@Composable
private fun HrvContent(
    contributor: RecoveryStateContributor.Hrv,
    modifier: Modifier = Modifier,
) {
    Row(
        horizontalArrangement = Arrangement.SpaceAround,
        modifier = modifier.fillMaxWidth(),
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = if (contributor.lastNight > 0) "${contributor.lastNight}" else "--",
                style = MaterialTheme.typography.bodyLargeBold
            )
            Text(
                text = stringResource(BaseR.string.widget_last_night),
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.secondary,
            )
            
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmall))
            
            HrvProgressBar(
                value = contributor.lastNight.toFloat().coerceIn(0f, 300f),
                lowRange = contributor.lowRange,
                highRange = contributor.highRange,
                modifier = Modifier.fillMaxWidth(0.9f)
            )
        }
        
        Column(modifier = Modifier.weight(1f)) {
            val hasBaselineRange = contributor.lowRange > 0 && contributor.highRange > 0
            
            val displayText = if (hasBaselineRange) {
                val statusText = when {
                    contributor.avg > contributor.highRange -> stringResource(R.string.hrv_status_above)
                    contributor.avg < contributor.lowRange -> stringResource(R.string.hrv_status_below)
                    else -> stringResource(R.string.hrv_status_normal)
                }
                "${contributor.avg} / $statusText"
            } else {
                "${contributor.avg}"
            }
            
            Text(
                text = displayText,
                style = MaterialTheme.typography.bodyLargeBold
            )
            Text(
                text = stringResource(R.string.hrv_seven_day_avg_short),
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.secondary,
            )
            
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmall))
            
            HrvProgressBar(
                value = contributor.avg.toFloat().coerceIn(0f, 300f),
                lowRange = contributor.lowRange,
                highRange = contributor.highRange,
                modifier = Modifier.fillMaxWidth(0.9f)
            )
        }
    }
}

@Composable
private fun HrvProgressBar(
    value: Float,
    lowRange: Int,
    highRange: Int,
    modifier: Modifier = Modifier
) {
    val lowColor = colorResource(id = CR.color.diary_calendar_color)
    val mediumColor = colorResource(id = CR.color.st_outdoor_adventures)
    val highColor = colorResource(id = CR.color.suunto_running)
    val noBaseLineNormal = colorResource(id = BaseR.color.near_white)
    val noBaseLineRange = colorResource(id = BaseR.color.cloudy_grey)
    
    val hasBaselineRange = lowRange > 0 && highRange > 0
    var showTick = value > 0
    
    val items = remember(value, lowRange, highRange) {
        if (hasBaselineRange) {
            val baselineLength = (highRange - lowRange).toFloat()

            val minValue = lowRange - baselineLength
            val maxValue = highRange + baselineLength
            
            val finalMin = if (value > 0) min(minValue, value - baselineLength * 0.2f) else minValue
            val finalMax = if (value > 0) max(maxValue, value + baselineLength * 0.2f) else maxValue
            
            val showProgress = value > 0
            val isInLowRange = showProgress && value < lowRange.toFloat()
            val isInMediumRange = showProgress && value >= lowRange.toFloat() && value <= highRange.toFloat()
            val isInHighRange = showProgress && value > highRange.toFloat()
            
            listOf(
                StackedProgressItem(
                    finalMin, 
                    lowRange.toFloat(), 
                    if (isInLowRange) lowColor else lowColor.copy(alpha = 0.15f)
                ),
                StackedProgressItem(
                    lowRange.toFloat(), 
                    highRange.toFloat(), 
                    if (isInMediumRange) mediumColor else mediumColor.copy(alpha = 0.15f)
                ),
                StackedProgressItem(
                    highRange.toFloat(), 
                    finalMax, 
                    if (isInHighRange) highColor else highColor.copy(alpha = 0.15f)
                )
            )
        } else {
            showTick = false
            listOf(
                StackedProgressItem(0f, 30f, noBaseLineNormal),
                StackedProgressItem(30f, 60f, noBaseLineRange),
                StackedProgressItem(60f, 90f, noBaseLineNormal)
            )
        }
    }
    GenericStackedProgress(
        value = value,
        items = items,
        modifier = modifier,
        showMarkers = false,
        showTick = showTick
    )

}

@Composable
private fun getCtlPercentLabel(ctlPercent: Float): String {
    return if (ctlPercent >= 75f) {
        stringResource(R.string.gain)
    } else {
        stringResource(R.string.recovery)
    }
}

@Composable
private fun TrainingFatigueContent(
    contributor: RecoveryStateContributor.TrainingFatigue,
    timeLabels: TimeLabels,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            horizontalArrangement = Arrangement.SpaceAround,
            modifier = Modifier.fillMaxWidth(),
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "${contributor.todayTSS}",
                    style = MaterialTheme.typography.bodyLargeBold
                )
                Text(
                    text = "${stringResource(R.string.tss)} ${timeLabels.currentLabel}",
                    style = MaterialTheme.typography.body,
                    color = MaterialTheme.colorScheme.secondary,
                )
                
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmall))
                // current version need not show todayCtlPercent progress bar
//                val items = createTssProgressItems(
//                    value = contributor.ctlPercent,
//                    zone1Color = colorResource(id = R.color.recovery_tss_zone_1),
//                    zone2Color = colorResource(id = CR.color.suunto_diving),
//                    zone3Color = colorResource(id = CR.color.suunto_performance),
//                    zone4Color = colorResource(id = R.color.recovery_tss_zone_4),
//                    zone5Color = colorResource(id = CR.color.heart_rate_2)
//                )
//
//                GenericStackedProgress(
//                    value = contributor.ctlPercent.coerceIn(0f, 150f),
//                    items = items,
//                    modifier = Modifier.fillMaxWidth(0.9f),
//                    showMarkers = false
//                )
            }
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "${contributor.yesterdayTSS}",
                    style = MaterialTheme.typography.bodyLargeBold
                )
                Text(
                    text = "${stringResource(R.string.tss)} ${timeLabels.previousLabel}",
                    style = MaterialTheme.typography.body,
                    color = MaterialTheme.colorScheme.secondary,
                )
                
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmall))
                // current version need not show yesterdayCtlPercent progress bar

//                val items = createTssProgressItems(
//                    value = contributor.yesterdayCtlPercent,
//                    zone1Color = colorResource(id = R.color.recovery_tss_zone_1),
//                    zone2Color = colorResource(id = CR.color.suunto_diving),
//                    zone3Color = colorResource(id = CR.color.suunto_performance),
//                    zone4Color = colorResource(id = R.color.recovery_tss_zone_4),
//                    zone5Color = colorResource(id = CR.color.heart_rate_2)
//                )
//
//                GenericStackedProgress(
//                    value = contributor.yesterdayCtlPercent.coerceIn(0f, 150f),
//                    items = items,
//                    modifier = Modifier.fillMaxWidth(0.9f),
//                    showMarkers = false
//                )
            }
        }
        
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
        
        Row(
            horizontalArrangement = Arrangement.SpaceAround,
            modifier = Modifier.fillMaxWidth(),
        ) {
            Column(modifier = Modifier.weight(1f)) {
                val tsbRating = when {
                    contributor.todayTSB < -30 -> stringResource(R.string.tsb_rating_exhausted)
                    contributor.todayTSB < -10 -> stringResource(R.string.tsb_rating_fatigued_gaining_fitness)
                    contributor.todayTSB < 15 -> stringResource(R.string.tsb_rating_balanced)
                    else -> stringResource(R.string.tsb_rating_ready_for_more)
                }
                
                Text(
                    text = "${contributor.todayTSB} / $tsbRating",
                    style = MaterialTheme.typography.bodyLargeBold
                )
                Text(
                    text = stringResource(R.string.tsb),
                    style = MaterialTheme.typography.body,
                    color = MaterialTheme.colorScheme.secondary,
                )
                
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmall))
                
                val items = createTsbProgressItems(
                    value = contributor.todayTSB.toFloat(),
                    zone1Color = colorResource(id = CR.color.st_cycling),
                    zone2Color = colorResource(id = CR.color.suunto_running),
                    zone3Color = colorResource(id = CR.color.confirmation_color),
                    zone4Color = colorResource(id = CR.color.st_diving)
                )
                
                GenericStackedProgress(
                    value = contributor.todayTSB.toFloat().coerceIn(-40f, 25f),
                    items = items,
                    modifier = Modifier.fillMaxWidth(0.9f),
                    showMarkers = false
                )
            }
            
            Column(modifier = Modifier.weight(1f)) {
                val feelingText = when {
                    contributor.last7DaysFeeling >= 4.5 -> stringResource(R.string.feeling_excellent)
                    contributor.last7DaysFeeling >= 3.5 -> stringResource(R.string.feeling_good)
                    contributor.last7DaysFeeling >= 2.5 -> stringResource(R.string.feeling_normal)
                    contributor.last7DaysFeeling >= 1.5 -> stringResource(R.string.feeling_poor)
                    else -> stringResource(R.string.feeling_very_poor)
                }
                
                Text(
                    text = feelingText,
                    style = MaterialTheme.typography.bodyLargeBold
                )
                Text(
                    text = stringResource(R.string.seven_day_avg_feelings),
                    style = MaterialTheme.typography.body,
                    color = MaterialTheme.colorScheme.secondary,
                )
                
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmall))
                
                val feelingDistribution = contributor.feelingDistribution.ifEmpty {
                    (1..5).associateWith { 20.0 }
                }
                
                val colorList = listOf(
                    colorResource(id = CR.color.bright_red),
                    colorResource(id = CR.color.st_cycling),
                    colorResource(id = CR.color.suunto_running),
                    colorResource(id = BaseR.color.dashboard_widget_resources),
                    colorResource(id = CR.color.confirmation_color)
                )
                
                val feelingItems = mutableListOf<StackedProgressItem>()
                var startPosition = 0f
                
                (1..5).forEach { feeling ->
                    val percentage = feelingDistribution[feeling] ?: 0.0
                    val endPosition = startPosition + percentage.toFloat()
                    
                    if (percentage > 0) {
                        feelingItems.add(
                            StackedProgressItem(
                                min = startPosition, 
                                max = endPosition,
                                color = colorList[feeling - 1]
                            )
                        )
                    }
                    startPosition = endPosition
                }
                
                GenericStackedProgress(
                    value = 100f,
                    items = feelingItems,
                    modifier = Modifier.fillMaxWidth(0.9f),
                    showMarkers = false,
                    showTick = false
                )
            }
        }
    }
}

@Composable
private fun ResourcesContent(
    contributor: RecoveryStateContributor.Resources,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        val resourcesLevel = when {
            contributor.today >= 80 -> stringResource(ChartR.string.resources_level_very_high)
            contributor.today >= 51 -> stringResource(ChartR.string.resources_level_high)
            contributor.today >= 20 -> stringResource(ChartR.string.resources_level_moderate)
            else -> stringResource(ChartR.string.resources_level_low)
        }
        
        Text(
            text = "${contributor.today}% / $resourcesLevel",
            style = MaterialTheme.typography.bodyLargeBold
        )
        
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(10.dp)
                .clip(RoundedCornerShape(5.dp))
                .background(MaterialTheme.colorScheme.surfaceVariant)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth(contributor.today / 100f)
                    .height(10.dp)
                    .clip(RoundedCornerShape(5.dp))
                    .background(color = colorResource(R.color.resources_progress_color))
            )
        }
    }
}

@Preview
@Composable
private fun RecoveryStateContributorsPreview() {
    M3AppTheme {
        LazyColumn {
            recoveryStateContributorsItems(
                RecoveryStateContributors(
                    last1DaySleepDuration = 7.1,
                    last7DaysSleepDuration = 6.0,
                    last1DaySleepDurationTotalSeconds = (7.1 * 3600).toLong(),
                    last7DaysSleepDurationTotalSeconds = (6.0 * 3600).toLong(),
                    last1DayHrv = 41,
                    last7DaysHrv = 32,
                    last1DayRestHR = 46,
                    last7DaysRestHR = 55,
                    last1DayMinHR = 42,
                    last7DaysMinHR = 40,
                    yesterdayTSS = 43,
                    todayTSS = 34,
                    todayTSB = -2,
                    last7DaysFeeling = 3.6,
                    todayResources = 34,
                ),
                timeLabels = TimeLabels(
                    "t","t"
                ),
                {},
            ) {}
        }
    }
}

