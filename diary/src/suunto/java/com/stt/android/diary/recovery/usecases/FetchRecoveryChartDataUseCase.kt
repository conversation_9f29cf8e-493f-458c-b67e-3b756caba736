package com.stt.android.diary.recovery.usecases

import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.diary.recovery.data.HrvChartDataLoader
import com.stt.android.diary.recovery.data.RecoveryChartDataLoader
import com.stt.android.diary.recovery.data.ResourcesChartDataLoader
import com.stt.android.diary.recovery.data.SleepComparisonChartDataLoader
import com.stt.android.diary.recovery.v2.RecoveryChartContributors
import com.stt.android.diary.recovery.v2.RecoveryChartStateData
import com.stt.android.diary.recovery.v2.SleepChartSelectionType
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOn
import java.time.LocalDate
import javax.inject.Inject

class FetchRecoveryChartDataUseCase @Inject constructor(
    private val recoveryChartDataLoader: RecoveryChartDataLoader,
    private val sleepComparisonChartDataLoader: SleepComparisonChartDataLoader,
    private val hrvChartDataLoader: HrvChartDataLoader,
    private val resourcesChartDataLoader: ResourcesChartDataLoader,
    private val coroutinesDispatchers: CoroutinesDispatchers
) {
     operator fun invoke(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        leftSleepSelectionType: SleepChartSelectionType,
        rightSleepSelectionType: SleepChartSelectionType,
        displayTitle: String
    ): Flow<RecoveryChartStateData> {
        return combine(
            recoveryChartDataLoader.loadChartData(chartGranularity, from, to),
            sleepComparisonChartDataLoader.loadComparisonChartData(
                chartGranularity, 
                leftSleepSelectionType, 
                rightSleepSelectionType, 
                from, 
                to,
                displayTitle
            ),
            hrvChartDataLoader.loadChartContributor(chartGranularity, from, to),
            resourcesChartDataLoader.loadChartContributor(chartGranularity, from, to)
        ) { recoveryChartData, sleepComparisonChartData, hrvContributor, resourcesContributor ->
            
            val commonChartContributors = listOf(
                hrvContributor,
                resourcesContributor
            )
            
            val recoveryChartContributors = RecoveryChartContributors(
                sleepComparisonChartData = sleepComparisonChartData,
                commonChartContributors = commonChartContributors
            )
            
            RecoveryChartStateData.Loaded(
                recoveryChartData,
                recoveryChartContributors = recoveryChartContributors
            )
        }
            .flowOn(coroutinesDispatchers.io)
    }
}
