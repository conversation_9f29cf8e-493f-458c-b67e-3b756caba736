package com.stt.android.diary.dailyhealth.data

import android.content.Context
import androidx.annotation.ColorInt
import androidx.compose.ui.text.AnnotatedString
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.model.ChartBarDisplayMode
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.ChartType
import com.stt.android.chart.impl.model.epochMonth
import com.stt.android.chart.impl.model.valueTypeRes
import com.stt.android.data.toEpochMilli
import com.stt.android.diary.dailyhealth.ChartType as DailyHealthChartType
import com.stt.android.diary.dailyhealth.HealthItem
import com.stt.android.diary.dailyhealth.ValueInfo
import com.stt.android.diary.dailyhealth.usecase.CreateTimeInfoUseCase
import com.stt.android.diary.recovery.data.minutesSinceEpoch
import com.stt.android.domain.activitydata.dailyvalues.ActivityDataDailyRepository
import com.stt.android.domain.activitydata.goals.ActivityDataGoalRepository
import com.stt.android.domain.trenddata.TrendData
import com.stt.android.domain.trenddata.TrendDataRepository
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.utils.atEndOfDay
import com.stt.android.utils.toEpochMilli
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.first
import java.time.LocalDate
import java.time.ZoneId
import java.time.temporal.TemporalAdjusters
import javax.inject.Inject
import kotlin.math.ceil
import kotlin.math.max
import kotlin.math.roundToInt
import kotlin.time.Duration.Companion.minutes
import com.stt.android.R as BaseR
import com.stt.android.home.diary.R as DiaryR
import com.stt.android.controllers.UserSettingsController

class StepsChartDataLoader @Inject constructor(
    @ApplicationContext private val appContext: Context,
    private val trendDataRepository: TrendDataRepository,
    private val activityDataDailyRepository: ActivityDataDailyRepository,
    private val activityDataGoalRepository: ActivityDataGoalRepository,
    private val infoModelFormatter: InfoModelFormatter,
    private val createTimeInfoUseCase: CreateTimeInfoUseCase,
    private val userSettingsController: UserSettingsController,
) {

    fun formatHighlightData(y: Int?): String =
        appContext.getString(BaseR.string.daily_goal_setting_value_1_steps, y ?: 0)

    suspend fun loadDailyHealthItem(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate
    ): HealthItem {
        val trendDataList = loadTrendData(chartGranularity, from, to)
        
        val todaySteps = activityDataDailyRepository.fetchSteps().first()
        val stepsGoal = activityDataGoalRepository.fetchStepsGoal().first()
        
        val chartColor = getChartColor()
        
        val chartSeries = createChartSeries(
            chartGranularity = chartGranularity,
            from = from,
            to = to,
            trendDataList = trendDataList,
            chartColor = chartColor,
            todaySteps = todaySteps.toFloat(),
            stepsGoal = stepsGoal
        )

        val goal = stepsGoal.takeIf {
            chartGranularity in listOf(
                ChartGranularity.WEEKLY,
                ChartGranularity.SEVEN_DAYS,
                ChartGranularity.THIRTY_DAYS,
                ChartGranularity.MONTHLY
            )
        }

        val chartData = ChartData(
            chartContent = ChartContent.STEPS,
            chartGranularity = chartGranularity,
            series = persistentListOf(chartSeries),
            highlightEnabled = true,
            goal = goal,
            highlightDecorationLines = persistentMapOf(),
            currentValues = persistentListOf(),
            chartBarDisplayMode = ChartBarDisplayMode.STACKED,
            colorIndicator = null,
        )
        
        val valueInfo = ValueInfo(
            label = appContext.getString(ChartContent.STEPS.valueTypeRes(chartGranularity)),
            value = chartSeries.value,
            timeInfo = createTimeInfoUseCase(chartGranularity, from, to)
        )
        
        return HealthItem(
            chartType = DailyHealthChartType.STEPS,
            iconRes = DiaryR.drawable.ic_steps_fill,
            titleRes = BaseR.string.dashboard_widget_steps_name,
            valueInfo = valueInfo,
            chartData = chartData,
            hasInfoButton = false
        )
    }

    private interface StepsSeriesStrategy {
        fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            color: Int,
            todaySteps: Float,
            stepsGoal: Int,
        ): ChartData.Series
    }

    private val seriesStrategies: Map<ChartGranularity, StepsSeriesStrategy> = mapOf(
        ChartGranularity.DAILY to DailyStepsSeriesStrategy(),
        ChartGranularity.WEEKLY to DayByDayStepsSeriesStrategy(),
        ChartGranularity.MONTHLY to DayByDayStepsSeriesStrategy(),
        ChartGranularity.YEARLY to MonthlyStepsSeriesStrategy(),
        ChartGranularity.SEVEN_DAYS to DayByDayStepsSeriesStrategy(),
        ChartGranularity.THIRTY_DAYS to DayByDayStepsSeriesStrategy(),
        ChartGranularity.SIX_WEEKS to DayByDayStepsSeriesStrategy(),
        ChartGranularity.SIX_MONTHS to WeeklyStepsSeriesStrategy(),
        ChartGranularity.EIGHT_YEARS to YearlyStepsSeriesStrategy(),
    )


    @ColorInt
    private fun getChartColor(): Int =
        appContext.getColor(BaseR.color.activity_data_steps)

    private fun createChartSeries(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        trendDataList: List<TrendData>,
        chartColor: Int,
        todaySteps: Float,
        stepsGoal: Int,
    ): ChartData.Series {
        val validStepsData = trendDataList.filter { it.steps > 0 }
        
        if (validStepsData.isEmpty() && todaySteps <= 0) {
            return createEmptyChartSeries(chartGranularity, from, to, chartColor, stepsGoal)
        }
        
        return seriesStrategies[chartGranularity]
            ?.createSeries(from, to, trendDataList, chartColor, todaySteps, stepsGoal)
            ?: throw IllegalArgumentException("Unsupported granularity: $chartGranularity")
    }

    private fun createEmptyChartSeries(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        chartColor: Int,
        stepsGoal: Int = 0,
    ): ChartData.Series {
        val (minX, maxX) = getAxisRangeForGranularity(chartGranularity, from, to)

        val maxY = if (stepsGoal > 0) {
            adjustYAxisRange(stepsGoal.toFloat())
        } else {
            900.0F
        }

        return ChartData.Series(
            chartType = ChartType.BAR,
            color = chartColor,
            axisRange = ChartData.AxisRange(
                minX = minX,
                maxX = maxX,
                minY = 0.0,
                maxY = maxY.toDouble(),
            ),
            entries = persistentListOf(),
            value = getFormattedStepsValue(0F),
            candlestickEntries = persistentListOf(),
            lineConfig = null,
            backgroundRegion = null,
            groupStackBarStyle = null,
        )
    }

    private fun getAxisRangeForGranularity(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate
    ): Pair<Double, Double> {
        return when (chartGranularity) {
            ChartGranularity.DAILY -> {
                val minX = from.atStartOfDay().minutesSinceEpoch
                val maxX = to.atEndOfDay().minutesSinceEpoch - 10L
                minX.toDouble() to maxX.toDouble()
            }
            ChartGranularity.YEARLY -> {
                from.withDayOfMonth(1).epochMonth.toDouble() to to.withDayOfMonth(1).epochMonth.toDouble()
            }
            ChartGranularity.MONTHLY -> {
                from.toEpochDay().toDouble() to to.toEpochDay().toDouble()
            }
            ChartGranularity.EIGHT_YEARS -> {
                from.year.toDouble() to to.year.toDouble()
            }
            ChartGranularity.SIX_MONTHS -> {
                val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
                val adjustedFrom = from.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
                adjustedFrom.toEpochDay().toDouble() to to.toEpochDay().toDouble()
            }
            else -> {
                from.toEpochDay().toDouble() to to.toEpochDay().toDouble()
            }
        }
    }

    private fun adjustYAxisRange(maxY: Float): Float {
        val factor = when {
            maxY < 150.0F -> 15.0F
            maxY < 300.0F -> 30.0F
            maxY < 6000.0F -> 300.0F
            maxY < 90000.0F -> 3000.0F
            else -> 30000.0F
        }
       return max(ceil(maxY / factor) * factor, 30.0F)
    }

    private inner class DailyStepsSeriesStrategy : StepsSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            color: Int,
            todaySteps: Float,
            stepsGoal: Int,
        ): ChartData.Series {
            val entries = mutableListOf<ChartData.Entry>()
            val allSteps = mutableListOf<Int>()
            var totalDataPoints = 0
            
            var maxY = 0.0F

            val timeWindowMap = groupTrendDataByMinutes(
                from = from,
                to = to,
                trendDataList = trendDataList,
                intervalMillis = 10.minutes.inWholeMilliseconds,
            )

            timeWindowMap.entries.sortedBy { it.key }.forEach { (_, trendsInWindow) ->
                val stepsInWindow = trendsInWindow.mapNotNull { trendData ->
                    trendData.steps.takeIf { it > 0 }
                }

                if (stepsInWindow.isNotEmpty()) {
                    val avgSteps = stepsInWindow.average().roundToInt()
                    
                    allSteps.addAll(stepsInWindow)
                    totalDataPoints++
                    
                    maxY = max(maxY, avgSteps.toFloat())
                    
                    val firstTrend = trendsInWindow.minByOrNull { it.timestamp } ?: trendsInWindow.first()
                    val x = firstTrend.timeISO8601.minutesSinceEpoch
                    
                    entries.add(ChartData.Entry(x = x, y = avgSteps))
                }
            }
            
            val minX = from.atStartOfDay().minutesSinceEpoch
            val maxX = to.atEndOfDay().minutesSinceEpoch - 10L
            
            val adjustedMaxY = adjustYAxisRange(maxY)

            var totalStepsValue = allSteps.sum().toFloat()
            
            // For today's data, if not all trend data has been synced, we need to rely on
            // steps from ActivityDataDailyRepository.
            if (from == LocalDate.now()) {
                totalStepsValue = max(totalStepsValue, todaySteps)
            }

            return ChartData.Series(
                chartType = ChartType.BAR,
                color = color,
                axisRange = ChartData.AxisRange(
                    minX = minX.toDouble(),
                    maxX = maxX.toDouble(),
                    minY = 0.0,
                    maxY = adjustedMaxY.toDouble(),
                ),
                entries = entries.toImmutableList(),
                value = getFormattedStepsValue(totalStepsValue),
                candlestickEntries = persistentListOf(),
                lineConfig = null,
                backgroundRegion = null,
                groupStackBarStyle = null,
            )
        }
    }

    private inner class DayByDayStepsSeriesStrategy : StepsSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            color: Int,
            todaySteps: Float,
            stepsGoal: Int,
        ): ChartData.Series {
            val entries = mutableListOf<ChartData.Entry>()
            val allSteps = mutableListOf<Int>()
            var totalValue = 0F
            var totalDays = 0
            
            var maxY = stepsGoal.toFloat()
            
            val trendDataByDay = trendDataList.groupBy { it.timeISO8601.toLocalDate() }
            
            var currentDate = from
            while (currentDate <= to) {
                val dailyTrends = trendDataByDay[currentDate] ?: emptyList()
                val dailySteps = dailyTrends.sumOf { it.steps }
                
                val actualSteps = if (currentDate == LocalDate.now()) {
                    max(dailySteps, todaySteps.toInt())
                } else {
                    dailySteps
                }
                
                val x = currentDate.toEpochDay()
                
                if (actualSteps > 0) {
                    allSteps.add(actualSteps)
                    totalValue += actualSteps
                    totalDays++
                    
                    maxY = max(maxY, actualSteps.toFloat())
                    
                    entries.add(ChartData.Entry(x = x, y = actualSteps))
                }
                
                currentDate = currentDate.plusDays(1)
            }
            
            val adjustedMaxY = adjustYAxisRange(maxY)
            val averageValue = if (totalDays == 0) 0F else totalValue / totalDays

            return ChartData.Series(
                chartType = ChartType.BAR,
                color = color,
                axisRange = ChartData.AxisRange(
                    minX = from.toEpochDay().toDouble(),
                    maxX = to.toEpochDay().toDouble(),
                    minY = 0.0,
                    maxY = adjustedMaxY.toDouble(),
                ),
                entries = entries.toImmutableList(),
                value = getFormattedStepsValue(averageValue),
                candlestickEntries = persistentListOf(),
                lineConfig = null,
                backgroundRegion = null,
                groupStackBarStyle = null,
            )
        }
    }

    private inner class WeeklyStepsSeriesStrategy : StepsSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            color: Int,
            todaySteps: Float,
            stepsGoal: Int,
        ): ChartData.Series {
            val entries = mutableListOf<ChartData.Entry>()
            val allSteps = mutableListOf<Int>()
            var totalSteps = 0
            var totalDays = 0
            
            var maxY = stepsGoal.toFloat()
            
            val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
            val adjustedFrom = from.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
            
            val groupedData = trendDataList.groupBy { trendData ->
                trendData.timeISO8601
                    .toLocalDate()
                    .with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
                    .toEpochDay()
            }
            
            groupedData.forEach { (x, weekTrendDataList) ->
                var allStepsInWeek = 0
                var daysWithData = 0
                
                weekTrendDataList.forEach { trendData ->
                    val dailySteps = trendData.steps
                    val actualSteps = if (trendData.timeISO8601.toLocalDate() == LocalDate.now()) {
                        max(dailySteps, todaySteps.toInt())
                    } else {
                        dailySteps
                    }
                    
                    if (actualSteps > 0) {
                        allStepsInWeek += actualSteps
                        daysWithData++
                        totalSteps += actualSteps
                        totalDays++
                    }
                }
                
                if (allStepsInWeek > 0 && daysWithData > 0) {
                    val weeklyAverage = allStepsInWeek.toFloat() / daysWithData
                    allSteps.add(weeklyAverage.toInt())
                    
                    maxY = max(maxY, weeklyAverage)
                    
                    entries.add(ChartData.Entry(x = x, y = weeklyAverage.toInt()))
                }
            }
            
            val adjustedMaxY = adjustYAxisRange(maxY)
            val averageValue = if (totalDays == 0) 0F else totalSteps.toFloat() / totalDays

            return ChartData.Series(
                chartType = ChartType.BAR,
                color = color,
                axisRange = ChartData.AxisRange(
                    minX = adjustedFrom.toEpochDay().toDouble(),
                    maxX = to.toEpochDay().toDouble(),
                    minY = 0.0,
                    maxY = adjustedMaxY.toDouble(),
                ),
                entries = entries.toImmutableList(),
                value = getFormattedStepsValue(averageValue),
                candlestickEntries = persistentListOf(),
                lineConfig = null,
                backgroundRegion = null,
                groupStackBarStyle = null,
            )
        }
    }

    private inner class MonthlyStepsSeriesStrategy : StepsSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            color: Int,
            todaySteps: Float,
            stepsGoal: Int,
        ): ChartData.Series {
            val entries = mutableListOf<ChartData.Entry>()
            val allSteps = mutableListOf<Int>()
            var totalSteps = 0
            var totalDays = 0
            
            var maxY = stepsGoal.toFloat()
            
            val trendDataByDay = trendDataList.groupBy { it.timeISO8601.toLocalDate() }
            
            var currentMonth = from.withDayOfMonth(1)
            val endMonth = to.withDayOfMonth(1)
            
            while (currentMonth <= endMonth) {
                val lastDayOfMonth = currentMonth.plusMonths(1).minusDays(1).coerceAtMost(to)
                var allStepsInMonth = 0
                var daysWithData = 0
                
                var currentDay = currentMonth
                while (currentDay <= lastDayOfMonth) {
                    val dailySteps = trendDataByDay[currentDay]?.sumOf { it.steps } ?: 0
                    val actualSteps = if (currentDay == LocalDate.now()) {
                        max(dailySteps, todaySteps.toInt())
                    } else {
                        dailySteps
                    }
                    
                    if (actualSteps > 0) {
                        allStepsInMonth += actualSteps
                        daysWithData++
                        totalSteps += actualSteps
                        totalDays++
                    }
                    currentDay = currentDay.plusDays(1)
                }
                
                val x = currentMonth.epochMonth.toLong()
                
                if (allStepsInMonth > 0 && daysWithData > 0) {
                    val monthlyAverage = allStepsInMonth.toFloat() / daysWithData
                    allSteps.add(monthlyAverage.toInt())
                    
                    maxY = max(maxY, monthlyAverage)
                    
                    entries.add(ChartData.Entry(x = x, y = monthlyAverage.toInt()))
                }
                
                currentMonth = currentMonth.plusMonths(1)
            }
            
            val adjustedMaxY = adjustYAxisRange(maxY)
            val averageValue = if (totalDays == 0) 0F else totalSteps.toFloat() / totalDays

            return ChartData.Series(
                chartType = ChartType.BAR,
                color = color,
                axisRange = ChartData.AxisRange(
                    minX = from.withDayOfMonth(1).epochMonth.toDouble(),
                    maxX = to.withDayOfMonth(1).epochMonth.toDouble(),
                    minY = 0.0,
                    maxY = adjustedMaxY.toDouble(),
                ),
                entries = entries.toImmutableList(),
                value = getFormattedStepsValue(averageValue),
                candlestickEntries = persistentListOf(),
                lineConfig = null,
                backgroundRegion = null,
                groupStackBarStyle = null,
            )
        }
    }

    private inner class YearlyStepsSeriesStrategy : StepsSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            color: Int,
            todaySteps: Float,
            stepsGoal: Int,
        ): ChartData.Series {
            val entries = mutableListOf<ChartData.Entry>()
            val allSteps = mutableListOf<Int>()
            var totalSteps = 0
            var totalDays = 0

            var maxY = stepsGoal.toFloat()
            
            val trendDataByDay = trendDataList.groupBy { it.timeISO8601.toLocalDate() }
            
            var currentYear = from.withDayOfYear(1)
            val endYear = to.withDayOfYear(1)
            
            while (currentYear.year <= endYear.year) {
                val lastDayOfYear = currentYear.withDayOfYear(currentYear.lengthOfYear()).coerceAtMost(to)
                var allStepsInYear = 0
                var daysWithData = 0
                
                var currentDay = if (currentYear.year == from.year) from else currentYear
                while (currentDay <= lastDayOfYear) {
                    val dailySteps = trendDataByDay[currentDay]?.sumOf { it.steps } ?: 0
                    val actualSteps = if (currentDay == LocalDate.now()) {
                        max(dailySteps, todaySteps.toInt())
                    } else {
                        dailySteps
                    }
                    
                    if (actualSteps > 0) {
                        allStepsInYear += actualSteps
                        daysWithData++
                        totalSteps += actualSteps
                        totalDays++
                    }
                    currentDay = currentDay.plusDays(1)
                }
                
                val x = currentYear.year.toLong()
                
                if (allStepsInYear > 0 && daysWithData > 0) {
                    val yearlyAverage = allStepsInYear.toFloat() / daysWithData
                    allSteps.add(yearlyAverage.toInt())
                    
                    maxY = max(maxY, yearlyAverage)
                    
                    entries.add(ChartData.Entry(x = x, y = yearlyAverage.toInt()))
                }
                
                currentYear = currentYear.plusYears(1)
            }
            
            val adjustedMaxY = adjustYAxisRange(maxY)
            val averageValue = if (totalDays == 0) 0F else totalSteps.toFloat() / totalDays

            return ChartData.Series(
                chartType = ChartType.BAR,
                color = color,
                axisRange = ChartData.AxisRange(
                    minX = from.year.toDouble(),
                    maxX = to.year.toDouble(),
                    minY = 0.0,
                    maxY = adjustedMaxY.toDouble(),
                ),
                entries = entries.toImmutableList(),
                value = getFormattedStepsValue(averageValue),
                candlestickEntries = persistentListOf(),
                lineConfig = null,
                backgroundRegion = null,
                groupStackBarStyle = null,
            )
        }
    }

    private fun groupTrendDataByMinutes(
        from: LocalDate,
        to: LocalDate,
        trendDataList: List<TrendData>,
        intervalMillis: Long,
    ): Map<Long, List<TrendData>> {
        val fromMillis = from.atStartOfDay(ZoneId.systemDefault()).toEpochMilli()
        val toMillis = to.atEndOfDay().toEpochMilli()
        val timeRange = fromMillis until toMillis

        val result = mutableMapOf<Long, MutableList<TrendData>>()
        trendDataList.forEach { trendData ->
            val timestamp = trendData.timeISO8601.toInstant().toEpochMilli()
            if (timestamp in timeRange) {
                val windowKey = (timestamp - fromMillis) / intervalMillis * intervalMillis + fromMillis
                result.getOrPut(windowKey) { mutableListOf() }.add(trendData)
            }
        }
        return result
    }

    private suspend fun loadTrendData(
        chartGranularity: ChartGranularity,
        from: LocalDate, 
        to: LocalDate
    ): List<TrendData> {
        val fromMillis = from.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
        val toMillis = to.plusDays(1).atStartOfDay(ZoneId.systemDefault()).minusNanos(1).toInstant().toEpochMilli()
        
        return trendDataRepository.fetchTrendDataForDateRange(
            fromTimestamp = fromMillis,
            toTimestamp = toMillis,
            aggregated = chartGranularity != ChartGranularity.DAILY
        ).catch { emit(emptyList()) }.first()
    }

    private fun getFormattedStepsValue(value: Float): AnnotatedString {
        if (value==0F){
            return AnnotatedString(appContext.getString(BaseR.string.widget_no_data_title))
        }
        return infoModelFormatter.formatValue(SummaryItem.STEPS, value).value?.let { 
            AnnotatedString(it) 
        } ?: AnnotatedString(appContext.getString(BaseR.string.widget_no_data_title))
    }
} 
