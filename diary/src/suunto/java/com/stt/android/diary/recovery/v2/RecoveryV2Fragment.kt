package com.stt.android.diary.recovery.v2

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.fragment.app.Fragment
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.chart.api.ChartNavigator
import com.stt.android.chart.api.model.ChartComparison
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartStyle
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.home.diary.InfoBottomSheet
import com.stt.android.home.diary.TrainingHubInfoSheetFragmentCreator
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class RecoveryV2Fragment : Fragment() {
    @Inject
    lateinit var chartNavigator: ChartNavigator

    @Inject
    lateinit var trainingHubInfoSheetFragmentCreator: TrainingHubInfoSheetFragmentCreator

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )

        setContentWithM3Theme {
            RecoveryV2Screen(
                fragmentActivity = activity,
                onShowRecoveryStateInfoSheet = { infoBottomSheet ->
                    showRecoveryStateInfoSheet(infoBottomSheet)
                },
                onContributorClick = { contributorType, targetDate, chartGranularity ->
                    onContributorClick(contributorType, targetDate, chartGranularity)
                },
            )
        }
    }

    private fun showRecoveryStateInfoSheet(infoBottomSheet: InfoBottomSheet) {
        val dialog = trainingHubInfoSheetFragmentCreator.createTrainingHubInfoSheetFragment(infoBottomSheet)
        dialog.show(parentFragmentManager, infoBottomSheet.name)
    }

    private fun onContributorClick(
        contributorType: ContributorType, 
        targetDate: java.time.LocalDate, 
        chartGranularity: ChartGranularity
    ) {
        val chartContent: ChartContent = when (contributorType) {
            ContributorType.SLEEP -> ChartContent.SLEEP
            ContributorType.HRV -> ChartContent.HRV
            ContributorType.REST_HR -> ChartContent.RESTING_HEART_RATE
            ContributorType.MIN_HR -> ChartContent.MINIMUM_HEART_RATE
            ContributorType.RESOURCES -> ChartContent.RESOURCES
            ContributorType.TRAINING_FATIGUE -> throw IllegalArgumentException("Training fatigue not supported")
        }

        chartNavigator.openChartScreen(
            context = requireContext(),
            chartContent = chartContent,
            chartStyle = ChartStyle.SINGLE,
            chartGranularity = chartGranularity,
            chartComparison = ChartComparison.NONE,
            targetDate = targetDate,
            source = getSourceForAnalytics(),
        )
    }
    
    private fun getSourceForAnalytics(): String {
        return when {
            activity is RecoveryV2Activity -> AnalyticsPropertyValue.WidgetDetailPageExposureSourceProperty.RECOVERY_DETAIL_PAGE
            else -> AnalyticsPropertyValue.WidgetDetailPageExposureSourceProperty.RECOVERY
        }
    }
}
