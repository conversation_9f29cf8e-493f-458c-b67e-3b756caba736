package com.stt.android.diary.dailyhealth.composables

import androidx.annotation.StringRes
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyMegaBold
import com.stt.android.compose.theme.spacing
import com.stt.android.R as BaseR

@Composable
fun ChartValue(
    modifier: Modifier = Modifier,
    label: String? = null,
    value: AnnotatedString? = null,
    timeInfo: String? = null,
    @StringRes labelRes: Int? = null,
    @StringRes valueRes: Int? = null,
    @StringRes timeInfoRes: Int? = null
) {
    val noDataTitle = stringResource(BaseR.string.widget_no_data_title)
    val isNoData = value?.text == noDataTitle
    
    Row(
        modifier = modifier.padding(horizontal = MaterialTheme.spacing.medium,
            vertical = MaterialTheme.spacing.medium),
    ) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = when {
                    isNoData -> ""
                    labelRes != null -> stringResource(id = labelRes)
                    label != null -> label
                    else -> ""
                },
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.secondary
            )

            Text(
                text = when {
                    value != null -> value
                    valueRes != null -> AnnotatedString(stringResource(id = valueRes))
                    else -> AnnotatedString("")
                },
                style = MaterialTheme.typography.bodyMegaBold,
                color = MaterialTheme.colorScheme.onSurface
            )

            Text(
                text = when {
                    timeInfoRes != null -> stringResource(id = timeInfoRes)
                    timeInfo != null -> timeInfo
                    else -> ""
                },
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.secondary
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun ChartValuePreview() {
    M3AppTheme {
        ChartValue(
            label = "Range",
            value = buildAnnotatedString { append("68-136 bpm") },
            timeInfo = "This week"
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun ChartValueStepsPreview() {
    M3AppTheme {
        ChartValue(
            label = "Daily avg.",
            value = buildAnnotatedString { append("5,568") },
            timeInfo = "This week"
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun ChartValueCaloriesPreview() {
    M3AppTheme {
        ChartValue(
            label = "Active daily avg.",
            value = buildAnnotatedString { append("423 kcal") },
            timeInfo = "This week"
        )
    }
} 
