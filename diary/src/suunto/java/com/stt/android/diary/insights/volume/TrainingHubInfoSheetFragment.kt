package com.stt.android.diary.insights.volume

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.toDp
import com.stt.android.diary.insights.TrainingHubInfoSheetFragmentCreatorImpl.Companion.INFO_SHEET_KEY
import com.stt.android.diary.insights.common.TrainingHubInfoSheetContent
import com.stt.android.domain.diary.models.RecoveryZone
import com.stt.android.home.diary.InfoBottomSheet
import com.stt.android.home.diary.R
import com.stt.android.ui.utils.SmartBottomSheetDialogFragment
import com.stt.android.utils.title
import com.stt.android.utils.zoneColorRes
import com.stt.android.R as BR
import com.stt.android.chart.impl.R as ChartR

class TrainingHubInfoSheetFragment : SmartBottomSheetDialogFragment() {

    private val bottomSheet by lazy {
        val ordinal = arguments?.getInt(INFO_SHEET_KEY)
            ?: throw IllegalStateException("Info sheet type not passed")
        InfoBottomSheet.entries[ordinal]
    }

    private fun initSheetStateByInfo(): Int? = when (bottomSheet) {
        InfoBottomSheet.RECOVERY_INFO_TRAINING_FATIGUE,
        InfoBottomSheet.RECOVERY_INFO_HRV,
        InfoBottomSheet.TRAINING_HUB_RECOVERY_STATE,
        InfoBottomSheet.TRAINING_HUB_LOAD,
        InfoBottomSheet.TRAINING_HUB_VOLUME,
        InfoBottomSheet.TRAINING_HUB_INTENSITY,
        InfoBottomSheet.TRAINING_HUB_CARDIO_IMPACT,
        InfoBottomSheet.TRAINING_HUB_MUSCULAR_IMPACT,
        InfoBottomSheet.TRAINING_HUB_TRAINING_MODEL -> BottomSheetBehavior.STATE_EXPANDED

        InfoBottomSheet.TRAINING_HUB_PROGRESS,
        InfoBottomSheet.TRAINING_HUB_RECOVERY_HRV,
        InfoBottomSheet.TRAINING_HUB_RECOVERY_FORM,
        InfoBottomSheet.TRAINING_HUB_RECOVERY_SLEEP,
        InfoBottomSheet.TRAINING_HUB_RECOVERY_FEELING,
        InfoBottomSheet.RECOVERY_INFO_RHR,
        InfoBottomSheet.RECOVERY_INFO_RESOURCES,
        InfoBottomSheet.RECOVERY_INFO_MIN_HR,
        InfoBottomSheet.DAILY_HEALTH_HEART_RATE,
        InfoBottomSheet.DAILY_HEALTH_CALORIES -> null
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        initSheetStateByInfo()?.let {
            (dialog as BottomSheetDialog).behavior.state = it
        }
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )

        setContent {
            AppTheme {
                when (bottomSheet) {
                    InfoBottomSheet.TRAINING_HUB_VOLUME -> LoadAndVolumeInfoSheet()
                    InfoBottomSheet.TRAINING_HUB_LOAD -> LoadAndVolumeInfoSheet()
                    InfoBottomSheet.TRAINING_HUB_INTENSITY -> IntensityInfoSheet()
                    InfoBottomSheet.TRAINING_HUB_PROGRESS -> ProgressInfoSheet()
                    InfoBottomSheet.TRAINING_HUB_RECOVERY_HRV -> RecoveryHrvInfoSheet()
                    InfoBottomSheet.TRAINING_HUB_RECOVERY_FORM -> RecoveryFormInfoSheet()
                    InfoBottomSheet.TRAINING_HUB_RECOVERY_SLEEP -> RecoverySleepInfoSheet()
                    InfoBottomSheet.TRAINING_HUB_RECOVERY_FEELING -> RecoveryFeelingInfoSheet()
                    InfoBottomSheet.TRAINING_HUB_CARDIO_IMPACT -> CardioImpactInfoSheet()
                    InfoBottomSheet.TRAINING_HUB_MUSCULAR_IMPACT -> MuscularImpactInfoSheet()
                    InfoBottomSheet.TRAINING_HUB_TRAINING_MODEL -> TrainingModelInfoSheet()
                    InfoBottomSheet.TRAINING_HUB_RECOVERY_STATE -> RecoveryStateInfoSheet()
                    InfoBottomSheet.RECOVERY_INFO_HRV -> RecoveryV2HrvInfoSheet()
                    InfoBottomSheet.RECOVERY_INFO_RHR -> RecoveryV2RhrInfoSheet()
                    InfoBottomSheet.RECOVERY_INFO_MIN_HR -> RecoveryV2MinHrInfoSheet()
                    InfoBottomSheet.RECOVERY_INFO_TRAINING_FATIGUE -> RecoveryV2TrainingFatigueInfoSheet()
                    InfoBottomSheet.RECOVERY_INFO_RESOURCES -> RecoveryV2ResourcesInfoSheet()
                    InfoBottomSheet.DAILY_HEALTH_HEART_RATE -> DailyHealthHeartRateInfoSheet()
                    InfoBottomSheet.DAILY_HEALTH_CALORIES -> DailyHealthCaloriesInfoSheet()
                }
            }
        }
    }
}

@Composable
private fun LoadAndVolumeInfoSheet(
    modifier: Modifier = Modifier
) {
    TrainingHubInfoSheetContent(
        titleRes = R.string.training_hub_info_sheet_volume_and_load_title,
        richDescription = stringResource(R.string.training_hub_info_sheet_volume_and_load_md),
        readMoreLink = "https://www.suunto.com/en-us/sports/News-Articles-container-page/training-with-tss-and-hrtss/",
        modifier = modifier
    )
}

@Composable
private fun IntensityInfoSheet(
    modifier: Modifier = Modifier
) {
    TrainingHubInfoSheetContent(
        titleRes = R.string.training_hub_info_sheet_intensity_title,
        richDescription = stringResource(R.string.training_hub_info_sheet_intensity_md),
        readMoreLink = "https://www.suunto.com/fi-fi/sports/News-Articles-container-page/Figure-out-your-training-zones-and-supercharge-your-fitness/",
        modifier = modifier
    )
}

@Composable
private fun ProgressInfoSheet(
    modifier: Modifier = Modifier
) {
    TrainingHubInfoSheetContent(
        titleRes = R.string.training_hub_info_sheet_progress_title,
        richDescription = stringResource(R.string.training_hub_info_sheet_progress_md),
        modifier = modifier
    )
}

@Composable
private fun RecoveryHrvInfoSheet(
    modifier: Modifier = Modifier
) {
    TrainingHubInfoSheetContent(
        titleRes = R.string.training_hub_info_sheet_recovery_hrv_title,
        richDescription = stringResource(R.string.training_hub_info_sheet_recovery_hrv_md),
        readMoreLink = "https://www.suunto.com/sports/News-Articles-container-page/how-to-use-hrv-to-optimize-your-recovery",
        modifier = modifier
    )
}

@Composable
private fun RecoveryFormInfoSheet(
    modifier: Modifier = Modifier
) {
    TrainingHubInfoSheetContent(
        titleRes = R.string.training_hub_info_sheet_recovery_form_title,
        richDescription = stringResource(R.string.training_hub_info_sheet_recovery_form_md),
        modifier = modifier
    )
}

@Composable
private fun RecoverySleepInfoSheet(
    modifier: Modifier = Modifier
) {
    TrainingHubInfoSheetContent(
        titleRes = R.string.training_hub_info_sheet_recovery_sleep_title,
        richDescription = stringResource(R.string.training_hub_info_sheet_recovery_sleep_md),
        modifier = modifier
    )
}

@Composable
private fun RecoveryFeelingInfoSheet(
    modifier: Modifier = Modifier
) {
    TrainingHubInfoSheetContent(
        titleRes = R.string.training_hub_info_sheet_recovery_feeling_title,
        richDescription = stringResource(R.string.training_hub_info_sheet_recovery_feeling_md),
        modifier = modifier
    )
}

@Composable
private fun CardioImpactInfoSheet(
    modifier: Modifier = Modifier
) {
    TrainingHubInfoSheetContent(
        titleRes = R.string.training_hub_info_sheet_impact_title,
        richDescription = stringResource(R.string.training_hub_info_sheet_cardio_impact_md),
        readMoreLink = "https://www.suunto.com/en-us/sports/News-Articles-container-page/12-high-intensity-strength-training-exercises-for-endurance-athletes/",
        modifier = modifier
    )
}

@Composable
private fun MuscularImpactInfoSheet(
    modifier: Modifier = Modifier
) {
    TrainingHubInfoSheetContent(
        titleRes = R.string.training_hub_info_sheet_impact_title,
        richDescription = stringResource(R.string.training_hub_info_sheet_muscular_impact_md),
        readMoreLink = "https://www.suunto.com/en-us/sports/News-Articles-container-page/12-high-intensity-strength-training-exercises-for-endurance-athletes/",
        modifier = modifier
    )
}

@Composable
private fun TrainingModelInfoSheet(
    modifier: Modifier = Modifier
) {
    TrainingHubInfoSheetContent(
        titleRes = R.string.training_hub_info_sheet_training_models_title,
        richDescription = stringResource(R.string.training_hub_info_sheet_training_models_md),
        modifier = modifier
    )
}

@Composable
private fun RecoveryStateInfoSheet(
    modifier: Modifier = Modifier
) {
    TrainingHubInfoSheetContent(
        titleRes = R.string.training_hub_info_sheet_recovery_state_title,
        richDescription = stringResource(R.string.training_hub_info_sheet_recovery_state_md),
        modifier = modifier,
        readMoreLink = "https://www.suunto.com/en-gb/sports/News-Articles-container-page/four-ways-to-follow-your-recovery-with-suunto/",
        readMoreTextRes = R.string.training_hub_info_sheet_recovery_state_read_more,
    ) {
        RecoverStateInfoSheetExtra()
    }
}

@Composable
private fun RecoveryV2HrvInfoSheet(
    modifier: Modifier = Modifier
) {
    TrainingHubInfoSheetContent(
        titleRes = R.string.recovery_hrv_info_sheet_title,
        richDescription = stringResource(R.string.recovery_hrv_info_sheet_intensity_md),
        modifier = modifier
    )
}

@Composable
private fun RecoveryV2RhrInfoSheet(
    modifier: Modifier = Modifier
) {
    TrainingHubInfoSheetContent(
        titleRes = R.string.recovery_rhr_info_sheet_title,
        richDescription = stringResource(R.string.recovery_rhr_info_sheet_md),
        modifier = modifier
    )
}

@Composable
private fun RecoveryV2MinHrInfoSheet(
    modifier: Modifier = Modifier
) {
    TrainingHubInfoSheetContent(
        titleRes = R.string.recovery_min_hr_info_sheet_title,
        richDescription = stringResource(R.string.recovery_min_hr_info_sheet_md),
        modifier = modifier
    )
}

@Composable
private fun RecoveryV2TrainingFatigueInfoSheet(
    modifier: Modifier = Modifier
) {
    TrainingHubInfoSheetContent(
        titleRes = R.string.recovery_training_fatigue_info_sheet_title,
        richDescription = stringResource(R.string.recovery_training_fatigue_info_sheet_md),
        modifier = modifier
    )
}

@Composable
private fun RecoveryV2ResourcesInfoSheet(
    modifier: Modifier = Modifier
) {
    TrainingHubInfoSheetContent(
        titleRes = R.string.recovery_resources_info_sheet_title,
        richDescription = stringResource(R.string.recovery_resources_info_sheet_md),
        modifier = modifier
    )
}

@Composable
private fun DailyHealthHeartRateInfoSheet(
    modifier: Modifier = Modifier
) {
    TrainingHubInfoSheetContent(
        titleRes = ChartR.string.about_heart_rate_title,
        richDescription = stringResource(R.string.about_heart_rate_md),
        modifier = modifier
    )
}

@Composable
private fun DailyHealthCaloriesInfoSheet(
    modifier: Modifier = Modifier
) {
    TrainingHubInfoSheetContent(
        titleRes = R.string.about_calories_title,
        richDescription = stringResource(R.string.about_calories_md),
        modifier = modifier
    )
}

@Composable
private fun RecoverStateInfoSheetExtra(
    modifier: Modifier = Modifier,
) {
    val zoneDisplayItems = listOf(
        RecoveryZone.OPTIMAL to "80-100%",
        RecoveryZone.GOOD to "60-79%",
        RecoveryZone.FAIR to "40-59%",
        RecoveryZone.LIMITED to "20-39%",
        RecoveryZone.POOR to "0-19%",
        RecoveryZone.NO_DATA to "--"
    )
    val textStyle = MaterialTheme.typography.bodyLarge
    val textMeasurer = rememberTextMeasurer()
    val percentageTextWidth = remember {
        textMeasurer.measure(zoneDisplayItems.first().second, style = textStyle)
    }

    Column(
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        modifier = modifier.padding(vertical = MaterialTheme.spacing.medium),
    ) {
        Text(
            stringResource(BR.string.recovery_state_zones_header),
            style = MaterialTheme.typography.bodyXLargeBold,
            color = MaterialTheme.colors.secondary,
        )

        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xxsmall),
        ) {
            zoneDisplayItems.forEach { (zone, percentText) ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(6.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .size(6.dp)
                            .clip(CircleShape)
                            .background(colorForZone(zone)),
                    )
                    Text(
                        percentText,
                        style = textStyle,
                        modifier = Modifier.width(percentageTextWidth.size.width.toDp()),
                    )
                    Text(
                        titleForZone(zone),
                        style = textStyle,
                    )
                }
            }
        }
    }
}

@Composable
private fun titleForZone(zone: RecoveryZone) = stringResource(
    when (zone) {
        RecoveryZone.NO_DATA -> BR.string.recovery_state_not_enough_data
        else -> zone.title()
    }
)

@Composable
private fun colorForZone(zone: RecoveryZone): Color {
    return when (zone) {
        RecoveryZone.NO_DATA -> MaterialTheme.colors.darkGrey
        else -> colorResource(zone.zoneColorRes())
    }
}

@Preview
@Composable
private fun LoadAndVolumeInfoSheetPreview() {
    AppTheme {
        LoadAndVolumeInfoSheet()
    }
}

@Preview
@Composable
private fun ProgressInfoSheetPreview() {
    AppTheme {
        ProgressInfoSheet()
    }
}

@Preview
@Composable
private fun RecoveryHrvInfoSheetPreview() {
    AppTheme {
        RecoveryHrvInfoSheet()
    }
}

@Preview
@Composable
private fun RecoveryFormInfoSheetPreview() {
    AppTheme {
        RecoveryFormInfoSheet()
    }
}

@Preview
@Composable
private fun RecoverySleepInfoSheetPreview() {
    AppTheme {
        RecoverySleepInfoSheet()
    }
}

@Preview
@Composable
private fun RecoveryFeelingInfoSheetPreview() {
    AppTheme {
        RecoveryFeelingInfoSheet()
    }
}

@Preview
@Composable
private fun IntensityInfoSheetPreview() {
    AppTheme {
        IntensityInfoSheet()
    }
}

@Preview
@Composable
private fun ImpactInfoSheetPreview() {
    AppTheme {
        CardioImpactInfoSheet()
    }
}

@Preview
@Composable
private fun RecoveryStateInfoSheetPreview() {
    AppTheme {
        RecoveryStateInfoSheet()
    }
}

@Preview
@Composable
private fun RecoveryV2HrvInfoSheetPreview() {
    AppTheme {
        RecoveryV2HrvInfoSheet()
    }
}

@Preview
@Composable
private fun RecoveryV2RhrInfoSheetPreview() {
    AppTheme {
        RecoveryV2RhrInfoSheet()
    }
}

@Preview
@Composable
private fun RecoveryV2TrainingFatigueInfoSheetPreview() {
    AppTheme {
        RecoveryV2TrainingFatigueInfoSheet()
    }
}

@Preview
@Composable
private fun RecoveryV2ResourcesInfoSheetPreview() {
    AppTheme {
        RecoveryV2ResourcesInfoSheet()
    }
}

@Preview
@Composable
private fun DailyHealthHeartRateInfoSheetPreview() {
    AppTheme {
        DailyHealthHeartRateInfoSheet()
    }
}

@Preview
@Composable
private fun DailyHealthCaloriesInfoSheetPreview() {
    AppTheme {
        DailyHealthCaloriesInfoSheet()
    }
}
