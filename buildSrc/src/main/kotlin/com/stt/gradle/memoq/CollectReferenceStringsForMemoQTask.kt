package com.stt.gradle.memoq

import org.gradle.api.DefaultTask
import org.gradle.api.file.FileTree
import org.gradle.api.provider.Property
import org.gradle.api.tasks.Input
import org.gradle.api.tasks.Optional
import org.gradle.api.tasks.TaskAction
import java.io.File
import javax.inject.Inject
import javax.xml.stream.XMLInputFactory
import javax.xml.stream.XMLOutputFactory
import javax.xml.stream.XMLStreamConstants
import java.io.File.separatorChar as SEPARATOR

abstract class CollectReferenceStringsForMemoQTask
@Inject constructor() : DefaultTask() {
    @get:Input
    abstract val input: Property<FileTree>

    @get:Optional
    @get:Input
    abstract val output: Property<File?>

    private val xmlInputFactory: XMLInputFactory by lazy {
        XMLInputFactory.newInstance().apply {
            setProperty(XMLInputFactory.IS_COALESCING, false)
            setProperty("http://java.sun.com/xml/stream/properties/report-cdata-event", true)
        }
    }

    private val xmlOutputFactory: XMLOutputFactory by lazy { XMLOutputFactory.newInstance() }

    init {
        description =
            "Merge English strings.xml files into a single file to be submitted for translation via memoQ's content connector"
        group = "memoQ"
    }

    @TaskAction
    fun mergeSourceStrings() {
        // 'input' is all the English strings.xml files with translatable content.
        // All the content from these files will be merged to 'output', usually
        // build/memoQ/reference/strings.xml
        val files = input.get().files.sorted()
        var stringCount = 0
        val stringsXmlFilesByStringID = mutableMapOf<StringKey, MutableList<String>>()

        fun scanStringIDs(file: File) {
            file.inputStream().use { stream ->
                val inputPath = file.relativeTo(project.projectDir).path
                val reader = xmlInputFactory.createXMLStreamReader(stream)
                while (reader.hasNext()) {
                    reader.next()
                    if (reader.isStringResourceElement()) {
                        val type = reader.localName
                        val attributes = reader.attributes()
                        val translatable = attributes["translatable"]?.toBooleanStrict() ?: true
                        val name: String? = attributes["name"]

                        var nestedTagCount = 0
                        while (reader.hasNext()) {
                            reader.next()
                            when (reader.eventType) {
                                XMLStreamConstants.START_ELEMENT -> nestedTagCount++
                                XMLStreamConstants.END_ELEMENT -> {
                                    nestedTagCount--
                                    if (nestedTagCount < 0) {
                                        reader.next()
                                        break
                                    }
                                }

                                else -> {
                                    // pass
                                }
                            }
                        }

                        if (translatable && name != null) {
                            val key = StringKey(id = name, type = type)
                            stringsXmlFilesByStringID[key] =
                                stringsXmlFilesByStringID.getOrElse(key) { mutableListOf() }
                                    .apply { add(inputPath) }
                            stringCount++
                        }
                    }
                }
            }
        }

        for (file in files) {
            scanStringIDs(file)
        }

        val duplicateStringIdsWithNoResolution =
            mutableMapOf<String, List<String>>() // string id, files

        val duplicateStringIDMapping = stringsXmlFilesByStringID
            .filterValues { fileList -> fileList.size > 1 }
            .mapNotNull { (key, files) ->
                val (name, _) = key
                val st = files.any { it.contains("${SEPARATOR}sportstracker${SEPARATOR}") }
                val suunto = files.any { it.contains("${SEPARATOR}suunto${SEPARATOR}") }
                val suuntoChina = files.any { it.contains("${SEPARATOR}suuntoChina${SEPARATOR}") }
                val suuntoPlaystore = files.any { it.contains("${SEPARATOR}suuntoPlaystore${SEPARATOR}") }
                val china = files.any { it.contains("${SEPARATOR}china${SEPARATOR}") }
                val main = files.any { it.contains("${SEPARATOR}main${SEPARATOR}") }

                when {
                    (suuntoChina && suuntoPlaystore) || (suuntoChina && main) -> {
                        // Add '_china' postfix to Chinese variant string ID
                        files.first { it.contains("${SEPARATOR}suuntoChina${SEPARATOR}") } + ":$name" to "${name}_china"
                    }

                    china && main -> {
                        // Add '_china' postfix to Chinese variant string ID
                        files.first { it.contains("${SEPARATOR}china${SEPARATOR}") } + ":$name" to "${name}_china"
                    }

                    (st && suunto && !main) || (st && !suunto && main) -> {
                        // Add '_sportstracker' postfix to ST string ID
                        files.first { it.contains("${SEPARATOR}sportstracker${SEPARATOR}") } + ":$name" to "${name}_sportstracker"
                    }

                    !st && suunto && main -> {
                        // Add '_suunto' postfix to Suunto string ID
                        files.first { it.contains("${SEPARATOR}suunto${SEPARATOR}") } + ":$name" to "${name}_suunto"
                    }

                    else -> {
                        duplicateStringIdsWithNoResolution[name] = files
                        null
                    }
                }
            }.toMap()

        if (duplicateStringIdsWithNoResolution.isNotEmpty()) {
            println("No resolution for duplicate strings with ids")
            duplicateStringIdsWithNoResolution.forEach { (name, files) ->
                println("- $name")
                println(files.joinToString(separator = "") { "  -$it\n" })
                println()
            }
            throw IllegalStateException(
                "No resolution for duplicate string IDs has been found, please fix them before proceeding"
            )
        }

        println("Mapping duplicate string IDs as follows when merging:")
        duplicateStringIDMapping.forEach { (k, v) ->
            println("    $k -> $v")
        }

        val outputFile: File? = if (output.isPresent) output.get() else null
        if (outputFile == null) {
            println("Output file is not given, nothing is written to disk")
            return
        }

        outputFile.createNewFile()
        outputFile.outputStream().use { outputStream ->
            val writer = xmlOutputFactory.createXMLStreamWriter(outputStream, "UTF-8")
            writer.writeStartDocument("utf-8", "1.0")
            writer.writeCharacters("\n")
            writer.writeStartElement("resources")
            writer.writeNamespace("xliff", "urn:oasis:names:tc:xliff:document:1.2")
            writer.writeNamespace("tools", "http://schemas.android.com/tools")

            for (file in files) {
                file.inputStream().use { inputStream ->
                    val inputPath = file.relativeTo(project.projectDir).path

                    writer.writeCharacters("\n")
                    writer.writeComment(" File: $inputPath ")

                    val reader = xmlInputFactory.createXMLStreamReader(inputStream)
                    var nestingLevel = 0
                    while (reader.hasNext()) {
                        reader.next()
                        when (reader.eventType) {
                            XMLStreamConstants.START_ELEMENT -> {
                                nestingLevel++
                                if (nestingLevel >= 2) {
                                    val attributes = reader.attributes()
                                    val translatable =
                                        attributes["translatable"]?.toBooleanStrict() ?: true
                                    if (translatable) {
                                        writer.writeStartElement(
                                            reader.prefix ?: "",
                                            reader.localName,
                                            ""
                                        )
                                        val name = attributes["name"]
                                        val pathWithName = "$inputPath:$name"
                                        if (duplicateStringIDMapping.contains(pathWithName)) {
                                            val nameWithPostfix =
                                                duplicateStringIDMapping[pathWithName]!!
                                            attributes["name"] = nameWithPostfix
                                        }
                                        writer.writeAttributes(attributes)
                                    } else {
                                        println("Skipping non-translatable string '${attributes["name"]}'")
                                        reader.skipUntilEndOfCurrentTag()
                                        nestingLevel--
                                    }
                                }
                            }

                            XMLStreamConstants.CHARACTERS -> writer.writeCharacters(reader.text)
                            XMLStreamConstants.CDATA -> writer.writeCData(reader.text)
                            XMLStreamConstants.END_ELEMENT -> {
                                nestingLevel--
                                if (nestingLevel >= 1) {
                                    writer.writeEndElement()
                                }
                            }

                            XMLStreamConstants.COMMENT -> writer.writeComment(reader.text)
                            XMLStreamConstants.END_DOCUMENT -> {}
                            XMLStreamConstants.START_DOCUMENT -> {}
                            else -> TODO("Don't know how to handle event type ${reader.eventType}")
                        }
                    }
                }
            }

            writer.writeEndElement()
            writer.writeEndDocument()
        }

        println("Processed $stringCount string resources")
    }
}
