package com.stt.android.device.domain.suuntoplusfeature.settings

import com.stt.android.device.datasource.WatchSerialDataSource
import kotlinx.coroutines.flow.first
import timber.log.Timber
import javax.inject.Inject

class LockAndGetSettingsFromWatchUseCase
@Inject constructor(
    private val watchDataSource: WatchSportsAppSettingsDataSource,
    private val settingsStateDataSource: SportsAppSettingsStateDataSource,
    private val watchSerialDataSource: WatchSerialDataSource
) {
    // Note that there is no proper queue. If two actors are calling this simultaneously, one of
    // them will win the race condition and succeed and the other one will fail with either timeout
    // or already locked exception. This is not an issue when used from SuuntoPlusSettingsViewModel.
    suspend fun waitUntilNotBusyAndLockAndGetSettingsData(pluginId: String): String {
        val serial = watchSerialDataSource.getCurrentWatchSerial()
            ?: throw IllegalStateException("Missing watch serial")

        settingsStateDataSource.anySportsAppBusy(serial)
            .first { busy -> !busy }

        return lockAndGetSettingsData(pluginId)
    }

    /**
     * Lock the sports app in the watch, download the settings 'data.jsn' file and leave the watch
     * in locked state.
     *
     * If there is an error when locking or downloading the file, then the watch is unlocked. The
     * locked state in the local database is updated accordingly. If the sports app is already
     * locked, then it will not be locked again as there is no need.
     */
    private suspend fun lockAndGetSettingsData(pluginId: String): String {
        val serial = watchSerialDataSource.getCurrentWatchSerial()
            ?: throw IllegalStateException("Missing watch serial")

        return settingsStateDataSource.runWithBusyState(serial, pluginId) {
            if (!settingsStateDataSource.isMarkedAsLocked(serial, pluginId)) {
                watchDataSource.lock(pluginId)
                settingsStateDataSource.markAsLocked(serial, pluginId)
            }

            try {
                val dataJson = watchDataSource.loadDataJson(pluginId)
                settingsStateDataSource.setDataJson(serial, pluginId, dataJson)
                dataJson
            } catch (e: Exception) {
                Timber.w(e, "Failed to get settings data JSON. Unlocking...")
                kotlin.runCatching {
                    watchDataSource.unlock(pluginId)
                    settingsStateDataSource.markAsUnlocked(serial, pluginId)
                }.onFailure { Timber.w(it, "Failed to unlock sports app") }
                throw e
            }
        }
    }
}
