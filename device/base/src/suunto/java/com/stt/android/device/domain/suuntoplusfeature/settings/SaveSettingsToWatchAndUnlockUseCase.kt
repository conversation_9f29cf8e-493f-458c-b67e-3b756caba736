package com.stt.android.device.domain.suuntoplusfeature.settings

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.device.datasource.WatchSerialDataSource
import timber.log.Timber
import javax.inject.Inject

class SaveSettingsToWatchAndUnlockUseCase
@Inject constructor(
    private val watchDataSource: WatchSportsAppSettingsDataSource,
    private val settingsStateDataSource: SportsAppSettingsStateDataSource,
    private val watchSerialDataSource: WatchSerialDataSource
) {
    /**
     * Save modified sports app settings to watch. Typically the sports app in question should be
     * locked already, but if not, lock it and verify that 'data.jsn' contents have not been
     * changed.
     *
     * The sports app is unlocked after this operation regardless if saving settings succeeded or
     * not.
     */
    suspend fun saveSettingsDataAndUnlock(
        pluginId: String,
        expectedDataJson: String,
        newDataJson: String
    ) {
        val serial = watchSerialDataSource.getCurrentWatchSerial()
            ?: throw IllegalStateException("Missing watch serial")

        settingsStateDataSource.runWithBusyState(serial, pluginId) {
            var previousPersistedDataJson: String? = null
            try {
                // Save new JSON to local database immediately even before it has been saved to
                // the watch. If an error occurs, restore the previous value.
                previousPersistedDataJson = settingsStateDataSource.findDataJson(serial, pluginId)
                settingsStateDataSource.setDataJson(serial, pluginId, newDataJson)

                if (!settingsStateDataSource.isMarkedAsLocked(serial, pluginId)) {
                    // If the sports app was not locked, lock it and verify that 'data.jsn'
                    // content has not changed. The lock may have been lost if the watch connection
                    // was interrupted, for example. Verifying the content is needed because we
                    // have no conflict resolution strategy if the settings have been modified by
                    // two actors separately.
                    Timber.d("saveSettingsDataAndUnlock: Locking plug-in ID $pluginId")
                    watchDataSource.lock(pluginId)
                    settingsStateDataSource.markAsLocked(serial, pluginId)
                    Timber.d("saveSettingsDataAndUnlock: Locking successful")

                    val oldDataJson = watchDataSource.loadDataJson(pluginId)
                    settingsStateDataSource.setDataJson(serial, pluginId, oldDataJson)
                    Timber.d("saveSettingsDataAndUnlock: Got current sports app data JSON")

                    if (expectedDataJson != oldDataJson) {
                        Timber.w("Sports app settings data JSON mismatch:\n\nExpected: $expectedDataJson\n\nGot: $oldDataJson")
                        throw IllegalStateException("Unable to save data JSON: data on watch has changed unexpectedly")
                    }
                }

                watchDataSource.saveDataJson(pluginId, newDataJson)
                Timber.d("saveSettingsDataAndUnlock: Updated data JSON saved successfully")
            } catch (e: Exception) {
                Timber.w(e, "Failed to save settings data JSON. Unlocking...")
                runSuspendCatching {
                    // Restore previously persisted data to local database if saving fails
                    settingsStateDataSource.setDataJson(serial, pluginId, previousPersistedDataJson)
                }
                throw e
            } finally {
                runSuspendCatching {
                    watchDataSource.unlock(pluginId)
                    settingsStateDataSource.markAsUnlocked(serial, pluginId)
                }.onFailure { Timber.w(it, "Failed to unlock sports app") }
            }
        }
    }

    suspend fun unlock(pluginId: String) {
        Timber.d("Unlocking plug-in ID $pluginId")
        val serial = watchSerialDataSource.getCurrentWatchSerial()
            ?: throw IllegalStateException("Missing watch serial")
        watchDataSource.unlock(pluginId)
        settingsStateDataSource.markAsUnlocked(serial, pluginId)
    }
}
