#!/usr/bin/env python3
import os
import xml.etree.ElementTree as ET
from collections import defaultdict
import sys

def find_string_files():
    """查找所有的strings.xml文件（只包含默认values目录，排除本地化版本）"""
    string_files = []
    for root, dirs, files in os.walk('.'):
        if 'strings.xml' in files and 'res/values' in root:
            # 只包含默认的 values 目录，排除本地化版本
            if root.endswith('/res/values'):
                string_files.append(os.path.join(root, 'strings.xml'))
    return sorted(string_files)

def parse_strings_xml(file_path):
    """解析strings.xml文件并返回string key到文件路径的映射"""
    string_keys = {}
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        for string_elem in root.findall('string'):
            name = string_elem.get('name')
            if name:
                string_keys[name] = file_path
                
    except ET.ParseError as e:
        print(f"Error parsing {file_path}: {e}")
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        
    return string_keys

def get_module_name(file_path):
    """从文件路径中提取模块名"""
    parts = file_path.split('/')
    # 查找第一个非 . 的部分作为模块名
    for part in parts:
        if part != '.' and part != '':
            return part
    return "unknown"

def check_duplicate_strings():
    """检查模块间重复的string key"""
    string_files = find_string_files()
    print(f"Found {len(string_files)} default strings.xml files")
    
    # 存储每个key出现的文件列表
    key_to_files = defaultdict(list)
    
    for file_path in string_files:
        print(f"Processing: {file_path}")
        string_keys = parse_strings_xml(file_path)
        
        for key in string_keys:
            key_to_files[key].append(file_path)
    
    # 查找重复的key（只在不同模块间重复）
    duplicates = {}
    for key, files in key_to_files.items():
        if len(files) > 1:
            # 检查是否来自不同模块
            modules = set(get_module_name(f) for f in files)
            if len(modules) > 1:
                duplicates[key] = files
    
    if duplicates:
        print(f"\n🚨 Found {len(duplicates)} duplicate string keys between modules:")
        print("=" * 80)
        
        # 按模块分组显示
        module_duplicates = defaultdict(list)
        for key, files in sorted(duplicates.items()):
            modules = [get_module_name(f) for f in files]
            module_key = " vs ".join(sorted(set(modules)))
            module_duplicates[module_key].append((key, files))
        
        for module_pair, keys in sorted(module_duplicates.items()):
            print(f"\n📦 Module conflict: {module_pair}")
            print(f"   {len(keys)} duplicate keys:")
            for key, files in keys:  # 显示所有的key
                print(f"   - '{key}'")
                for file_path in files:
                    print(f"     • {file_path}")
                
        print("\n" + "=" * 80)
        print(f"Total cross-module duplicate keys: {len(duplicates)}")
        return len(duplicates)
    else:
        print("\n✅ No duplicate string keys found between modules!")
        return 0

if __name__ == "__main__":
    duplicate_count = check_duplicate_strings()
    sys.exit(duplicate_count if duplicate_count > 0 else 0) 