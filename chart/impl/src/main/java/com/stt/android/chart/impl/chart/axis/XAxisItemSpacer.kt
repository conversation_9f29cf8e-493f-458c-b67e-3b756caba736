package com.stt.android.chart.impl.chart.axis

import com.patrykandpatrick.vico.core.cartesian.CartesianDrawingContext
import com.patrykandpatrick.vico.core.cartesian.CartesianMeasuringContext
import com.patrykandpatrick.vico.core.cartesian.axis.HorizontalAxis
import com.patrykandpatrick.vico.core.cartesian.data.CartesianChartModel
import com.patrykandpatrick.vico.core.cartesian.layer.CartesianLayerDimensions
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.model.ChartData
import java.time.LocalDate

fun createXAxisItemPlacer(
    chartGranularity: ChartGranularity,
    axisRange: ChartData.AxisRange? = null,
    chartContent: ChartContent? = null,
): HorizontalAxis.ItemPlacer = when (chartGranularity) {
    ChartGranularity.DAILY -> HorizontalAxis.ItemPlacer.aligned(spacing = { 36 })
    ChartGranularity.WEEKLY,
    ChartGranularity.SEVEN_DAYS,
    ChartGranularity.EIGHT_YEARS -> HorizontalAxis.ItemPlacer.aligned()
    ChartGranularity.MONTHLY -> OptimizedMonthlyItemPlacer()
    ChartGranularity.THIRTY_DAYS -> ThirtyDaysItemPlacer()
    ChartGranularity.SIXTY_DAYS,
    ChartGranularity.SIX_WEEKS -> HorizontalAxis.ItemPlacer.aligned(spacing = { 7 })
    ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS -> HorizontalAxis.ItemPlacer.aligned(spacing = { 91 })
    ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS -> HorizontalAxis.ItemPlacer.aligned(spacing = {21})
    ChartGranularity.YEARLY -> {
       if (chartContent == ChartContent.VO2MAX){
           HorizontalAxis.ItemPlacer.aligned(spacing = {53})
       } else {
           HorizontalAxis.ItemPlacer.aligned()
       }
    }
    ChartGranularity.SIX_MONTHS -> DynamicMonthItemPlacer()
}


private class DynamicMonthItemPlacer : HorizontalAxis.ItemPlacer {

    override fun getShiftExtremeLines(context: CartesianDrawingContext): Boolean = false

    override fun getHeightMeasurementLabelValues(
        context: CartesianMeasuringContext,
        layerDimensions: CartesianLayerDimensions,
        fullXRange: ClosedFloatingPointRange<Double>,
        maxLabelWidth: Float,
    ): List<Double> {
        return getMonthlyLabelPositions(fullXRange)
    }

    override fun getWidthMeasurementLabelValues(
        context: CartesianMeasuringContext,
        layerDimensions: CartesianLayerDimensions,
        fullXRange: ClosedFloatingPointRange<Double>,
    ): List<Double> = getMonthlyLabelPositions(fullXRange)

    override fun getFirstLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double {
        val ranges = context.ranges
        return ranges.minX
    }

    override fun getLastLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double {
        val ranges = context.ranges
        return ranges.maxX
    }

    override fun getLabelValues(
        context: CartesianDrawingContext,
        visibleXRange: ClosedFloatingPointRange<Double>,
        fullXRange: ClosedFloatingPointRange<Double>,
        maxLabelWidth: Float,
    ): List<Double> {
        return getMonthlyLabelPositions(fullXRange)
    }

    override fun getLineValues(
        context: CartesianDrawingContext,
        visibleXRange: ClosedFloatingPointRange<Double>,
        fullXRange: ClosedFloatingPointRange<Double>,
        maxLabelWidth: Float,
    ): List<Double> {
        val monthlyLabels = getMonthlyLabelPositions(fullXRange)
        val allTicks = mutableListOf<Double>()
        allTicks.addAll(monthlyLabels)

        for (i in 0 until monthlyLabels.size - 1) {
            val startPos = monthlyLabels[i]
            val endPos = monthlyLabels[i + 1]
            val interval = (endPos - startPos) / 4.0
            for (j in 1..3) {
                allTicks.add(startPos + j * interval)
            }
        }

        if (monthlyLabels.isNotEmpty()) {
            var pos = monthlyLabels.first().toLong() - 7
            while (pos >= fullXRange.start.toLong()) {
                allTicks.add(pos.toDouble())
                pos -= 7
            }
        }

        if (monthlyLabels.isNotEmpty()) {
            var pos = monthlyLabels.last().toLong() + 7
            while (pos <= fullXRange.endInclusive.toLong()) {
                allTicks.add(pos.toDouble())
                pos += 7
            }
        }

        return allTicks.filter { it >= visibleXRange.start && it <= visibleXRange.endInclusive }.sorted().distinct()
    }

    private fun getMonthlyLabelPositions(fullXRange: ClosedFloatingPointRange<Double>): List<Double> {
        val startEpochDay = fullXRange.start.toLong()
        val endEpochDay = fullXRange.endInclusive.toLong()
        val startDate = LocalDate.ofEpochDay(startEpochDay)
        val endDate = LocalDate.ofEpochDay(endEpochDay)
        val labelPositions = mutableListOf<Double>()
        var currentDate = startDate.withDayOfMonth(1).plusDays(10)

        if (startDate.dayOfMonth > 1) {
            currentDate = currentDate.plusMonths(1)
        }

        var monthCount = 0
        while (!currentDate.isAfter(endDate) && monthCount < 6) {
            val epochDay = currentDate.toEpochDay().toDouble()

            if (epochDay >= fullXRange.start + 10 && epochDay <= fullXRange.endInclusive - 10) {
                labelPositions.add(epochDay)
                monthCount++
            }

            currentDate = currentDate.plusMonths(1)
        }

        if (labelPositions.size < 6) {
            val totalRange = endEpochDay - startEpochDay
            val interval = totalRange / 5.0
            labelPositions.clear()

            val adjustedStart = startEpochDay + (interval * 0.5).toLong()
            val adjustedEnd = endEpochDay - (interval * 0.5).toLong()

            for (i in 0..5) {
                val position = adjustedStart + (i * interval).toLong()
                if (position <= adjustedEnd) {
                    labelPositions.add(position.toDouble())
                }
            }

        }
        return labelPositions.sorted()
    }

    override fun getStartLayerMargin(
        context: CartesianMeasuringContext,
        layerDimensions: CartesianLayerDimensions,
        tickThickness: Float,
        maxLabelWidth: Float,
    ): Float = 0f

    override fun getEndLayerMargin(
        context: CartesianMeasuringContext,
        layerDimensions: CartesianLayerDimensions,
        tickThickness: Float,
        maxLabelWidth: Float,
    ): Float = 0f
}

internal class OptimizedMonthlyItemPlacer : HorizontalAxis.ItemPlacer {

    override fun getShiftExtremeLines(context: CartesianDrawingContext): Boolean = false

    override fun getHeightMeasurementLabelValues(
        context: CartesianMeasuringContext,
        layerDimensions: CartesianLayerDimensions,
        fullXRange: ClosedFloatingPointRange<Double>,
        maxLabelWidth: Float,
    ): List<Double> {
        return getOptimizedLabelPositions(fullXRange)
    }

    override fun getWidthMeasurementLabelValues(
        context: CartesianMeasuringContext,
        layerDimensions: CartesianLayerDimensions,
        fullXRange: ClosedFloatingPointRange<Double>,
    ): List<Double> = getOptimizedLabelPositions(fullXRange)

    override fun getFirstLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double {
        val ranges = context.ranges
        return ranges.minX
    }

    override fun getLastLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double {
        val ranges = context.ranges
        return ranges.maxX
    }

    override fun getLabelValues(
        context: CartesianDrawingContext,
        visibleXRange: ClosedFloatingPointRange<Double>,
        fullXRange: ClosedFloatingPointRange<Double>,
        maxLabelWidth: Float,
    ): List<Double> {
        return getOptimizedLabelPositions(fullXRange)
    }

    override fun getLineValues(
        context: CartesianDrawingContext,
        visibleXRange: ClosedFloatingPointRange<Double>,
        fullXRange: ClosedFloatingPointRange<Double>,
        maxLabelWidth: Float,
    ): List<Double> {
        val labelPositions = getOptimizedLabelPositions(fullXRange)
        val allTicks = mutableListOf<Double>()

        allTicks.addAll(labelPositions)

        val startDay = fullXRange.start.toLong()
        val endDay = fullXRange.endInclusive.toLong()

        for (day in startDay..endDay) {
            allTicks.add(day.toDouble())
        }

        return allTicks.filter { it >= visibleXRange.start && it <= visibleXRange.endInclusive }
            .sorted()
            .distinct()
    }


    private fun getOptimizedLabelPositions(fullXRange: ClosedFloatingPointRange<Double>): List<Double> {
        val startEpochDay = fullXRange.start.toLong()
        val endEpochDay = fullXRange.endInclusive.toLong()
        val labelPositions = mutableListOf<Double>()

        val midPoint = (startEpochDay + endEpochDay) / 2
        val midDate = LocalDate.ofEpochDay(midPoint)
        val targetMonth = midDate.month
        val targetYear = midDate.year

        val firstDayOfTargetMonth = LocalDate.of(targetYear, targetMonth, 1)
        val monthFirstDay = firstDayOfTargetMonth.toEpochDay()

        var currentDay = monthFirstDay

        while (currentDay <= endEpochDay + 7) {
            if (currentDay >= startEpochDay - 14 && currentDay <= endEpochDay + 7) {
                labelPositions.add(currentDay.toDouble())
            }
            currentDay += 7
        }

        return labelPositions
    }

    override fun getStartLayerMargin(
        context: CartesianMeasuringContext,
        layerDimensions: CartesianLayerDimensions,
        tickThickness: Float,
        maxLabelWidth: Float,
    ): Float {
        return 0f
    }

    override fun getEndLayerMargin(
        context: CartesianMeasuringContext,
        layerDimensions: CartesianLayerDimensions,
        tickThickness: Float,
        maxLabelWidth: Float,
    ): Float {
        return 0f
    }
}

private class ThirtyDaysItemPlacer : HorizontalAxis.ItemPlacer {

    override fun getShiftExtremeLines(context: CartesianDrawingContext): Boolean = false

    override fun getHeightMeasurementLabelValues(
        context: CartesianMeasuringContext,
        layerDimensions: CartesianLayerDimensions,
        fullXRange: ClosedFloatingPointRange<Double>,
        maxLabelWidth: Float,
    ): List<Double> = getFixedLabelPositions(fullXRange, context.ranges.minX)

    override fun getWidthMeasurementLabelValues(
        context: CartesianMeasuringContext,
        layerDimensions: CartesianLayerDimensions,
        fullXRange: ClosedFloatingPointRange<Double>,
    ): List<Double> = getFixedLabelPositions(fullXRange, context.ranges.minX)

    override fun getFirstLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double {
        return context.ranges.minX
    }

    override fun getLastLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double {
        return context.ranges.maxX
    }

    override fun getLabelValues(
        context: CartesianDrawingContext,
        visibleXRange: ClosedFloatingPointRange<Double>,
        fullXRange: ClosedFloatingPointRange<Double>,
        maxLabelWidth: Float,
    ): List<Double> = getFixedLabelPositions(fullXRange, context.ranges.minX)

    override fun getLineValues(
        context: CartesianDrawingContext,
        visibleXRange: ClosedFloatingPointRange<Double>,
        fullXRange: ClosedFloatingPointRange<Double>,
        maxLabelWidth: Float,
    ): List<Double> {
        val labelPositions = getFixedLabelPositions(fullXRange, context.ranges.minX)
        val allTicks = mutableListOf<Double>()

        allTicks.addAll(labelPositions)

        val startDay = fullXRange.start.toLong()
        val endDay = fullXRange.endInclusive.toLong()

        for (day in startDay..endDay) {
            allTicks.add(day.toDouble())
        }

        return allTicks.filter { it >= visibleXRange.start && it <= visibleXRange.endInclusive }
            .sorted()
            .distinct()
    }

    override fun getStartLayerMargin(
        context: CartesianMeasuringContext,
        layerDimensions: CartesianLayerDimensions,
        tickThickness: Float,
        maxLabelWidth: Float,
    ): Float = 0f

    override fun getEndLayerMargin(
        context: CartesianMeasuringContext,
        layerDimensions: CartesianLayerDimensions,
        tickThickness: Float,
        maxLabelWidth: Float,
    ): Float = 0f

    private fun getFixedLabelPositions(
        fullXRange: ClosedFloatingPointRange<Double>,
        actualMinX: Double
    ): List<Double> {
        val startEpochDay = actualMinX.toLong()
        fullXRange.endInclusive.toLong()
        val labelPositions = mutableListOf<Double>()

        for (i in 0 until 5) {
            labelPositions.add((startEpochDay + i * 7).toDouble())
        }
        return labelPositions
    }
}

fun calculateXAxisStep(
    chartGranularity: ChartGranularity,
): (CartesianChartModel) -> Double = {
    when (chartGranularity) {
        ChartGranularity.DAILY -> 10.0
        ChartGranularity.SIX_MONTHS -> 7.0
        ChartGranularity.WEEKLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.MONTHLY,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.YEARLY,
        ChartGranularity.EIGHT_YEARS -> 1.0
    }
}
