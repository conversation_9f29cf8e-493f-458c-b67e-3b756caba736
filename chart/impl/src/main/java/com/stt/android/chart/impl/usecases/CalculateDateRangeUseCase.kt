package com.stt.android.chart.impl.usecases

import androidx.annotation.IntRange
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.controllers.UserSettingsController
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.temporal.TemporalAdjusters
import javax.inject.Inject

class CalculateDateRangeUseCase @Inject constructor(
    private val userSettingsController: UserSettingsController,
) {
    operator fun invoke(
        chartGranularity: ChartGranularity,
        @IntRange(from = 0L) chartPageIndex: Int,
        @IntRange(from = 1L) chartPageCount: Int,
        today: LocalDate = LocalDate.now(),
    ): ClosedRange<LocalDate> {
        val diff = chartPageCount - chartPageIndex - 1L
        return when (chartGranularity) {
            ChartGranularity.DAILY -> {
                val date = today.minusDays(diff)
                date..date
            }
            ChartGranularity.WEEKLY -> {
                val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
                val startOfWeek = today.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
                    .minusWeeks(diff)
                val endOfWeek = startOfWeek.plusDays(6L)
                startOfWeek..endOfWeek
            }
            ChartGranularity.SEVEN_DAYS -> {
                val endOfSevenDays = today.minusDays(7L * diff)
                val startOfSevenDays = endOfSevenDays.minusDays(6L)
                startOfSevenDays..endOfSevenDays
            }
            ChartGranularity.MONTHLY -> {
                val startOfMonth = today.minusMonths(diff)
                    .withDayOfMonth(1)
                val endOfMonth = startOfMonth.withDayOfMonth(startOfMonth.lengthOfMonth())
                startOfMonth..endOfMonth
            }
            ChartGranularity.THIRTY_DAYS -> {
                val endOfThirtyDays = today.minusDays(30L * diff)
                val startOfThirtyDays = endOfThirtyDays.minusDays(29L)
                startOfThirtyDays..endOfThirtyDays
            }
            ChartGranularity.SIXTY_DAYS -> {
                val endOfSixtyDays = today.minusDays(60L * diff)
                val startOfSixtyDays = endOfSixtyDays.minusDays(59L)
                startOfSixtyDays..endOfSixtyDays
            }
            ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS -> {
                val endOfPeriod = today.minusDays(180L * diff)
                val startOfPeriod = endOfPeriod.minusDays(179L)
                startOfPeriod..endOfPeriod
            }
            ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS -> {
                val endOfPeriod = today.minusDays(365L * diff)
                val startOfPeriod = endOfPeriod.minusDays(364L)
                startOfPeriod..endOfPeriod
            }
            ChartGranularity.SIX_WEEKS -> {
                val endOfSixWeeks = today.minusWeeks(6L * diff)
                val startOfSixWeek = endOfSixWeeks
                    .minusWeeks(5L)
                    .minusDays(6L)
                startOfSixWeek..endOfSixWeeks
            }
            ChartGranularity.SIX_MONTHS -> {
                val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
                var period = calculateSixMonthWithWeek(today, firstDayOfWeek)
                var offsetDate = period.start.minusDays(1)
                repeat(diff.toInt()) {
                    period = calculateSixMonthWithWeek(offsetDate, firstDayOfWeek)
                    offsetDate = period.start.minusDays(1)
                }
                period
            }
            ChartGranularity.YEARLY -> {
                val startOfYear = today.withDayOfYear(1)
                    .minusYears(diff)
                val endOfYear = startOfYear
                    .withDayOfYear(startOfYear.lengthOfYear())
                startOfYear..endOfYear
            }
            ChartGranularity.EIGHT_YEARS -> {
                val startOfEightYears = today.withDayOfYear(1)
                    .minusYears(7L + 8L * diff)
                val endOfEightYears = startOfEightYears
                    .plusYears(7L)
                    .withDayOfYear(startOfEightYears.lengthOfYear())
                startOfEightYears..endOfEightYears
            }
        }
    }

    private fun calculateSixMonthWithWeek(
        date: LocalDate,
        firstDayOfWeek: DayOfWeek,
    ): ClosedRange<LocalDate> {
        val startDate = date
            .with(TemporalAdjusters.nextOrSame(firstDayOfWeek))
        return startDate.minusMonths(6)
            .with(TemporalAdjusters.previousOrSame(firstDayOfWeek))..startDate.minusDays(1)
    }
}
