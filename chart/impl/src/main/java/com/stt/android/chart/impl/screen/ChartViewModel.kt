package com.stt.android.chart.impl.screen

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.chart.api.model.ChartComparison
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartStyle
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.SleepComparisonGraphType
import com.stt.android.chart.impl.model.WidgetInstruction
import com.stt.android.chart.impl.model.availableStyles
import com.stt.android.chart.impl.model.comparisonOffTitleRes
import com.stt.android.chart.impl.model.comparisonOnTitleRes
import com.stt.android.chart.impl.model.extraChartGranularity
import com.stt.android.chart.impl.model.leftComparisonColorRes
import com.stt.android.chart.impl.model.mainChartGranularity
import com.stt.android.chart.impl.model.rightComparisonColorRes
import com.stt.android.chart.impl.model.rightValueTypeRes
import com.stt.android.chart.impl.model.supportsComparison
import com.stt.android.chart.impl.model.targetChartComparison
import com.stt.android.chart.impl.model.titleRes
import com.stt.android.chart.impl.model.toAnalyticsTimeDim
import com.stt.android.chart.impl.model.toAnalyticsWidgetName
import com.stt.android.chart.impl.model.valueTypeRes
import com.stt.android.chart.impl.usecases.CalculateDateRangeUseCase
import com.stt.android.chart.impl.usecases.CommuteTagsDataUseCase
import com.stt.android.chart.impl.usecases.CreateChartDataUseCase
import com.stt.android.chart.impl.usecases.CreateChartTimeRangeStringUseCase
import com.stt.android.chart.impl.usecases.CreateGoalDataUseCase
import com.stt.android.chart.impl.usecases.CreateGoalEditorDataUseCase
import com.stt.android.chart.impl.usecases.CreateSleepViewDataUseCase
import com.stt.android.chart.impl.usecases.GetChartHighlightDataUseCase
import com.stt.android.chart.impl.usecases.GetHeartRateStatsViewDataUseCase
import com.stt.android.chart.impl.usecases.GetWidgetInstructionsUseCase
import com.stt.android.chart.impl.usecases.IsWatchConnectedUseCase
import com.stt.android.chart.impl.usecases.UpdateGoalUseCase
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.combine
import com.stt.android.data.RELEASED_YEAR
import com.stt.android.domain.user.autoCommuteTaggingEnabled
import com.stt.android.eventtracking.EventTracker
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import android.util.Log
import java.time.LocalDate
import java.time.Year
import java.time.temporal.ChronoUnit
import javax.inject.Inject

@HiltViewModel
internal class ChartViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    private val createGoalDataUseCase: CreateGoalDataUseCase,
    private val createGoalEditorDataUseCase: CreateGoalEditorDataUseCase,
    private val updateGoalUseCase: UpdateGoalUseCase,
    private val createChartDataUseCase: CreateChartDataUseCase,
    private val calculateDateRangeUseCase: CalculateDateRangeUseCase,
    private val createChartTimeRangeStringUseCase: CreateChartTimeRangeStringUseCase,
    private val getChartHighlightDataUseCase: GetChartHighlightDataUseCase,
    private val isWatchConnectedUseCase: IsWatchConnectedUseCase,
    private val getWidgetInstructionsUseCase: GetWidgetInstructionsUseCase,
    private val createSleepViewDataUseCase: CreateSleepViewDataUseCase,
    private val getHeartRateStatsViewDataUseCase: GetHeartRateStatsViewDataUseCase,
    private val commuteTagsDataUseCase: CommuteTagsDataUseCase,
    private val userSettingsController: UserSettingsController,
    private val eventTracker: EventTracker,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : ViewModel() {
    private val chartContent: Flow<ChartContent> = savedStateHandle
        .getStateFlow(ChartActivity.KEY_CHART_CONTENT, null)
        .filterNotNull()
    private val chartStyle: Flow<ChartStyle> = savedStateHandle
        .getStateFlow(ChartActivity.KEY_CHART_STYLE, null)
        .filterNotNull()
    private val chartGranularity: Flow<ChartGranularity> = savedStateHandle
        .getStateFlow(ChartActivity.KEY_CHART_GRANULARITY, null)
        .filterNotNull()
    private val chartComparison: Flow<ChartComparison> = savedStateHandle
        .getStateFlow(ChartActivity.KEY_CHART_COMPARISON, null)
        .filterNotNull()
    private val scrollToCurrentPage: MutableStateFlow<Boolean> = MutableStateFlow(false)
    private val chartPageIndex: MutableStateFlow<Int> = MutableStateFlow(
        calculateInitialPageIndex()
    )
    private val showExtraChartGranularitySelection: MutableStateFlow<Boolean> = MutableStateFlow(false)
    private val selectedHeartRateStatId = MutableStateFlow<String?>(null)
    private val _viewData: MutableStateFlow<ChartViewData> = MutableStateFlow(ChartViewData.Initial)
    val viewData: StateFlow<ChartViewData> = _viewData.asStateFlow()

    private var showHighlightJob: Job? = null
    // Job for managing updateViewData operations to allow cancellation
    private var updateViewDataJob: Job? = null

    init {
        trackWidgetDetailPageExposure()
        
        combine(
            chartContent,
            chartStyle,
            chartGranularity,
            chartComparison,
            isWatchConnectedUseCase(),
            selectedHeartRateStatId,
            ::updateViewData
        )
            .flowOn(coroutinesDispatchers.io)
            .launchIn(viewModelScope)

        showExtraChartGranularitySelection
            .onEach { show ->
                _viewData.update { current ->
                    (current as? ChartViewData.Loaded)
                        ?.copy(showExtraChartGranularitySelection = show)
                        ?: current
                }
            }
            .launchIn(viewModelScope)
    }

    private fun calculateInitialPageIndex(): Int {
        val targetDate = savedStateHandle.get<LocalDate>(ChartActivity.KEY_TARGET_DATE) ?: LocalDate.now()
        val chartGranularity = savedStateHandle.get<ChartGranularity>(ChartActivity.KEY_CHART_GRANULARITY) 
            ?: return UNKNOWN_CHART_PAGE_INDEX
        
        return calculatePageIndexForDate(chartGranularity, targetDate)
    }

    private suspend fun updateViewData(
        chartContent: ChartContent = savedStateHandle.chartContent,
    ) {
        val currentGranularity = savedStateHandle.chartGranularity
        Log.d(TAG, "updateViewData(1) called - currentGranularity: $currentGranularity")

        // Cancel any previous updateViewData operation immediately to prevent
        // multiple concurrent operations when combine triggers rapidly
        updateViewDataJob?.cancel()
        Log.d(TAG, "updateViewData(1) - cancelled previous job")

        // We should use default arguments, but isWatchConnectedUseCase().first()
        // is a suspend function, so have to do this.
        updateViewData(
            chartContent = chartContent,
            chartStyle = savedStateHandle.chartStyle,
            chartGranularity = currentGranularity,
            chartComparison = savedStateHandle.chartComparison,
            isWatchConnected = isWatchConnectedUseCase().first(),
            selectedHeartRateStatId = selectedHeartRateStatId.value,
        )
    }

    private suspend fun updateViewData(
        chartContent: ChartContent,
        chartStyle: ChartStyle,
        chartGranularity: ChartGranularity,
        chartComparison: ChartComparison,
        isWatchConnected: Boolean,
        selectedHeartRateStatId: String? = null,
    ) {
        Log.d(TAG, "updateViewData(2) called - chartGranularity: $chartGranularity, current: ${savedStateHandle.chartGranularity}")

        // Cancel any previous updateViewData operation to prevent resource waste
        // and potential data inconsistency when chartGranularity changes rapidly
        updateViewDataJob?.cancel()
        Log.d(TAG, "updateViewData(2) - cancelled previous job")

        updateViewDataJob = viewModelScope.launch {
            Log.d(TAG, "updateViewData(2) - started new job for granularity: $chartGranularity")
            // Create a child coroutine scope for this update operation
            // This ensures all chart data loading operations can be cancelled together
            val updateScope = this

            val chartPageCount = calculateChartPageCount(chartGranularity)
            val sanitizedChartPageIndex = chartPageIndex
                .value
                .takeIf { it in 0..<chartPageCount }
                ?: (chartPageCount - 1)

            val style = ChartStyleViewData(
                currentChartStyle = chartStyle,
                availableChartStyles = chartContent.availableStyles.toImmutableList(),
            )

            val commuteTagsCreating = commuteTagsDataUseCase(chartContent = chartContent)

            val comparison = if (chartContent.supportsComparison(chartGranularity)) {
                when (chartComparison) {
                    ChartComparison.NONE -> ChartComparisonViewData.Off(
                        titleRes = chartContent.comparisonOffTitleRes(chartGranularity),
                        target = chartContent.targetChartComparison(chartGranularity),
                    )

                    ChartComparison.LAST_PERIOD -> ChartComparisonViewData.LastPeriod(
                        titleRes = chartContent.comparisonOnTitleRes(chartGranularity),
                        chartValueType = chartContent.valueTypeRes(chartGranularity),
                        chartTimeRange = createChartTimeRangeStringUseCase(
                            chartGranularity = chartGranularity,
                            chartPageIndex = sanitizedChartPageIndex - 1,
                            chartPageCount = chartPageCount,
                        ),
                        leftColorRes = chartContent.leftComparisonColorRes,
                        rightColorRes = chartContent.rightComparisonColorRes,
                    )

                    ChartComparison.RIGHT_AXIS -> ChartComparisonViewData.RightChart(
                        titleRes = chartContent.comparisonOnTitleRes(chartGranularity),
                        chartValueType = chartContent.valueTypeRes(chartGranularity),
                        rightChartValueType = chartContent.rightValueTypeRes(chartGranularity),
                        leftColorRes = chartContent.leftComparisonColorRes,
                        rightColorRes = chartContent.rightComparisonColorRes,
                    )
                }
            } else {
                ChartComparisonViewData.NotSupported
            }

            val dateRange = calculateDateRangeUseCase(
                chartGranularity = chartGranularity,
                chartPageIndex = sanitizedChartPageIndex,
                chartPageCount = chartPageCount,
            )

            val sleepViewData = createSleepViewDataUseCase(
                chartContent = chartContent,
                chartGranularity = chartGranularity,
                from = dateRange.start,
                to = dateRange.endInclusive,
            )

            val heartRateStatsViewData = getHeartRateStatsViewDataUseCase(
                chartContent = chartContent,
                chartGranularity = chartGranularity,
                from = dateRange.start,
                to = dateRange.endInclusive,
                selectedHeartRateStatId = selectedHeartRateStatId
            )
            // Double-check chartGranularity before updating UI to prevent stale data
            if (savedStateHandle.chartGranularity != chartGranularity) {
                Log.d(TAG, "updateViewData(2) - granularity changed during execution, aborting. Expected: $chartGranularity, Current: ${savedStateHandle.chartGranularity}")
                return@launch
            }
            Log.d(TAG, "updateViewData(2) - setting initial UI data for granularity: $chartGranularity")

            _viewData.value = ChartViewData.Loaded(
                chartStyle = style,
                chartGranularity = chartGranularity,
                chartComparison = comparison,
                mainChartGranularities = chartContent.mainChartGranularity.toImmutableList(),
                extraChartGranularities = chartContent.extraChartGranularity.toImmutableList(),
                showExtraChartGranularitySelection = showExtraChartGranularitySelection.value,
                chartContentTitle = chartContent.titleRes,
                chartValueType = chartContent.valueTypeRes(chartGranularity),
                chartTimeRange = createChartTimeRangeStringUseCase(
                    chartGranularity = chartGranularity,
                    chartPageIndex = sanitizedChartPageIndex,
                    chartPageCount = chartPageCount,
                ),
                scrollToCurrentPage = scrollToCurrentPage.value,
                currentChartPage = sanitizedChartPageIndex,
                chartPageCount = chartPageCount,
                chartData = emptyMap(),
                chartHighlight = ChartHighlightViewData.None,
                goal = GoalViewData.None,
                commuterTags = commuteTagsCreating,
                goalEditor = GoalEditorViewData.None,
                isWatchConnected = isWatchConnected,
                instructions = getInstructions(chartContent),
                sleepViewData = sleepViewData,
                heartRateStatsData = heartRateStatsViewData,
            )

            val chartData = ((sanitizedChartPageIndex - 1)..(sanitizedChartPageIndex + 1))
                .associateWith { pageIndex ->
                    createChartData(
                        coroutineScope = updateScope,
                        chartContent = chartContent,
                        chartStyle = chartStyle,
                        chartGranularity = chartGranularity,
                        chartComparison = chartComparison,
                        chartPageIndex = pageIndex,
                        chartPageCount = chartPageCount,
                        selectedHeartRateStatId = selectedHeartRateStatId,
                    )
                }
            val goalData = createGoalDataUseCase(
                chartContent = chartContent,
            )

            // Check if chartGranularity has changed during the execution of this coroutine
            // If it has changed, cancel this operation to prevent stale data from being applied
            if (savedStateHandle.chartGranularity != chartGranularity) {
                Log.d(TAG, "updateViewData(2) - granularity changed before final update, aborting. Expected: $chartGranularity, Current: ${savedStateHandle.chartGranularity}")
                return@launch
            }
            Log.d(TAG, "updateViewData(2) - applying chart data for granularity: $chartGranularity")

            _viewData.update { current ->
                // Final check before applying chart data to ensure consistency
                if (savedStateHandle.chartGranularity != chartGranularity) {
                    Log.d(TAG, "updateViewData(2) - final check failed, not updating. Expected: $chartGranularity, Current: ${savedStateHandle.chartGranularity}")
                    return@update current
                }

                Log.d(TAG, "updateViewData(2) - final update successful for granularity: $chartGranularity")
                (current as? ChartViewData.Loaded)
                    ?.copy(
                        chartData = chartData,
                        goal = goalData,
                    )
                    ?: current
            }
            Log.d(TAG, "updateViewData(2) - completed for granularity: $chartGranularity")
        }
    }

    private fun getInstructions(chartContent: ChartContent): ImmutableList<WidgetInstruction> {
        return getWidgetInstructionsUseCase(chartContent)
    }

    private suspend fun createChartData(
        coroutineScope: CoroutineScope,
        chartContent: ChartContent,
        chartStyle: ChartStyle,
        chartGranularity: ChartGranularity,
        chartComparison: ChartComparison,
        chartPageIndex: Int,
        chartPageCount: Int,
        selectedHeartRateStatId: String?,
    ): StateFlow<ChartData> {
        Log.d(TAG, "createChartData called - granularity: $chartGranularity, pageIndex: $chartPageIndex, current: ${savedStateHandle.chartGranularity}")

        if (chartPageIndex < 0 || chartPageIndex >= chartPageCount || savedStateHandle.chartGranularity != chartGranularity) {
            Log.d(TAG, "createChartData - returning empty data for granularity: $chartGranularity, pageIndex: $chartPageIndex")
            return MutableStateFlow(
                ChartData(
                    chartGranularity = chartGranularity,
                    series = persistentListOf(),
                    highlightEnabled = false,
                    goal = null,
                    highlightDecorationLines = persistentMapOf(),
                    currentValues = persistentListOf(),
                    chartContent = chartContent,
                    colorIndicator = null,
                )
            )
        }

        val sanitizedChartComparison = chartComparison
            .takeIf { chartContent.supportsComparison(chartGranularity) }
            ?: ChartComparison.NONE

        val dateRange = calculateDateRangeUseCase(
            chartGranularity = chartGranularity,
            chartPageIndex = chartPageIndex,
            chartPageCount = chartPageCount,
        )

        Log.d(TAG, "createChartData - creating StateFlow for granularity: $chartGranularity, pageIndex: $chartPageIndex")

        return createChartDataUseCase(
            coroutineScope = coroutineScope,
            chartContent = chartContent,
            chartStyle = chartStyle,
            chartGranularity = chartGranularity,
            chartComparison = sanitizedChartComparison,
            dateRange = dateRange,
            selectedHeartRateStatId = selectedHeartRateStatId
        )
            .filter { chartData ->
                // Only emit chart data if the granularity still matches current state
                // This prevents stale data from overriding current data when switching rapidly
                // Use the input granularity parameter instead of chartData.chartGranularity
                // because createChartDataUseCase may have bugs in setting chartGranularity field
                val isValid = chartGranularity == savedStateHandle.chartGranularity
                Log.d(TAG, "createChartData - filter check: input.granularity=$chartGranularity, chartData.granularity=${chartData.chartGranularity}, current=${savedStateHandle.chartGranularity}, isValid=$isValid, pageIndex=$chartPageIndex")
                isValid
            }
            .stateIn(
                scope = coroutineScope,
                started = SharingStarted.Lazily,
                initialValue = ChartData(
                    chartGranularity = chartGranularity,
                    series = persistentListOf(),
                    highlightEnabled = false,
                    goal = null,
                    highlightDecorationLines = persistentMapOf(),
                    currentValues = persistentListOf(),
                    chartContent = chartContent,
                    colorIndicator = null,
                )
            )
    }

    fun onViewEvent(event: ChartViewEvent) {
        when (event) {
            is ChartViewEvent.Close -> throw IllegalStateException("Should be handled in ChartActivity")
            is ChartViewEvent.ChartPageUpdated -> viewModelScope.launch {
                onCurrentPageIndexUpdated(event.currentPageIndex)
            }
            is ChartViewEvent.ShowExtraChartGranularitySelection ->
                showExtraChartGranularitySelection.value = true
            is ChartViewEvent.HideExtraChartGranularitySelection ->
                showExtraChartGranularitySelection.value = false
            is ChartViewEvent.UpdateChartGranularity -> {
                Log.d(TAG, "UpdateChartGranularity event - changing from ${savedStateHandle.chartGranularity} to ${event.chartGranularity}")
                chartPageIndex.value = UNKNOWN_CHART_PAGE_INDEX
                scrollToCurrentPage.value = true
                savedStateHandle[ChartActivity.KEY_CHART_GRANULARITY] = event.chartGranularity
                Log.d(TAG, "UpdateChartGranularity event - completed, new granularity: ${savedStateHandle.chartGranularity}")
            }
            is ChartViewEvent.UpdateChartStyle ->
                savedStateHandle[ChartActivity.KEY_CHART_STYLE] = event.chartStyle
            is ChartViewEvent.UpdateChartComparison ->
                savedStateHandle[ChartActivity.KEY_CHART_COMPARISON] = event.chartComparison
            is ChartViewEvent.ShowGoalEditor -> showGoalEditor()
            is ChartViewEvent.HideGoalEditor -> hideGoalEditor()
            is ChartViewEvent.UpdateGoal -> updateGoal(event)
            is ChartViewEvent.ShowHighlight -> showHighlight(event)
            is ChartViewEvent.HideHighlight -> hideHighlight()
            is ChartViewEvent.GoBackToCurrent -> viewModelScope.launch {
                chartPageIndex.value = UNKNOWN_CHART_PAGE_INDEX
                scrollToCurrentPage.value = true
                updateViewData()
            }
            is ChartViewEvent.ScrolledToCurrent -> viewModelScope.launch {
                scrollToCurrentPage.value = false
                updateViewData()
            }
            is ChartViewEvent.ShowSleepComparisonHighlight -> {
                showSleepComparisonHighlight(event.entryX)
            }
            ChartViewEvent.HideSleepComparisonHighlight -> {
                hideSleepComparisonHighlight()
            }
            is ChartViewEvent.UpdateSleepComparisonPrimaryGraphType -> {
                updateSleepComparisonGraphType(primaryGraphType = event.graphType)
            }
            is ChartViewEvent.UpdateSleepComparisonSecondaryGraphType -> {
                updateSleepComparisonGraphType(secondaryGraphType = event.graphType)
            }
            is ChartViewEvent.UpdateCommuteTags -> {
                updateAutoTagCommute(enabled = event.tagsOn)
            }
            ChartViewEvent.ShowSleepComparisonPrimaryGraphSelection -> {
                updateSleepComparisonGraphSelection(primaryVisible = true)
            }
            ChartViewEvent.ShowSleepComparisonSecondaryGraphSelection -> {
                updateSleepComparisonGraphSelection(secondaryVisible = true)
            }
            ChartViewEvent.HideSleepComparisonPrimaryGraphSelection,
            ChartViewEvent.HideSleepComparisonSecondaryGraphSelection -> {
                updateSleepComparisonGraphSelection()
            }
            is ChartViewEvent.HeartRateStatSelected -> {
                updateSelectedHeartRateStat(event.statId)
            }
        }
    }

    private fun showGoalEditor() {
        viewModelScope.launch {
            _viewData.update { current ->
                (current as? ChartViewData.Loaded)
                    ?.copy(
                        goalEditor = createGoalEditorDataUseCase(
                            chartContent = savedStateHandle.chartContent,
                        ),
                    )
                    ?: current
            }
        }
    }

    private fun hideGoalEditor() {
        _viewData.update { current ->
            (current as? ChartViewData.Loaded)
                ?.copy(
                    goalEditor = GoalEditorViewData.None,
                )
                ?: current
        }
    }

    private fun updateGoal(goal: ChartViewEvent.UpdateGoal) {
        viewModelScope.launch {
            val chartContent = savedStateHandle.chartContent
            updateGoalUseCase(
                chartContent = chartContent,
                goal = goal.goal,
            )

            updateViewData(chartContent = chartContent)
        }
    }

    private fun showHighlight(event: ChartViewEvent.ShowHighlight) {
        showHighlightJob?.cancel()
        showHighlightJob = viewModelScope.launch {
            _viewData.update { current ->
                val chartContent = savedStateHandle.chartContent
                val chartComparison = if (chartContent.supportsComparison(savedStateHandle.chartGranularity)) {
                    savedStateHandle.chartComparison
                } else {
                    ChartComparison.NONE
                }
                (current as? ChartViewData.Loaded)
                    ?.copy(
                        chartHighlight = getChartHighlightDataUseCase(
                            chartContent = chartContent,
                            chartComparison = chartComparison,
                            viewData = current,
                            entryX = event.entryX,
                            selectedHeartRateStatId = selectedHeartRateStatId.value
                        ),
                    )
                    ?: current
            }
        }
    }

    private fun hideHighlight() {
        showHighlightJob?.cancel()
        showHighlightJob = null

        _viewData.update { current ->
            (current as? ChartViewData.Loaded)
                ?.copy(
                    chartHighlight = ChartHighlightViewData.None,
                )
                ?: current
        }
    }

    private fun updateAutoTagCommute(enabled: Boolean) {
        viewModelScope.launch {
            val autoCommuteTaggingEnabled = userSettingsController.storeSettings(
                userSettingsController.settings.autoCommuteTaggingEnabled(enabled)
            ).autoCommuteTaggingEnabled
            _viewData.update { current ->
                (current as? ChartViewData.Loaded)?.let { loaded ->
                    loaded.copy(
                        commuterTags = when (loaded.commuterTags) {
                            is CommuteTagsViewData.CommuteTags -> {
                                CommuteTagsViewData.CommuteTags(
                                    title = loaded.commuterTags.title,
                                    tagsOn = autoCommuteTaggingEnabled,
                                )
                            }
                            CommuteTagsViewData.None -> CommuteTagsViewData.None
                        }
                    )
                } ?: current
            }
        }
    }

    private fun updateSleepComparisonGraphType(
        primaryGraphType: SleepComparisonGraphType? = null,
        secondaryGraphType: SleepComparisonGraphType? = null,
    ) {
        val chartGranularity = savedStateHandle.chartGranularity
        val currentPageIndex = (viewData.value as? ChartViewData.Loaded)?.currentChartPage ?: 0
        val chartPageCount = calculateChartPageCount(chartGranularity)

        val dateRange = calculateDateRangeUseCase(
            chartGranularity = chartGranularity,
            chartPageIndex = currentPageIndex,
            chartPageCount = chartPageCount,
        )

        val updatedSleepComparisonViewData = createSleepViewDataUseCase.updateComparisonViewData(
            chartGranularity = chartGranularity,
            from = dateRange.start,
            to = dateRange.endInclusive,
            primaryGraphType = primaryGraphType,
            secondaryGraphType = secondaryGraphType,
        )

        _viewData.update { current ->
            (current as? ChartViewData.Loaded)?.copy(
                sleepViewData = (current.sleepViewData as? SleepViewData.Loaded)?.copy(
                    comparisonViewData = updatedSleepComparisonViewData
                ) ?: SleepViewData.None
            ) ?: current
        }
    }

    private fun updateSleepComparisonGraphSelection(
        primaryVisible: Boolean = false,
        secondaryVisible: Boolean = false,
    ) = _viewData.update { current ->
        (current as? ChartViewData.Loaded)?.copy(
            sleepViewData = (current.sleepViewData as? SleepViewData.Loaded)?.copy(
                showPrimaryComparisonGraphSelection = primaryVisible,
                showSecondaryComparisonGraphSelection = secondaryVisible,
            ) ?: SleepViewData.None
        ) ?: current
    }

    private fun showSleepComparisonHighlight(entryX: Long) = _viewData.update { current ->
        (current as? ChartViewData.Loaded)?.copy(
            sleepViewData = (current.sleepViewData as? SleepViewData.Loaded)?.copy(
                comparisonHighlightViewData = createSleepViewDataUseCase.updateComparisonHighlightViewData(
                    current.sleepViewData.comparisonViewData, entryX,
                )
            ) ?: SleepViewData.None
        ) ?: current
    }

    private fun hideSleepComparisonHighlight() = _viewData.update { current ->
        (current as? ChartViewData.Loaded)?.copy(
            sleepViewData = (current.sleepViewData as? SleepViewData.Loaded)?.copy(
                comparisonHighlightViewData = SleepComparisonHighlightViewData.None
            ) ?: SleepViewData.None
        ) ?: current
    }

    private suspend fun onCurrentPageIndexUpdated(updatedPageIndex: Int) {
        val chartComparison = savedStateHandle.chartComparison
        val chartContent = savedStateHandle.chartContent
        val chartGranularity = savedStateHandle.chartGranularity
        val chartStyle = savedStateHandle.chartStyle

        val chartPageCount = calculateChartPageCount(chartGranularity)
        val sanitizedChartPageIndex = updatedPageIndex
            .takeIf { it in 0..<chartPageCount }
            ?: (chartPageCount - 1)

        chartPageIndex.value = sanitizedChartPageIndex

        val dateRange = calculateDateRangeUseCase(
            chartGranularity = chartGranularity,
            chartPageIndex = sanitizedChartPageIndex,
            chartPageCount = chartPageCount,
        )

        val heartRateStatsViewData = getHeartRateStatsViewDataUseCase(
            chartContent = chartContent,
            chartGranularity = chartGranularity,
            from = dateRange.start,
            to = dateRange.endInclusive,
            selectedHeartRateStatId = selectedHeartRateStatId.value,
        )

        val comparison = if (chartContent.supportsComparison(chartGranularity)) {
            when (chartComparison) {
                ChartComparison.NONE -> ChartComparisonViewData.Off(
                    titleRes = chartContent.comparisonOffTitleRes(chartGranularity),
                    target = chartContent.targetChartComparison(chartGranularity),
                )

                ChartComparison.LAST_PERIOD -> ChartComparisonViewData.LastPeriod(
                    titleRes = chartContent.comparisonOnTitleRes(chartGranularity),
                    chartValueType = chartContent.valueTypeRes(chartGranularity),
                    chartTimeRange = createChartTimeRangeStringUseCase(
                        chartGranularity = chartGranularity,
                        chartPageIndex = sanitizedChartPageIndex - 1,
                        chartPageCount = chartPageCount,
                    ),
                    leftColorRes = chartContent.leftComparisonColorRes,
                    rightColorRes = chartContent.rightComparisonColorRes,
                )

                ChartComparison.RIGHT_AXIS -> ChartComparisonViewData.RightChart(
                    titleRes = chartContent.comparisonOnTitleRes(chartGranularity),
                    chartValueType = chartContent.valueTypeRes(chartGranularity),
                    rightChartValueType = chartContent.rightValueTypeRes(chartGranularity),
                    leftColorRes = chartContent.leftComparisonColorRes,
                    rightColorRes = chartContent.rightComparisonColorRes,
                )
            }
        } else {
            ChartComparisonViewData.NotSupported
        }

        val sleepViewData = createSleepViewDataUseCase(
            chartContent = chartContent,
            chartGranularity = chartGranularity,
            from = dateRange.start,
            to = dateRange.endInclusive,
        )

        val chartTimeRange = createChartTimeRangeStringUseCase(
            chartGranularity = chartGranularity,
            chartPageIndex = sanitizedChartPageIndex,
            chartPageCount = chartPageCount,
        )

        _viewData.update { current ->
            (current as? ChartViewData.Loaded)?.let { loaded ->
                loaded.copy(
                    chartComparison = comparison,
                    chartTimeRange = chartTimeRange,
                    currentChartPage = sanitizedChartPageIndex,
                    heartRateStatsData = heartRateStatsViewData,
                    chartData = ((sanitizedChartPageIndex - 1)..(sanitizedChartPageIndex + 1))
                        .associateWith { pageIndex ->
                            loaded.chartData[pageIndex]
                                ?: createChartData(
                                    coroutineScope = viewModelScope,
                                    chartContent = chartContent,
                                    chartStyle = chartStyle,
                                    chartGranularity = chartGranularity,
                                    chartComparison = chartComparison,
                                    chartPageIndex = pageIndex,
                                    chartPageCount = chartPageCount,
                                    selectedHeartRateStatId = selectedHeartRateStatId.value,
                                )
                        },
                    sleepViewData = sleepViewData,
                )
            } ?: current
        }
    }

    private fun updateSelectedHeartRateStat(statId: String?) {
        selectedHeartRateStatId.value = statId
    }
    
    private fun trackWidgetDetailPageExposure() {
        viewModelScope.launch(coroutinesDispatchers.io) {
            val chartContent = savedStateHandle.get<ChartContent>(ChartActivity.KEY_CHART_CONTENT)
            val chartGranularity = savedStateHandle.get<ChartGranularity>(ChartActivity.KEY_CHART_GRANULARITY)
            val source = savedStateHandle.get<String>(ChartActivity.KEY_SOURCE) 
                ?: AnalyticsPropertyValue.WidgetDetailPageExposureSourceProperty.OTHER
            
            val widgetName = chartContent.toAnalyticsWidgetName()
            val timeDim = chartGranularity.toAnalyticsTimeDim()
            
            if (widgetName.isNotEmpty() && timeDim.isNotEmpty()) {
                eventTracker.trackEvent(
                    AnalyticsEvent.WIDGET_DETAIL_PAGE_EXPOSURE,
                    mapOf(
                        AnalyticsEventProperty.WIDGET_DETAIL_PAGE_EXPOSURE_WIDGET_NAME to widgetName,
                        AnalyticsEventProperty.WIDGET_DETAIL_PAGE_EXPOSURE_TIME_DIM to timeDim,
                        AnalyticsEventProperty.WIDGET_DETAIL_PAGE_EXPOSURE_SOURCE to source
                    )
                )
            }
        }
    }

    private companion object {
        const val TAG = "ChartViewModel_Debug"
        const val UNKNOWN_CHART_PAGE_INDEX: Int = -1

        private val EPOCH_OF_SPORTS_TRACKER: LocalDate = LocalDate.of(RELEASED_YEAR, 1, 1)

        val SavedStateHandle.chartComparison: ChartComparison get() =
            requireNotNull(get<ChartComparison>(ChartActivity.KEY_CHART_COMPARISON))

        val SavedStateHandle.chartContent: ChartContent get() =
            requireNotNull(get<ChartContent>(ChartActivity.KEY_CHART_CONTENT))

        val SavedStateHandle.chartGranularity: ChartGranularity get() =
            requireNotNull(get<ChartGranularity>(ChartActivity.KEY_CHART_GRANULARITY))

        val SavedStateHandle.chartStyle: ChartStyle get() =
            requireNotNull(get<ChartStyle>(ChartActivity.KEY_CHART_STYLE))

        fun calculateChartPageCount(chartGranularity: ChartGranularity): Int = when (chartGranularity) {
            ChartGranularity.DAILY -> ChronoUnit.DAYS.between(EPOCH_OF_SPORTS_TRACKER, LocalDate.now()).toInt() + 1
            ChartGranularity.WEEKLY -> ChronoUnit.WEEKS.between(EPOCH_OF_SPORTS_TRACKER, LocalDate.now()).toInt() + 1
            ChartGranularity.SEVEN_DAYS -> (ChronoUnit.DAYS.between(EPOCH_OF_SPORTS_TRACKER, LocalDate.now()).toInt() / 7) + 1
            ChartGranularity.MONTHLY -> ChronoUnit.MONTHS.between(EPOCH_OF_SPORTS_TRACKER, LocalDate.now()).toInt() + 1
            ChartGranularity.THIRTY_DAYS -> (ChronoUnit.DAYS.between(EPOCH_OF_SPORTS_TRACKER, LocalDate.now()).toInt() / 30) + 1
            ChartGranularity.SIXTY_DAYS -> (ChronoUnit.DAYS.between(EPOCH_OF_SPORTS_TRACKER, LocalDate.now()).toInt() / 60) + 1
            ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS -> (ChronoUnit.DAYS.between(EPOCH_OF_SPORTS_TRACKER, LocalDate.now()).toInt() / 180) + 1
            ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS -> (ChronoUnit.DAYS.between(EPOCH_OF_SPORTS_TRACKER, LocalDate.now()).toInt() / 365) + 1
            ChartGranularity.SIX_WEEKS -> (ChronoUnit.WEEKS.between(EPOCH_OF_SPORTS_TRACKER, LocalDate.now()).toInt() / 6) + 1
            ChartGranularity.SIX_MONTHS -> (ChronoUnit.MONTHS.between(EPOCH_OF_SPORTS_TRACKER, LocalDate.now()).toInt() / 6) + 1
            ChartGranularity.YEARLY -> Year.now().value - EPOCH_OF_SPORTS_TRACKER.year + 1
            ChartGranularity.EIGHT_YEARS -> (Year.now().value - EPOCH_OF_SPORTS_TRACKER.year) / 8 + 1
        }

        fun calculatePageIndexForDate(
            chartGranularity: ChartGranularity,
            targetDate: LocalDate
        ): Int {
            val totalPageCount = calculateChartPageCount(chartGranularity)
            val targetPageIndex = when (chartGranularity) {
                ChartGranularity.DAILY -> ChronoUnit.DAYS.between(EPOCH_OF_SPORTS_TRACKER, targetDate).toInt()
                ChartGranularity.WEEKLY -> ChronoUnit.WEEKS.between(EPOCH_OF_SPORTS_TRACKER, targetDate).toInt()
                ChartGranularity.SEVEN_DAYS -> (ChronoUnit.DAYS.between(EPOCH_OF_SPORTS_TRACKER, targetDate).toInt() / 7)
                ChartGranularity.MONTHLY -> ChronoUnit.MONTHS.between(EPOCH_OF_SPORTS_TRACKER, targetDate).toInt()
                ChartGranularity.THIRTY_DAYS -> (ChronoUnit.DAYS.between(EPOCH_OF_SPORTS_TRACKER, targetDate).toInt() / 30)
                ChartGranularity.SIXTY_DAYS -> (ChronoUnit.DAYS.between(EPOCH_OF_SPORTS_TRACKER, targetDate).toInt() / 60)
                ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS -> (ChronoUnit.DAYS.between(EPOCH_OF_SPORTS_TRACKER, targetDate).toInt() / 180)
                ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS -> (ChronoUnit.DAYS.between(EPOCH_OF_SPORTS_TRACKER, targetDate).toInt() / 365)
                ChartGranularity.SIX_WEEKS -> (ChronoUnit.WEEKS.between(EPOCH_OF_SPORTS_TRACKER, targetDate).toInt() / 6)
                ChartGranularity.SIX_MONTHS -> (ChronoUnit.MONTHS.between(EPOCH_OF_SPORTS_TRACKER, targetDate).toInt() / 6)
                ChartGranularity.YEARLY -> targetDate.year - EPOCH_OF_SPORTS_TRACKER.year
                ChartGranularity.EIGHT_YEARS -> (targetDate.year - EPOCH_OF_SPORTS_TRACKER.year) / 8
            }
            return targetPageIndex.coerceIn(0, totalPageCount - 1)
        }
    }
}

