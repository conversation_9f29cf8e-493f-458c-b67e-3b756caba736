package com.stt.android.chart.impl.screen.components

import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.stt.android.chart.api.model.ChartStyle
import com.stt.android.chart.impl.R
import com.stt.android.chart.impl.model.abbreviatedNameRes
import com.stt.android.chart.impl.screen.ChartViewData
import com.stt.android.chart.impl.screen.ChartViewEvent
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.ui.R as CommonR

@Composable
internal fun ChartSelection(
    viewData: ChartViewData.Loaded,
    onEvent: (ChartViewEvent) -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(
                horizontal = MaterialTheme.spacing.medium,
                vertical = MaterialTheme.spacing.small
            ),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        ChartGranularitySelection(
            viewData = viewData,
            onEvent = onEvent,
            modifier = Modifier.weight(1f)
        )

        ChartStyleSelection(
            viewData = viewData,
            onEvent = onEvent,
        )
    }
}

@Composable
private fun ChartGranularitySelection(
    viewData: ChartViewData.Loaded,
    onEvent: (ChartViewEvent) -> Unit,
    modifier: Modifier = Modifier,
) {
    val hasExtraChartGranularity = viewData.extraChartGranularities.isNotEmpty()

    Box(
        modifier = modifier
            .background(
                color = Color.White,
                shape = RoundedCornerShape(32.dp)
            )
            .height(36.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .border(
                    width = 1.dp,
                    color = MaterialTheme.colorScheme.lightGrey,
                    shape = RoundedCornerShape(32.dp)
                )
        )

        Row(
            modifier = Modifier
                .fillMaxSize(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            viewData.mainChartGranularities.forEachIndexed { index, chartGranularity ->
                val selected = viewData.chartGranularity == chartGranularity
                
                ChartSegment(
                    text = stringResource(chartGranularity.abbreviatedNameRes),
                    selected = selected,
                    onClick = { onEvent(ChartViewEvent.UpdateChartGranularity(chartGranularity)) },
                    modifier = Modifier
                        .fillMaxHeight()
                        .weight(1f)
                )
            }

            if (hasExtraChartGranularity) {
                val moreSelected = viewData.extraChartGranularities.contains(viewData.chartGranularity)
                
                ChartSegment(
                    text = stringResource(
                        id = if (moreSelected) {
                            viewData.chartGranularity.abbreviatedNameRes
                        } else {
                            R.string.chart_granularity_more
                        }
                    ),
                    selected = moreSelected,
                    onClick = { onEvent(ChartViewEvent.ShowExtraChartGranularitySelection) },
                    showIndicator = true,
                    modifier = Modifier
                        .fillMaxHeight()
                        .weight(1f)
                )
            }
        }
    }
}

@Composable
private fun ChartSegment(
    text: String,
    selected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    showIndicator: Boolean = false,
) {
    Row(
        modifier = modifier
            .clip(RoundedCornerShape(32.dp))
            .clickable(onClick = onClick)
            .background(if (selected) MaterialTheme.colorScheme.primary else Color.Transparent)
            .defaultMinSize(minHeight = 36.dp)
            .padding(horizontal = MaterialTheme.spacing.small),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.bodyMedium,
            color = if (selected) Color.White else MaterialTheme.colorScheme.onSurface,
            textAlign = TextAlign.Center,
            maxLines = 1,
        )

        if (showIndicator) {
            Spacer(modifier = Modifier.width(MaterialTheme.spacing.xsmall))
            Icon(
                painter = painterResource(CommonR.drawable.ic_arrow_down_18),
                contentDescription = null,
                modifier = Modifier.size(18.dp),
                tint = if (selected) Color.White else MaterialTheme.colorScheme.onSurface
            )
        }
    }
}

@Composable
private fun ChartStyleSelection(
    viewData: ChartViewData.Loaded,
    onEvent: (ChartViewEvent) -> Unit,
    modifier: Modifier = Modifier,
) {
     if (viewData.chartStyle.availableChartStyles.size <= 1) {
         return
     }

    Box(
        modifier = modifier
            .size(32.dp)
            .border(
                width = 1.dp,
                color = MaterialTheme.colorScheme.lightGrey,
                shape = CircleShape
            )
            .background(
                color = Color.White,
                shape = CircleShape
            )
            .clip(CircleShape)
            .clickable {
                val chartStyle = when (viewData.chartStyle.currentChartStyle) {
                    ChartStyle.SINGLE -> ChartStyle.CUMULATIVE
                    ChartStyle.CUMULATIVE -> ChartStyle.SINGLE
                }
                onEvent(ChartViewEvent.UpdateChartStyle(chartStyle))
            },
        contentAlignment = Alignment.Center
    ) {
        Icon(
            painter = painterResource(viewData.chartStyle.currentChartStyle.iconRes()),
            contentDescription = null,
            modifier = Modifier.size(18.dp),
            tint = MaterialTheme.colorScheme.nearBlack,
        )
    }
}

@DrawableRes
private fun ChartStyle.iconRes(): Int = when (this) {
    ChartStyle.SINGLE -> R.drawable.ic_bar_graph
    ChartStyle.CUMULATIVE -> R.drawable.ic_line_graph
}
