package com.stt.android.chart.impl

import android.content.Context
import com.stt.android.chart.api.ChartNavigator
import com.stt.android.chart.api.model.ChartComparison
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartStyle
import com.stt.android.chart.impl.screen.ChartActivity
import java.time.LocalDate
import javax.inject.Inject

class ChartNavigatorImpl @Inject constructor(): ChartNavigator {
    override fun openChartScreen(
        context: Context,
        chartContent: ChartContent,
        chartStyle: ChartStyle,
        chartGranularity: ChartGranularity,
        chartComparison: ChartComparison,
        targetDate: LocalDate,
        source: String,
    ) {
        context.startActivity(
            ChartActivity.newStartIntent(
                context = context,
                chartContent = chartContent,
                chartStyle = chartStyle,
                chartGranularity = chartGranularity,
                chartComparison = chartComparison,
                targetDate = targetDate,
                source = source,
            ),
        )
    }
}
