package com.stt.android.chart.impl.chart

import android.graphics.Paint
import android.graphics.Path
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import com.patrykandpatrick.vico.core.cartesian.CartesianDrawingContext
import com.patrykandpatrick.vico.core.cartesian.axis.Axis
import com.patrykandpatrick.vico.core.cartesian.marker.CartesianMarker
import com.patrykandpatrick.vico.core.common.component.LineComponent

class HighlightMarker(
    private val markerLine: LineComponent,
    var drawOverLayers: Boolean,
    private val offsetYDp: Float = 0f,
    private val drawTriangle: Boolean = false,
    private val triangleColor: Color = Color.Black,
    private val drawCircle: Boolean = false,
    private val circleEntryX: Double? = null,
    private val circleEntryY: Double? = null,
    private val circleColor: Color = Color.Black,
    private val verticalAxisPosition: Axis.Position.Vertical = Axis.Position.Vertical.End,
) : CartesianMarker {

    private val trianglePaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            style = Paint.Style.FILL
            color = triangleColor.toArgb()
        }
    }
    private val trianglePath by lazy { Path() }

    private val circlePaint by lazy { Paint(Paint.ANTI_ALIAS_FLAG) }

    override fun drawOverLayers(
        context: CartesianDrawingContext,
        targets: List<CartesianMarker.Target>,
    ) {
        if (!drawOverLayers) return
        val target = targets.firstOrNull() ?: return

        val top = -context.dpToPx(offsetYDp)

        if (drawTriangle) {
            context.drawHighlightTriangle(target.canvasX, top)
        }

        markerLine.drawVertical(
            context = context,
            x = target.canvasX,
            top = top,
            bottom = context.layerBounds.bottom,
        )

        if (drawCircle && circleEntryX == target.x && circleEntryY != null) {
            context.drawHighlightCircle(target.canvasX, circleEntryY)
        }
    }

    private fun CartesianDrawingContext.drawHighlightTriangle(canvasX: Float, top: Float) {
        val triangleWidth = 5f.pixels
        val triangleHeight = 4f.pixels
        trianglePath.reset()
        trianglePath.moveTo(canvasX - triangleWidth / 2f, top)
        trianglePath.lineTo(canvasX + triangleWidth / 2f, top)
        trianglePath.lineTo(canvasX, top + triangleHeight)
        trianglePath.close()
        canvas.drawPath(trianglePath, trianglePaint)
    }

    private fun CartesianDrawingContext.drawHighlightCircle(canvasX: Float, entryY: Double) {
        val yRange = ranges.getYRange(verticalAxisPosition)
        val canvasY = layerBounds.bottom - ((entryY - yRange.minY) / yRange.length).toFloat() * layerBounds.height()
        circlePaint.color = Color.White.toArgb()
        canvas.drawCircle(canvasX, canvasY, 6f.pixels, circlePaint)
        circlePaint.color = circleColor.toArgb()
        canvas.drawCircle(canvasX, canvasY, 4f.pixels, circlePaint)
    }
}
