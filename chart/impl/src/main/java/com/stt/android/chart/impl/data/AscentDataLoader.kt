package com.stt.android.chart.impl.data

import android.content.Context
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartGranularity.DAILY
import com.stt.android.chart.api.model.ChartGranularity.EIGHT_YEARS
import com.stt.android.chart.api.model.ChartGranularity.MONTHLY
import com.stt.android.chart.api.model.ChartGranularity.SEVEN_DAYS
import com.stt.android.chart.api.model.ChartGranularity.SIX_MONTHS
import com.stt.android.chart.api.model.ChartGranularity.SIX_WEEKS
import com.stt.android.chart.api.model.ChartGranularity.THIRTY_DAYS
import com.stt.android.chart.api.model.ChartGranularity.WEEKLY
import com.stt.android.chart.api.model.ChartGranularity.YEARLY
import com.stt.android.chart.api.model.ChartGranularity.SIXTY_DAYS
import com.stt.android.chart.api.model.ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS
import com.stt.android.chart.api.model.ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS
import com.stt.android.chart.api.model.ChartStyle
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.ChartType
import com.stt.android.chart.impl.model.epochMonth
import com.stt.android.chart.impl.screen.GoalEditorViewData
import com.stt.android.chart.impl.screen.GoalViewData
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.workouts.GetWorkoutHeadersForRangeUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.infomodel.shouldNotCountAscentForActivity
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.utils.atEndOfDay
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.time.LocalDate
import java.time.ZoneId
import java.time.temporal.TemporalAdjusters
import java.util.stream.Collectors
import javax.inject.Inject
import kotlin.math.max
import kotlin.math.roundToInt
import kotlin.math.ceil
import kotlin.math.floor
import kotlin.math.log10
import kotlin.math.pow
import com.stt.android.R as BaseR
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.sp
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.impl.model.ChartBarDisplayMode
import com.stt.android.chart.impl.model.LineChartConfig
import java.time.Instant
import java.time.temporal.ChronoUnit

internal class AscentDataLoader @Inject constructor(
    @ApplicationContext private val context: Context,
    private val userController: CurrentUserController,
    private val userSettingsController: UserSettingsController,
    private val getWorkoutHeadersForRangeUseCase: GetWorkoutHeadersForRangeUseCase,
    private val infoModelFormatter: InfoModelFormatter
) {
    fun loadGoalData(): GoalViewData = GoalViewData.None

    fun loadGoalEditorData(): GoalEditorViewData = GoalEditorViewData.None

    private fun adjustMaxY(maxY: Double): Double {
        if (maxY == 0.0) {
            return 300.0
        }
        val originalStep = maxY / 3.0
        val magnitude = if (originalStep > 0) {
            floor(log10(originalStep)).toInt()
        } else {
            0
        }
        val base = 10.0.pow(max(1, magnitude).toDouble())
        val adjustedStep = ceil(originalStep / base) * base
        return adjustedStep * 3.0
    }

    fun load(
        chartStyle: ChartStyle,
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        loadComparison: Boolean
    ): Flow<ChartData> = flow {
        val workoutHeaders = getWorkoutHeadersForRangeUseCase(
            GetWorkoutHeadersForRangeUseCase.Params(
                userController.username,
                null,
                from.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(),
                to.atEndOfDay().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()
            )
        )

        val series = mutableListOf<ChartData.Series>()

        val currentSeries = createAscentDataSeries(
            chartStyle = chartStyle,
            chartGranularity = chartGranularity,
            from = from,
            to = to,
            workoutHeaders = workoutHeaders
        )

        if (loadComparison) {
            val periodLength = ChronoUnit.DAYS.between(from, to) + 1
            val comparisonTo = from.minusDays(1)
            val comparisonFrom = comparisonTo.minusDays(periodLength - 1)
            
            val comparisonWorkoutHeaders = getWorkoutHeadersForRangeUseCase(
                GetWorkoutHeadersForRangeUseCase.Params(
                    userController.username,
                    null,
                    comparisonFrom.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(),
                    comparisonTo.atEndOfDay().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()
                )
            )
            
            val comparisonSeries = createComparisonDataSeries(
                chartStyle = chartStyle,
                chartGranularity = chartGranularity,
                currentDateRange = from..to,
                comparisonDateRange = comparisonFrom..comparisonTo,
                workoutHeaders = comparisonWorkoutHeaders,
                currentSeries = currentSeries
            )
            
            if (comparisonSeries != null) {
                val updatedCurrentSeries = currentSeries.copy(
                    axisRange = comparisonSeries.axisRange
                )
                series.add(updatedCurrentSeries)
                series.add(comparisonSeries)
            } else {
                series.add(currentSeries)
            }
        } else {
            series.add(currentSeries)
        }
        
        emit(
            ChartData(
                chartGranularity = chartGranularity,
                chartBarDisplayMode = ChartBarDisplayMode.GROUPED,
                series = series.toImmutableList(),
                highlightEnabled = true,
                goal = null,
                highlightDecorationLines = persistentMapOf(),
                currentValues = persistentListOf(),
                chartContent = ChartContent.ASCENT,
                colorIndicator = null,
            )
        )
    }

    private fun createAscentDataSeries(
        chartStyle: ChartStyle,
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        workoutHeaders: List<WorkoutHeader>,
        isComparison: Boolean = false
    ): ChartData.Series {
        val minX: Long
        val maxX: Long
        var maxY = 0.0
        var totalAscent = 0.0
        var monthsWithActivity = 0
        var yearsWithActivity = 0

        val filteredWorkoutHeaders = workoutHeaders.filterNot {
            shouldNotCountAscentForActivity(it.activityTypeId) 
        }

        val workoutsByDate = filteredWorkoutHeaders.groupBy { header ->
            LocalDate.ofInstant(
                Instant.ofEpochMilli(header.startTime),
                ZoneId.systemDefault()
            )
        }
        
        val entries:List<ChartData.Entry> = when (chartGranularity) {
            DAILY -> buildList {
                from.datesUntil(to.plusDays(1))
                    .collect(Collectors.toList())
                    .forEach { date ->
                        val dateWorkouts = workoutsByDate[date] ?: emptyList()
                        val dateAscent = dateWorkouts.sumOf {
                            infoModelFormatter.unit.toAltitudeUnit(it.totalAscent)
                        }
                        
                        totalAscent += dateAscent
                        val value = when (chartStyle) {
                            ChartStyle.SINGLE -> dateAscent
                            ChartStyle.CUMULATIVE -> totalAscent
                        }
                        maxY = max(maxY, value)
                        
                        val shouldAddEntry = when (chartStyle) {
                            ChartStyle.SINGLE -> dateAscent > 0
                            ChartStyle.CUMULATIVE -> !date.isAfter(LocalDate.now())
                        }
                        
                        if (shouldAddEntry) {
                            val entry = ChartData.Entry(
                                x = date.atStartOfDay().toEpochSecond(ZoneId.systemDefault().rules.getOffset(date.atStartOfDay())) / 60,
                                y = value
                            )
                            add(entry)
                        }
                    }
                
                minX = from.atStartOfDay().toEpochSecond(ZoneId.systemDefault().rules.getOffset(from.atStartOfDay())) / 60
                maxX = to.atEndOfDay().toEpochSecond(ZoneId.systemDefault().rules.getOffset(to.atEndOfDay())) / 60
            }
            
            WEEKLY, SEVEN_DAYS, MONTHLY, SIX_WEEKS -> buildList {
                from.datesUntil(to.plusDays(1))
                    .collect(Collectors.toList())
                    .forEach { date ->
                        val dateWorkouts = workoutsByDate[date] ?: emptyList()
                        val dateAscent = dateWorkouts.sumOf {
                            infoModelFormatter.unit.toAltitudeUnit(it.totalAscent)
                        }
                        
                        totalAscent += dateAscent
                        val value = when (chartStyle) {
                            ChartStyle.SINGLE -> dateAscent
                            ChartStyle.CUMULATIVE -> totalAscent
                        }
                        maxY = max(maxY, value)
                        
                        val shouldAddEntry = when (chartStyle) {
                            ChartStyle.SINGLE -> dateAscent > 0
                            ChartStyle.CUMULATIVE -> !date.isAfter(LocalDate.now())
                        }
                        
                        if (shouldAddEntry) {
                            val entry = ChartData.Entry(
                                x = date.toEpochDay(),
                                y = value
                            )
                            add(entry)
                        }
                    }
                
                minX = from.toEpochDay()
                maxX = to.toEpochDay()
            }
            
            SIX_MONTHS -> buildList {
                val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
                
                val weeklyData = mutableMapOf<LocalDate, Double>()
                from.datesUntil(to.plusDays(1))
                    .collect(Collectors.toList())
                    .forEach { date ->
                        val startOfWeek = date.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
                        val dateWorkouts = workoutsByDate[date] ?: emptyList()
                        val dateAscent = dateWorkouts.sumOf {
                            infoModelFormatter.unit.toAltitudeUnit(it.totalAscent)
                        }
                        
                        weeklyData[startOfWeek] = (weeklyData[startOfWeek] ?: 0.0) + dateAscent
                    }
                
                var cumulativeAscent = 0.0
                weeklyData.entries.sortedBy { it.key }.forEach { (startOfWeek, weekAscent) ->
                    cumulativeAscent += weekAscent
                    
                    val value = when (chartStyle) {
                        ChartStyle.SINGLE -> weekAscent
                        ChartStyle.CUMULATIVE -> cumulativeAscent
                    }
                    maxY = max(maxY, value)
                    
                    val shouldAddEntry = when (chartStyle) {
                        ChartStyle.SINGLE -> weekAscent > 0
                        ChartStyle.CUMULATIVE -> !startOfWeek.isAfter(LocalDate.now())
                    }
                    
                    if (shouldAddEntry) {
                        val entry = ChartData.Entry(
                            x = startOfWeek.toEpochDay(),
                            y = value
                        )
                        add(entry)
                    }
                }
                
                totalAscent = cumulativeAscent
                minX = from.toEpochDay()
                maxX = to.toEpochDay()
            }
            
            YEARLY -> buildList {
                val monthlyData = mutableMapOf<Int, Double>()
                from.datesUntil(to.plusDays(1))
                    .collect(Collectors.toList())
                    .forEach { date ->
                        val epochMonth = date.epochMonth
                        val dateWorkouts = workoutsByDate[date] ?: emptyList()
                        val dateAscent = dateWorkouts.sumOf {
                            infoModelFormatter.unit.toAltitudeUnit(it.totalAscent)
                        }
                        
                        monthlyData[epochMonth] = (monthlyData[epochMonth] ?: 0.0) + dateAscent
                    }
                
                var cumulativeAscent = 0.0
                monthlyData.entries.sortedBy { it.key }.forEach { (epochMonth, monthAscent) ->
                    cumulativeAscent += monthAscent
                    
                    val value = when (chartStyle) {
                        ChartStyle.SINGLE -> monthAscent
                        ChartStyle.CUMULATIVE -> cumulativeAscent
                    }
                    maxY = max(maxY, value)
                    
                    val shouldAddEntry = when (chartStyle) {
                        ChartStyle.SINGLE -> monthAscent > 0
                        ChartStyle.CUMULATIVE -> epochMonth <= LocalDate.now().epochMonth
                    }
                    
                    if (shouldAddEntry) {
                        val entry = ChartData.Entry(
                            x = epochMonth.toLong(),
                            y = value
                        )
                        add(entry)
                    }
                    
                    if (monthAscent > 0) {
                        monthsWithActivity++
                    }
                }
                
                totalAscent = cumulativeAscent
                minX = from.epochMonth.toLong()
                maxX = to.epochMonth.toLong()
            }
            
            EIGHT_YEARS -> buildList {
                val yearlyData = mutableMapOf<Int, Double>()
                val yearlyDayCount = mutableMapOf<Int, Int>()
                
                from.datesUntil(to.plusDays(1))
                    .collect(Collectors.toList())
                    .forEach { date ->
                        val year = date.year
                        val dateWorkouts = workoutsByDate[date] ?: emptyList()
                        val dateAscent = dateWorkouts.sumOf {
                            infoModelFormatter.unit.toAltitudeUnit(it.totalAscent)
                        }
                        
                        yearlyData[year] = (yearlyData[year] ?: 0.0) + dateAscent
                        
                        if (dateAscent > 0) {
                            yearlyDayCount[year] = (yearlyDayCount[year] ?: 0) + 1
                        }
                    }
                
                var cumulativeAscent = 0.0
                yearlyData.entries.sortedBy { it.key }.forEach { (year, yearAscent) ->
                    cumulativeAscent += yearAscent
                    
                    val value = when (chartStyle) {
                        ChartStyle.SINGLE -> {
                            maxY = max(maxY, yearAscent)
                            yearAscent
                        }
                        ChartStyle.CUMULATIVE -> {
                            maxY = max(maxY, cumulativeAscent)
                            cumulativeAscent
                        }
                    }
                    
                    val shouldAddEntry = when (chartStyle) {
                        ChartStyle.SINGLE -> yearAscent > 0
                        ChartStyle.CUMULATIVE -> year <= LocalDate.now().year
                    }
                    
                    if (shouldAddEntry) {
                        val entry = ChartData.Entry(
                            x = year.toLong(),
                            y = value
                        )
                        add(entry)
                    }
                    
                    if (yearAscent > 0) {
                        yearsWithActivity++
                    }
                }
                
                totalAscent = cumulativeAscent
                minX = from.year.toLong()
                maxX = to.year.toLong()
            }
            
            THIRTY_DAYS -> buildList {
                from.datesUntil(to.plusDays(1))
                    .collect(Collectors.toList())
                    .forEach { date ->
                        val dateWorkouts = workoutsByDate[date] ?: emptyList()
                        val dateAscent = dateWorkouts.sumOf {
                            infoModelFormatter.unit.toAltitudeUnit(it.totalAscent)
                        }
                        
                        totalAscent += dateAscent
                        val value = when (chartStyle) {
                            ChartStyle.SINGLE -> dateAscent
                            ChartStyle.CUMULATIVE -> totalAscent
                        }
                        maxY = max(maxY, value)
                        
                        val shouldAddEntry = when (chartStyle) {
                            ChartStyle.SINGLE -> dateAscent > 0
                            ChartStyle.CUMULATIVE -> !date.isAfter(LocalDate.now())
                        }
                        
                        if (shouldAddEntry) {
                            val entry = ChartData.Entry(
                                x = date.toEpochDay(),
                                y = value
                            )
                            add(entry)
                        }
                    }
                
                minX = from.toEpochDay()
                maxX = to.toEpochDay()
            }
            SIXTY_DAYS,
            ONE_HUNDRED_EIGHTY_DAYS,
            THREE_HUNDRED_SIXTY_FIVE_DAYS ->  throw IllegalArgumentException("this $chartGranularity is not supported for ascent data")
        }

        maxY = adjustMaxY(maxY)

        val formattedTotalAscent = formatValueUnit(totalAscent).let { (value, unit) ->
            generateWidgetTitle(value, unit)
        }

        val color = if (isComparison) {
            context.getColor(BaseR.color.medium_grey)
        } else {
            context.getColor(BaseR.color.dashboard_widget_max_vo2)
        }

        val displayValue = when (chartGranularity) {
            EIGHT_YEARS -> calculateYearlyAverageAscent(totalAscent, yearsWithActivity)
            YEARLY -> calculateMonthlyAverageAscent(totalAscent, monthsWithActivity)
            else -> formattedTotalAscent
        }

        return ChartData.Series(
            chartType = when (chartStyle) {
                ChartStyle.SINGLE -> ChartType.BAR
                ChartStyle.CUMULATIVE -> ChartType.LINE
            },
            color = color,
            axisRange = ChartData.AxisRange(
                minX = minX.toDouble(),
                maxX = maxX.toDouble(),
                minY = 0.0,
                maxY = maxY
            ),
            entries = entries.toImmutableList(),
            value = displayValue,
            candlestickEntries = persistentListOf(),
            lineConfig = LineChartConfig(
                showAreaFill = !isComparison
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
        )
    }

    private fun calculateMonthlyAverageAscent(
        totalAscent: Double,
        monthsWithActivity: Int
    ): AnnotatedString {
        return if (monthsWithActivity > 0) {
            val avgMonthlyAscent = totalAscent / monthsWithActivity
            formatValueUnit(avgMonthlyAscent).let { (value, unit) ->
                generateWidgetTitle(value, unit)
            }
        } else {
            formatValueUnit(totalAscent).let { (value, unit) ->
                generateWidgetTitle(value, unit)
            }
        }
    }

    private fun calculateYearlyAverageAscent(
        totalAscent: Double,
        yearsWithActivity: Int
    ): AnnotatedString {
        return if (yearsWithActivity > 0) {
            val avgYearlyAscent = totalAscent / yearsWithActivity
            formatValueUnit(avgYearlyAscent).let { (value, unit) ->
                generateWidgetTitle(value, unit)
            }
        } else {
            formatValueUnit(totalAscent).let { (value, unit) ->
                generateWidgetTitle(value, unit)
            }
        }
    }

    private fun formatValueUnit(value: Double): Pair<String, String> {
        var formattedAscent = infoModelFormatter.unit.toAltitudeUnit(value)
        var distanceUnit = infoModelFormatter.unit.altitudeUnit
        if (formattedAscent.roundToInt() >= 1_000_000) {
            formattedAscent = infoModelFormatter.unit.toAltitudeUnit(value)
            distanceUnit = infoModelFormatter.unit.altitudeUnit
        }
        return "${formattedAscent.roundToInt()}" to context.getString(distanceUnit)
    }

    private fun generateWidgetTitle(value: String, unit: String): AnnotatedString {
        return buildAnnotatedString {
            append(value)
            withStyle(SpanStyle(fontSize = 12.sp)) {
                append(" ")
                append(unit)
            }
        }
    }
    
    fun formatHighlightData(value: Number?): String {
        if (value == null) {
            return "-"
        }
        return formatValueUnit(value.toDouble()).let { (formattedValue, unit) ->
            "$formattedValue $unit"
        }
    }

    private fun createComparisonDataSeries(
        chartStyle: ChartStyle,
        chartGranularity: ChartGranularity,
        currentDateRange: ClosedRange<LocalDate>,
        comparisonDateRange: ClosedRange<LocalDate>,
        workoutHeaders: List<WorkoutHeader>,
        currentSeries: ChartData.Series
    ): ChartData.Series? {
        if (workoutHeaders.isEmpty()) {
            return null
        }

        val created = createAscentDataSeries(
            chartStyle = chartStyle,
            chartGranularity = chartGranularity,
            from = comparisonDateRange.start,
            to = comparisonDateRange.endInclusive,
            workoutHeaders = workoutHeaders,
            isComparison = true
        )

        val xOffset = when (chartGranularity) {
            DAILY -> {
                val currentStart = currentDateRange.start.atStartOfDay().toEpochSecond(ZoneId.systemDefault().rules.getOffset(currentDateRange.start.atStartOfDay())) / 60
                val comparisonStart = comparisonDateRange.start.atStartOfDay().toEpochSecond(ZoneId.systemDefault().rules.getOffset(comparisonDateRange.start.atStartOfDay())) / 60
                currentStart - comparisonStart
            }
            
            WEEKLY,
            SEVEN_DAYS,
            MONTHLY,
            THIRTY_DAYS,
            SIX_WEEKS,
            SIX_MONTHS -> ChronoUnit.DAYS.between(
                comparisonDateRange.start,
                currentDateRange.start
            )

            YEARLY -> ChronoUnit.MONTHS.between(
                comparisonDateRange.start,
                currentDateRange.start
            )
            
            EIGHT_YEARS -> ChronoUnit.YEARS.between(
                comparisonDateRange.start,
                currentDateRange.start
            )

            SIXTY_DAYS,
            ONE_HUNDRED_EIGHTY_DAYS,
            THREE_HUNDRED_SIXTY_FIVE_DAYS -> throw IllegalArgumentException("Unsupported granularity: $chartGranularity")
        }

        val updatedEntries = created.entries
            .map { entry ->
                entry.copy(x = entry.x + xOffset)
            }.toImmutableList()

        val currentMaxY = currentSeries.axisRange.maxY
        val comparisonMaxY = adjustMaxY(created.axisRange.maxY)
        val finalMaxY = max(currentMaxY, comparisonMaxY)

        return created.copy(
            axisRange = ChartData.AxisRange(
                minX = currentSeries.axisRange.minX,
                maxX = currentSeries.axisRange.maxX,
                minY = 0.0,
                maxY = finalMaxY
            ),
            entries = updatedEntries,
        )
    }
} 
