package com.stt.android.chart.impl.usecases

import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.impl.R
import com.stt.android.chart.impl.model.NestedListItem
import com.stt.android.chart.impl.model.NestedListSubItem
import com.stt.android.chart.impl.model.WidgetInstruction
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import javax.inject.Inject

internal class GetWidgetInstructionsUseCase @Inject constructor(
) {

    operator fun invoke(chartContent: ChartContent): ImmutableList<WidgetInstruction> {
        return when (chartContent) {
            ChartContent.CALORIES -> persistentListOf(
                WidgetInstruction(
                    titleRes = R.string.about_calories,
                    contentRes = R.string.calories_instruction,
                )
            )
            
            ChartContent.MINIMUM_HEART_RATE -> persistentListOf(
                WidgetInstruction(
                    titleRes = R.string.about_minimum_hr_title,
                    contentRes = R.string.about_minimum_hr_description,
                )
            )
            
            ChartContent.SLEEPING_MINIMUM_HEART_RATE -> persistentListOf(
                WidgetInstruction(
                    titleRes = R.string.about_sleep_minimum_hr_title,
                    contentRes = R.string.about_sleep_minimum_hr_description,
                )
            )
            ChartContent.SLEEP,
            ChartContent.STEPS,
            ChartContent.DURATION,
            ChartContent.ASCENT -> persistentListOf()
            ChartContent.HEART_RATE -> persistentListOf(
                WidgetInstruction(
                    titleRes = R.string.about_heart_rate_title,
                    contentRes = R.string.about_heart_rate_description,
                )
            )
            ChartContent.RESTING_HEART_RATE -> persistentListOf(
                WidgetInstruction(
                    titleRes = R.string.about_resting_heart_rate_title,
                    contentRes = R.string.about_resting_heart_rate_description,
                )
            )
            ChartContent.COMMUTE -> persistentListOf(
                WidgetInstruction(
                    titleRes = R.string.about_commute,
                    contentRes = R.string.commute_instruction,
                )
            )
            ChartContent.TSS -> persistentListOf(
                WidgetInstruction(
                    titleRes = R.string.about_tss,
                    contentRes = R.string.tss_instruction,
                )
            )
            ChartContent.RESOURCES -> persistentListOf(
                WidgetInstruction(
                    titleRes = R.string.about_resources_title,
                    contentRes = R.string.about_resources_description,
                ),

                WidgetInstruction(
                    titleRes = R.string.how_to_read_data,
                    contentRes = R.string.resource_states_intro,
                    items = persistentListOf(
                        NestedListItem(
                            titleRes = R.string.recovering,
                            descriptionRes = R.string.resources_rise_quickly,
                            subItems = persistentListOf(
                                NestedListSubItem(
                                    prefixRes = R.string.scenario_prefix,
                                    contentRes = R.string.recovering_scenario,
                                )
                            )
                        ),
                        NestedListItem(
                            titleRes = R.string.inactive,
                            descriptionRes = R.string.resources_change_slowly,
                            subItems = persistentListOf(
                                NestedListSubItem(
                                    prefixRes = R.string.during_sleep_prefix,
                                    contentRes = R.string.inactive_sleep_desc,
                                ),
                                NestedListSubItem(
                                    prefixRes = R.string.during_wakefulness_prefix,
                                    contentRes = R.string.inactive_wakefulness_desc,
                                )
                            )
                        ),
                        NestedListItem(
                            titleRes = R.string.active,
                            descriptionRes = R.string.resources_decline,
                            subItems = persistentListOf(
                                NestedListSubItem(
                                    prefixRes = R.string.scenario_prefix,
                                    contentRes = R.string.active_scenario,
                                )
                            )
                        ),
                        NestedListItem(
                            titleRes = R.string.stressed,
                            descriptionRes = R.string.resources_drop_rapidly,
                            subItems = persistentListOf(
                                NestedListSubItem(
                                    prefixRes = R.string.scenario_prefix,
                                    contentRes = R.string.stressed_scenario,
                                )
                            )
                        )
                    )
                ),

                WidgetInstruction(
                    titleRes = R.string.how_resources_measured,
                    contentRes = R.string.resources_measured_based,
                    items = persistentListOf(
                        NestedListItem(
                            titleRes = R.string.active_state,
                            descriptionRes = R.string.active_state_desc,
                            subItems = persistentListOf(),
                        ),
                        NestedListItem(
                            titleRes = R.string.inactive_state,
                            descriptionRes = R.string.inactive_state_desc,
                            subItems = persistentListOf(),
                        )
                    )
                )
            )

            ChartContent.HRV -> persistentListOf(
                WidgetInstruction(
                    titleRes = R.string.about_hrv_title,
                    contentRes = R.string.about_hrv_description,
                ),
                WidgetInstruction(
                    titleRes = R.string.how_to_read_hrv_data_title,
                    contentRes = R.string.how_to_read_hrv_data_description,
                ),
                WidgetInstruction(
                    titleRes = R.string.how_to_measure_hrv_title,
                    contentRes = null,
                    items = persistentListOf(
                        NestedListItem(
                            titleRes = null,
                            needPadding = true,
                            descriptionRes = R.string.how_to_measure_hrv_description,
                            sameTitleStyle = true,
                        ),
                        NestedListItem(
                            titleRes = null,
                            needPadding = true,
                            descriptionRes = R.string.hrv_values_explained_title,
                            sameTitleStyle = true,
                            subItems = persistentListOf(
                                NestedListSubItem(
                                    prefixRes = null,
                                    needPadding = true,
                                    contentRes = R.string.hrv_todays_value_explained
                                ),
                                NestedListSubItem(
                                    prefixRes = null,
                                    needPadding = true,
                                    contentRes = R.string.hrv_seven_day_average_explained
                                ),
                                NestedListSubItem(
                                    prefixRes = null,
                                    needPadding = true,
                                    contentRes = R.string.hrv_normal_range_explained
                                )
                            )
                        )
                    )
                )
            )
            
            ChartContent.VO2MAX -> persistentListOf(
                WidgetInstruction(
                    titleRes = R.string.about_vo2max_title,
                    contentRes = R.string.about_vo2max_description,
                ),
                WidgetInstruction(
                    titleRes = R.string.how_to_get_vo2max_title,
                    contentRes = R.string.how_to_get_vo2max_description_v2,
                ),
                WidgetInstruction(
                    titleRes = R.string.how_to_use_vo2max_title,
                    contentRes = null,
                    items = persistentListOf(
                        NestedListItem(
                            titleRes = R.string.vo2max_health_indicator_title,
                            descriptionRes = R.string.vo2max_health_indicator_description,
                            subItems = persistentListOf(),
                            singleLine = false,
                        ),
                        NestedListItem(
                            titleRes = R.string.vo2max_benchmark_comparison_title,
                            descriptionRes = R.string.vo2max_benchmark_comparison_description,
                            subItems = persistentListOf(),
                            singleLine = false,
                        ),
                        NestedListItem(
                            titleRes = R.string.vo2max_long_term_gauge_title,
                            descriptionRes = R.string.vo2max_long_term_gauge_description,
                            singleLine = false,
                            subItems = persistentListOf(),
                        )
                    )
                )
            )
        }
    }
} 
