package com.stt.android.chart.impl.screen

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.stt.android.chart.impl.screen.components.BackToCurrentButton
import com.stt.android.chart.impl.screen.components.ChartHighlight
import com.stt.android.chart.impl.screen.components.ChartPager
import com.stt.android.chart.impl.screen.components.ChartScreenTopBar
import com.stt.android.chart.impl.screen.components.ChartSelection
import com.stt.android.chart.impl.screen.components.ChartSummary
import com.stt.android.chart.impl.screen.components.CommuterTags
import com.stt.android.chart.impl.screen.components.CurrentValues
import com.stt.android.chart.impl.screen.components.ExtraChartGranularitySelection
import com.stt.android.chart.impl.screen.components.GoalEditor
import com.stt.android.chart.impl.screen.components.GoalSection
import com.stt.android.chart.impl.screen.components.HeartRateStatsSelector
import com.stt.android.chart.impl.screen.components.WidgetInstructions
import com.stt.android.chart.impl.screen.components.sleep.SleepComparisonGraphSelectionBottomSheet
import com.stt.android.chart.impl.screen.components.sleep.SleepSection
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.spacing

@Composable
internal fun ChartLoadedScreen(
    viewData: ChartViewData.Loaded,
    onEvent: (ChartViewEvent) -> Unit,
    modifier: Modifier = Modifier,
) {
    val scrollState = rememberScrollState()
    var isReturnToCurrentButtonVisible by remember { mutableStateOf(false) }

    Scaffold(
        topBar = {
            ChartScreenTopBar(
                title = viewData.chartContentTitle,
                onEvent = onEvent,
            )
        },
        modifier = modifier,
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .narrowContent()
                .background(color = MaterialTheme.colorScheme.surface)
                .padding(paddingValues)
        ) {
            Column {
                ChartSelection(
                    viewData = viewData,
                    onEvent = onEvent,
                )

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .verticalScroll(scrollState)
                        .padding(bottom = if (isReturnToCurrentButtonVisible) 112.dp else 0.dp),
                ){
                    Column(
                        modifier = Modifier.padding(
                            vertical = MaterialTheme.spacing.small,
                            horizontal = MaterialTheme.spacing.medium,
                        ),
                    ) {
                        Box {
                            ChartSummary(
                                viewData = viewData,
                                onEvent = onEvent,
                                modifier = Modifier
                                    .padding(bottom = MaterialTheme.spacing.medium),
                            )

                            ChartHighlight(
                                viewData = viewData.chartHighlight,
                                modifier = Modifier
                                    .align(Alignment.BottomCenter),
                            )
                        }

                        ChartPager(
                            viewData = viewData,
                            onEvent = onEvent,
                        )
                    }

                    CurrentValues(
                        viewData = viewData,
                        modifier = Modifier.padding(
                            vertical = MaterialTheme.spacing.small,
                            horizontal = MaterialTheme.spacing.medium,
                        ),
                    )

                    CommuterTags(
                        viewData = viewData,
                        onEvent = onEvent,
                        modifier = Modifier.padding(
                            vertical = MaterialTheme.spacing.small,
                            horizontal = MaterialTheme.spacing.medium,
                        ),
                    )

                    SleepSection(
                        viewData = viewData.sleepViewData,
                        chartTimeRange = viewData.chartTimeRange,
                        onEvent = onEvent,
                        modifier = Modifier.padding(
                            vertical = MaterialTheme.spacing.small,
                            horizontal = MaterialTheme.spacing.medium,
                        ),
                    )

                    GoalSection(
                        viewData = viewData,
                        onEvent = onEvent,
                        modifier = Modifier.padding(
                            vertical = MaterialTheme.spacing.small,
                            horizontal = MaterialTheme.spacing.medium,
                        ),
                    )

                    HeartRateStatsSelector(
                        viewData = viewData.heartRateStatsData,
                        onEvent = onEvent,
                        modifier = Modifier.padding(
                            vertical = MaterialTheme.spacing.small,
                            horizontal = MaterialTheme.spacing.medium,
                        ),
                    )

                    WidgetInstructions(
                        instructions = viewData.instructions,
                        scrollState = scrollState,
                        modifier = Modifier.padding(
                            vertical = MaterialTheme.spacing.small,
                            horizontal = MaterialTheme.spacing.medium,
                        ),
                    )
                }
            }

            ExtraChartGranularitySelection(
                viewData = viewData,
                onEvent = onEvent,
            )

            SleepComparisonGraphSelectionBottomSheet(
                viewData = viewData.sleepViewData,
                onEvent = onEvent,
            )

            GoalEditor(
                viewData = viewData,
                onEvent = onEvent,
            )

            isReturnToCurrentButtonVisible = viewData.currentChartPage < viewData.chartPageCount - 1
            AnimatedVisibility(
                visible = isReturnToCurrentButtonVisible,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = MaterialTheme.spacing.xxxlarge),
                enter = slideInVertically(initialOffsetY = { it * 2 }),
                exit = slideOutVertically(targetOffsetY = { it * 2 }),
            ) {
                BackToCurrentButton(
                    onEvent = onEvent,
                )
            }
        }
    }
}
