package com.stt.android.chart.impl.screen.components.sleep

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.InlineTextContent
import androidx.compose.foundation.text.appendInlineContent
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.Placeholder
import androidx.compose.ui.text.PlaceholderVerticalAlign
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.stt.android.chart.impl.R
import com.stt.android.chart.impl.model.DailySleepDurationData
import com.stt.android.chart.impl.model.DailySleepQualityData
import com.stt.android.chart.impl.model.DailySleepResourcesData
import com.stt.android.chart.impl.model.formatDuration
import com.stt.android.chart.impl.model.formatPercent
import com.stt.android.chart.impl.model.secondsToHourMinute
import com.stt.android.chart.impl.screen.DailySleepMetricsViewData
import com.stt.android.chart.impl.screen.SleepViewData
import com.stt.android.compose.theme.activityCycling
import com.stt.android.compose.theme.activityRecovery
import com.stt.android.compose.theme.activitySleep
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyBold
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.material3.bodyMegaBold
import com.stt.android.compose.theme.material3.bodySmallBold
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.spacing
import java.util.Locale
import kotlin.math.abs
import kotlin.math.roundToInt
import kotlin.time.Duration
import com.stt.android.core.R as CR

@Composable
internal fun DailySleepMetrics(
    viewData: SleepViewData.Loaded,
    date: String,
    modifier: Modifier = Modifier,
) {
    val metricsViewData = viewData.dailyMetricsViewData
    if (metricsViewData !is DailySleepMetricsViewData.Loaded) return

    val data = metricsViewData.dailySleepMetrics.collectAsState(null).value ?: return

    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(top = MaterialTheme.spacing.medium),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
    ) {
        data.sleepQuality?.let { sleepQuality ->
            DailySleepQuality(data = sleepQuality)
        }
        data.sleepDuration?.let { sleepDuration ->
            DailySleepDuration(
                data = sleepDuration,
                date = date,
            )
        }
        data.resources?.let { resources ->
            DailySleepResources(data = resources)
        }
    }
}

@Composable
private fun DailySleepQuality(
    data: DailySleepQualityData,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier.fillMaxWidth()) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Icon(
                painter = painterResource(R.drawable.ic_sleep_quality),
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurface,
            )
            Text(
                text = data.quality.times(100).formatPercent(),
                style = MaterialTheme.typography.bodyMegaBold,
                color = MaterialTheme.colorScheme.activitySleep,
            )
        }
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        Text(
            text = stringResource(R.string.sleep_quality_title),
            style = MaterialTheme.typography.bodyXLargeBold,
            color = MaterialTheme.colorScheme.onSurface,
        )
        data.qualityDesc?.let {
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
            Text(
                text = it,
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.onSurface,
            )
        }
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .defaultMinSize(minHeight = 45.dp),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            NameValueColumn(
                modifier = Modifier.weight(1f),
                painter = painterResource(R.drawable.ic_hr_12),
                name = stringResource(R.string.sleep_quality_arg_sleep_hr),
                value = data.avgHr?.inBpm?.roundToInt()?.toString() ?: "--",
                unit = stringResource(CR.string.bpm),
            )
            NameValueColumn(
                modifier = Modifier.weight(1f),
                painter = painterResource(R.drawable.ic_hr_12),
                name = stringResource(R.string.sleep_quality_min_sleep_hr),
                value = data.minHr?.inBpm?.roundToInt()?.toString() ?: "--",
                unit = stringResource(CR.string.bpm),
            )
        }
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .defaultMinSize(minHeight = 45.dp),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            NameValueColumn(
                modifier = Modifier.weight(1f),
                painter = painterResource(R.drawable.ic_hrv_12),
                name = stringResource(R.string.sleep_quality_arg_sleep_hrv),
                value = data.avgHrv?.toString() ?: "--",
                unit = stringResource(CR.string.ms),
            )
            NameValueColumn(
                modifier = Modifier.weight(1f),
                painter = painterResource(R.drawable.ic_spo2_12),
                name = stringResource(R.string.sleep_quality_max_sleep_spo2),
                text = buildAnnotatedString {
                    append(data.maxSpO2?.times(100)?.formatPercent()?.trimEnd('%') ?: "--")
                    withStyle(MaterialTheme.typography.bodySmallBold.toSpanStyle()) {
                        append(" ")
                        append("%")
                        data.altitude?.roundToInt()?.let {
                            append(" ")
                            appendInlineContent(id = "altitude")
                            withStyle(SpanStyle(fontWeight = FontWeight.Normal)) {
                                append(stringResource(R.string.sleep_quality_max_altitude, it))
                                append(stringResource(data.altitudeUnitRes))
                            }
                        }
                    }
                },
                inlineContent = mapOf(
                    "altitude" to InlineTextContent(
                        Placeholder(12.sp, 12.sp, PlaceholderVerticalAlign.TextCenter)
                    ) {
                        Icon(
                            painter = painterResource(R.drawable.ic_altitude_12),
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onSurface
                        )
                    }
                ),
            )
        }
    }
}

@Composable
private fun NameValueColumn(
    painter: Painter,
    name: String,
    value: String,
    unit: String,
    modifier: Modifier = Modifier,
) = NameValueColumn(
    modifier = modifier,
    painter = painter,
    name = name,
    text = buildAnnotatedString {
        append(value)
        withStyle(MaterialTheme.typography.bodySmallBold.toSpanStyle()) {
            append(" ")
            append(unit)
        }
    },
)

@Composable
private fun NameValueColumn(
    painter: Painter,
    name: String,
    text: AnnotatedString,
    modifier: Modifier = Modifier,
    inlineContent: Map<String, InlineTextContent> = emptyMap(),
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xxsmall),
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.bodyXLargeBold,
            color = MaterialTheme.colorScheme.onSurface,
            inlineContent = inlineContent,
        )
        Row(
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xxsmall),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Icon(
                painter = painter,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.secondary,
            )
            Text(
                text = name,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.secondary,
            )
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun DailySleepDuration(
    data: DailySleepDurationData,
    date: String,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(6.dp),
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                modifier = Modifier.weight(1f),
                text = data.totalDuration.inWholeSeconds.formatDuration(context),
                style = MaterialTheme.typography.bodyLargeBold,
                color = MaterialTheme.colorScheme.onSurface,
            )
            Text(
                text = stringResource(
                    R.string.sleep_time_duration_of_target,
                    formatSleepTarget(data.totalDuration, data.goal),
                ),
                style = MaterialTheme.typography.bodyBold,
                color = MaterialTheme.colorScheme.activitySleep,
            )
        }
        SleepProgress(
            progress = (data.longSleepDuration / data.goal).toFloat(),
            progressBarColor = MaterialTheme.colorScheme.activitySleep,
            secondaryProgress = (data.napDuration / data.goal).toFloat(),
            secondaryProgressBarColor = MaterialTheme.colorScheme.activityCycling,
            backgroundColor = MaterialTheme.colorScheme.secondaryContainer,
        )
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            FlowRow(
                modifier = Modifier.weight(1f),
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmaller),
                verticalArrangement = Arrangement.spacedBy(6.dp),
            ) {
                data.longSleep?.let {
                    SleepLegend(
                        color = MaterialTheme.colorScheme.activitySleep,
                        text = stringResource(
                            R.string.sleep_time_duration_of_sleep,
                            "${it.fellAsleep} - ${it.wokeUp}",
                        ),
                    )
                }
                data.naps.forEach {
                    SleepLegend(
                        color = MaterialTheme.colorScheme.activityCycling,
                        text = stringResource(
                            R.string.sleep_time_duration_of_nap,
                            "${it.fellAsleep} - ${it.wokeUp}",
                        ),
                    )
                }
            }
            Text(
                text = date,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface,
            )
        }
    }
}

private fun formatSleepTarget(totalDuration: Duration, goal: Duration): String {
    val diff = (totalDuration - goal).inWholeSeconds
    val sign = if (diff < 0L) "-" else "+"
    return abs(diff).secondsToHourMinute().let { (hours, minutes) ->
        String.format(Locale.US, "%s%d:%02d", sign, hours, minutes)
    }
}

@Composable
private fun DailySleepResources(
    data: DailySleepResourcesData,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(6.dp),
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(6.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                modifier = Modifier.weight(1f),
                text = data.wakeUpBalance.times(100).formatPercent(),
                style = MaterialTheme.typography.bodyLargeBold,
                color = MaterialTheme.colorScheme.onSurface,
            )
            data.gainedBalance?.let { gainedBalance ->
                val label = if (gainedBalance < 0) {
                    stringResource(R.string.sleep_wake_up_resources_lost_during_sleep)
                } else {
                    stringResource(R.string.sleep_wake_up_resources_gained_during_sleep)
                }
                Text(
                    text = "${abs(gainedBalance).times(100).formatPercent()} $label",
                    style = MaterialTheme.typography.bodyBold,
                    color = MaterialTheme.colorScheme.activityRecovery,
                )
            }
        }
        SleepProgress(
            progress = data.wakeUpBalance,
            progressBarColor = MaterialTheme.colorScheme.activityRecovery,
            backgroundColor = MaterialTheme.colorScheme.secondaryContainer,
        )
        Text(
            text = stringResource(R.string.sleep_wake_up_resources_title),
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurface,
        )
    }
}
