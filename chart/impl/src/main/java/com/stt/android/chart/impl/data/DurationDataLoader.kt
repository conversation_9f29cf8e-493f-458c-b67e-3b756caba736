package com.stt.android.chart.impl.data

import android.content.Context
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.sp
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartGranularity.DAILY
import com.stt.android.chart.api.model.ChartGranularity.EIGHT_YEARS
import com.stt.android.chart.api.model.ChartGranularity.MONTHLY
import com.stt.android.chart.api.model.ChartGranularity.SEVEN_DAYS
import com.stt.android.chart.api.model.ChartGranularity.SIX_MONTHS
import com.stt.android.chart.api.model.ChartGranularity.SIX_WEEKS
import com.stt.android.chart.api.model.ChartGranularity.THIRTY_DAYS
import com.stt.android.chart.api.model.ChartGranularity.WEEKLY
import com.stt.android.chart.api.model.ChartGranularity.YEARLY
import com.stt.android.chart.api.model.ChartGranularity.SIXTY_DAYS
import com.stt.android.chart.api.model.ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS
import com.stt.android.chart.api.model.ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS
import com.stt.android.chart.api.model.ChartStyle
import com.stt.android.chart.impl.model.ChartBarDisplayMode
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.ChartType
import com.stt.android.chart.impl.model.LineChartConfig
import com.stt.android.chart.impl.model.epochMonth
import com.stt.android.chart.impl.screen.GoalEditorViewData
import com.stt.android.chart.impl.screen.GoalViewData
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.workouts.GetWorkoutHeadersForRangeUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.settings.goalsettings.FetchWeeklyTrainingGoalUseCase
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.utils.atEndOfDay
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters
import java.util.stream.Collectors
import javax.inject.Inject
import kotlin.math.ceil
import kotlin.math.floor
import kotlin.math.log10
import kotlin.math.max
import kotlin.math.pow
import kotlin.math.roundToInt
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

internal class DurationDataLoader @Inject constructor(
    @ApplicationContext private val context: Context,
    private val userController: CurrentUserController,
    private val userSettingsController: UserSettingsController,
    private val getWorkoutHeadersForRangeUseCase: GetWorkoutHeadersForRangeUseCase,
    private val fetchWeeklyTrainingGoalUseCase: FetchWeeklyTrainingGoalUseCase
)  {
    fun loadGoalData(): GoalViewData = if (fetchWeeklyTrainingGoalUseCase.hasGoal()) {
        GoalViewData.Goal(
            icon = BaseR.drawable.dashboard_widget_duration,
            iconColor = BaseR.color.dashboard_widget_duration,
            title = BaseR.string.weekly_goal_bottom_sheet_title,
            goal = fetchWeeklyTrainingGoalUseCase().map { goal ->
                TextFormatter.formatElapsedTimeWithUnit(context.resources, goal.toLong())
            }
        )
    } else {
        GoalViewData.None
    }

    suspend fun loadGoalEditorData(): GoalEditorViewData = if (fetchWeeklyTrainingGoalUseCase.hasGoal()) {
        GoalEditorViewData.Editor(
            chartContent = ChartContent.DURATION,
            requiresWatchConnection = false,
            currentGoal = fetchWeeklyTrainingGoalUseCase().first()
        )
    } else {
        GoalEditorViewData.None
    }

    private fun adjustMaxY(maxY: Double, goal: Double? = null): Double {
        val baseMaxY = if (maxY <= 0.0) {
            180.0
        } else {
            val maxYHours = ceil(maxY / 60.0).toInt()
            val (stepHours, maxHours) = computeYAxisTickStepAndMax(maxYHours)
            maxHours.toDouble() * 60.0
        }
        
        return if (goal != null && goal > 0.0) {
            val goalHours = ceil(goal / 60.0).toInt()
            val requiredMaxYHours = max(ceil(baseMaxY / 60.0).toInt(), goalHours + 1)
            val (stepHours, maxHours) = computeYAxisTickStepAndMax(requiredMaxYHours)
            maxHours.toDouble() * 60.0
        } else {
            baseMaxY
        }
    }
    
    private fun computeYAxisTickStepAndMax(maxValue: Int): Pair<Int, Int> {
        if (maxValue <= 0) {
            return Pair(1, 3)
        }
        val rawInterval = maxValue.toDouble() / 3.0
        val base = 10.0.pow(floor(log10(rawInterval)))
        val stepDouble = ceil(rawInterval / base) * base
        val step = max(1, stepDouble.roundToInt())
        return Pair(step, step * 3)
    }

    fun load(
        chartStyle: ChartStyle,
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        loadComparison: Boolean
    ): Flow<ChartData> = flow {
        val workoutHeaders = getWorkoutHeadersForRangeUseCase(
            GetWorkoutHeadersForRangeUseCase.Params(
                userController.username,
                null,
                from.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(),
                to.atEndOfDay().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()
            )
        )

        val goalValue = if (fetchWeeklyTrainingGoalUseCase.hasGoal()
            && chartGranularity == SIX_MONTHS
            && chartStyle == ChartStyle.SINGLE) {
            fetchWeeklyTrainingGoalUseCase().first().toDouble() / 60.0
        } else {
            null
        }

        val series = mutableListOf<ChartData.Series>()

        val currentSeries = createDurationDataSeries(
            chartStyle = chartStyle,
            chartGranularity = chartGranularity,
            from = from,
            to = to,
            workoutHeaders = workoutHeaders,
            goal = goalValue
        )

        if (loadComparison) {
            val periodLength = ChronoUnit.DAYS.between(from, to) + 1
            val comparisonTo = from.minusDays(1)
            val comparisonFrom = comparisonTo.minusDays(periodLength - 1)
            
            val comparisonWorkoutHeaders = getWorkoutHeadersForRangeUseCase(
                GetWorkoutHeadersForRangeUseCase.Params(
                    userController.username,
                    null,
                    comparisonFrom.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(),
                    comparisonTo.atEndOfDay().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()
                )
            )
            
            val comparisonSeries = createComparisonDataSeries(
                chartStyle = chartStyle,
                chartGranularity = chartGranularity,
                currentDateRange = from..to,
                comparisonDateRange = comparisonFrom..comparisonTo,
                workoutHeaders = comparisonWorkoutHeaders,
                currentSeries = currentSeries,
                goal = goalValue
            )
            
            if (comparisonSeries != null) {
                val updatedCurrentSeries = currentSeries.copy(
                    axisRange = comparisonSeries.axisRange
                )
                series.add(updatedCurrentSeries)
                series.add(comparisonSeries)
            } else {
                series.add(currentSeries)
            }
        } else {
            series.add(currentSeries)
        }
        
        emit(
            ChartData(
                chartGranularity = chartGranularity,
                chartBarDisplayMode = ChartBarDisplayMode.GROUPED,
                series = series.toImmutableList(),
                highlightEnabled = true,
                goal = goalValue,
                highlightDecorationLines = persistentMapOf(),
                currentValues = persistentListOf(),
                chartContent = ChartContent.DURATION,
                colorIndicator = null,
            )
        )
    }

    private fun createComparisonDataSeries(
        chartStyle: ChartStyle,
        chartGranularity: ChartGranularity,
        currentDateRange: ClosedRange<LocalDate>,
        comparisonDateRange: ClosedRange<LocalDate>,
        workoutHeaders: List<WorkoutHeader>,
        currentSeries: ChartData.Series,
        goal: Double? = null
    ): ChartData.Series? {
        if (workoutHeaders.isEmpty()) {
            return null
        }

        val created = createDurationDataSeries(
            chartStyle = chartStyle,
            chartGranularity = chartGranularity,
            from = comparisonDateRange.start,
            to = comparisonDateRange.endInclusive,
            workoutHeaders = workoutHeaders,
            isComparison = true
        )

        val xOffset = when (chartGranularity) {
            DAILY -> {
                val currentStart = currentDateRange.start.atStartOfDay().toEpochSecond(ZoneId.systemDefault().rules.getOffset(currentDateRange.start.atStartOfDay())) / 60
                val comparisonStart = comparisonDateRange.start.atStartOfDay().toEpochSecond(ZoneId.systemDefault().rules.getOffset(comparisonDateRange.start.atStartOfDay())) / 60
                currentStart - comparisonStart
            }
            
            WEEKLY,
            SEVEN_DAYS,
            MONTHLY,
            THIRTY_DAYS,
            SIX_WEEKS,
            SIX_MONTHS -> ChronoUnit.DAYS.between(
                comparisonDateRange.start,
                currentDateRange.start
            )

            YEARLY -> ChronoUnit.MONTHS.between(
                comparisonDateRange.start,
                currentDateRange.start
            )
            
            EIGHT_YEARS -> ChronoUnit.YEARS.between(
                comparisonDateRange.start,
                currentDateRange.start
            )

            SIXTY_DAYS,
            ONE_HUNDRED_EIGHTY_DAYS,
            THREE_HUNDRED_SIXTY_FIVE_DAYS  -> throw IllegalArgumentException("Unsupported granularity: $chartGranularity")
        }

        val updatedEntries = created.entries
            .map { entry ->
                entry.copy(x = entry.x + xOffset)
            }.toImmutableList()

        val currentMaxY = currentSeries.axisRange.maxY
        val comparisonMaxY = adjustMaxY(created.axisRange.maxY, goal)
        val finalMaxY = max(currentMaxY, comparisonMaxY)

        return created.copy(
            axisRange = ChartData.AxisRange(
                minX = currentSeries.axisRange.minX,
                maxX = currentSeries.axisRange.maxX,
                minY = 0.0,
                maxY = finalMaxY
            ),
            entries = updatedEntries,
        )
    }

    private fun createDurationDataSeries(
        chartStyle: ChartStyle,
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        workoutHeaders: List<WorkoutHeader>,
        isComparison: Boolean = false,
        goal: Double? = null
    ): ChartData.Series {
        val minX: Long
        val maxX: Long
        var maxY = 0.0
        var totalDuration = 0.0
        var monthsWithActivity = 0
        var yearsWithActivity = 0

        val workoutsByDate = workoutHeaders.groupBy { header ->
            LocalDate.ofInstant(
                Instant.ofEpochMilli(header.startTime),
                ZoneId.systemDefault()
            )
        }
        
        val entries:List<ChartData.Entry> = when (chartGranularity) {
            DAILY -> buildList {
                from.datesUntil(to.plusDays(1))
                    .collect(Collectors.toList())
                    .forEach { date ->
                        val dateWorkouts = workoutsByDate[date] ?: emptyList()
                        val dateDuration = dateWorkouts.sumOf { it.totalTime / 60.0 }
                        
                        totalDuration += dateDuration
                        val value = when (chartStyle) {
                            ChartStyle.SINGLE -> dateDuration
                            ChartStyle.CUMULATIVE -> totalDuration
                        }
                        maxY = max(maxY, value)
                        
                        val shouldAddEntry = when (chartStyle) {
                            ChartStyle.SINGLE -> dateDuration > 0
                            ChartStyle.CUMULATIVE -> !date.isAfter(LocalDate.now())
                        }
                        
                        if (shouldAddEntry) {
                            val entry = ChartData.Entry(
                                x = date.atStartOfDay().toEpochSecond(ZoneId.systemDefault().rules.getOffset(date.atStartOfDay())) / 60,
                                y = value
                            )
                            add(entry)
                        }
                    }
                
                minX = from.atStartOfDay().toEpochSecond(ZoneId.systemDefault().rules.getOffset(from.atStartOfDay())) / 60
                maxX = to.atEndOfDay().toEpochSecond(ZoneId.systemDefault().rules.getOffset(to.atEndOfDay())) / 60
            }
            
            WEEKLY, SEVEN_DAYS, MONTHLY, SIX_WEEKS -> buildList {
                from.datesUntil(to.plusDays(1))
                    .collect(Collectors.toList())
                    .forEach { date ->
                        val dateWorkouts = workoutsByDate[date] ?: emptyList()
                        val dateDuration = dateWorkouts.sumOf { it.totalTime / 60.0 }
                        
                        totalDuration += dateDuration
                        val value = when (chartStyle) {
                            ChartStyle.SINGLE -> dateDuration
                            ChartStyle.CUMULATIVE -> totalDuration
                        }
                        maxY = max(maxY, value)
                        
                        val shouldAddEntry = when (chartStyle) {
                            ChartStyle.SINGLE -> dateDuration > 0
                            ChartStyle.CUMULATIVE -> !date.isAfter(LocalDate.now())
                        }
                        
                        if (shouldAddEntry) {
                            val entry = ChartData.Entry(
                                x = date.toEpochDay(),
                                y = value
                            )
                            add(entry)
                        }
                    }
                
                minX = from.toEpochDay()
                maxX = to.toEpochDay()
            }
            
            SIX_MONTHS -> buildList {
                val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
                
                val weeklyData = mutableMapOf<LocalDate, Double>()
                from.datesUntil(to.plusDays(1))
                    .collect(Collectors.toList())
                    .forEach { date ->
                        val startOfWeek = date.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
                        val dateWorkouts = workoutsByDate[date] ?: emptyList()
                        val dateDuration = dateWorkouts.sumOf { it.totalTime / 60.0 }
                        
                        weeklyData[startOfWeek] = (weeklyData[startOfWeek] ?: 0.0) + dateDuration
                    }
                
                var cumulativeDuration = 0.0
                weeklyData.entries.sortedBy { it.key }.forEach { (startOfWeek, weekDuration) ->
                    cumulativeDuration += weekDuration
                    
                    val value = when (chartStyle) {
                        ChartStyle.SINGLE -> weekDuration
                        ChartStyle.CUMULATIVE -> cumulativeDuration
                    }
                    maxY = max(maxY, value)
                    
                    val shouldAddEntry = when (chartStyle) {
                        ChartStyle.SINGLE -> weekDuration > 0
                        ChartStyle.CUMULATIVE -> !startOfWeek.isAfter(LocalDate.now())
                    }
                    
                    if (shouldAddEntry) {
                        val entry = ChartData.Entry(
                            x = startOfWeek.toEpochDay(),
                            y = value
                        )
                        add(entry)
                    }
                }
                
                totalDuration = cumulativeDuration
                minX = from.toEpochDay()
                maxX = to.toEpochDay()
            }
            
            YEARLY -> buildList {
                val monthlyData = mutableMapOf<Int, Double>()
                from.datesUntil(to.plusDays(1))
                    .collect(Collectors.toList())
                    .forEach { date ->
                        val epochMonth = date.epochMonth
                        val dateWorkouts = workoutsByDate[date] ?: emptyList()
                        val dateDuration = dateWorkouts.sumOf { it.totalTime / 60.0 }
                        
                        monthlyData[epochMonth] = (monthlyData[epochMonth] ?: 0.0) + dateDuration
                    }
                
                var cumulativeDuration = 0.0
                monthlyData.entries.sortedBy { it.key }.forEach { (epochMonth, monthDuration) ->
                    cumulativeDuration += monthDuration
                    
                    val value = when (chartStyle) {
                        ChartStyle.SINGLE -> monthDuration
                        ChartStyle.CUMULATIVE -> cumulativeDuration
                    }
                    maxY = max(maxY, value)
                    
                    val shouldAddEntry = when (chartStyle) {
                        ChartStyle.SINGLE -> monthDuration > 0
                        ChartStyle.CUMULATIVE -> epochMonth <= LocalDate.now().epochMonth
                    }
                    
                    if (shouldAddEntry) {
                        val entry = ChartData.Entry(
                            x = epochMonth.toLong(),
                            y = value
                        )
                        add(entry)
                    }
                    
                    if (monthDuration > 0) {
                        monthsWithActivity++
                    }
                }
                
                totalDuration = cumulativeDuration
                minX = from.epochMonth.toLong()
                maxX = to.epochMonth.toLong()
            }
            
            EIGHT_YEARS -> buildList {
                val yearlyData = mutableMapOf<Int, Double>()
                val yearlyDayCount = mutableMapOf<Int, Int>()
                
                from.datesUntil(to.plusDays(1))
                    .collect(Collectors.toList())
                    .forEach { date ->
                        val year = date.year
                        val dateWorkouts = workoutsByDate[date] ?: emptyList()
                        val dateDuration = dateWorkouts.sumOf { it.totalTime / 60.0 }
                        
                        yearlyData[year] = (yearlyData[year] ?: 0.0) + dateDuration
                        
                        if (dateDuration > 0) {
                            yearlyDayCount[year] = (yearlyDayCount[year] ?: 0) + 1
                        }
                    }
                
                var cumulativeDuration = 0.0
                yearlyData.entries.sortedBy { it.key }.forEach { (year, yearDuration) ->
                    cumulativeDuration += yearDuration
                    
                    val value = when (chartStyle) {
                        ChartStyle.SINGLE -> {
                            maxY = max(maxY, yearDuration)
                            yearDuration
                        }
                        ChartStyle.CUMULATIVE -> {
                            maxY = max(maxY, cumulativeDuration)
                            cumulativeDuration
                        }
                    }
                    
                    val shouldAddEntry = when (chartStyle) {
                        ChartStyle.SINGLE -> yearDuration > 0
                        ChartStyle.CUMULATIVE -> year <= LocalDate.now().year
                    }
                    
                    if (shouldAddEntry) {
                        val entry = ChartData.Entry(
                            x = year.toLong(),
                            y = value
                        )
                        add(entry)
                    }
                    
                    if (yearDuration > 0) {
                        yearsWithActivity++
                    }
                }
                
                totalDuration = cumulativeDuration
                minX = from.year.toLong()
                maxX = to.year.toLong()
            }
            
            THIRTY_DAYS -> buildList {
                from.datesUntil(to.plusDays(1))
                    .collect(Collectors.toList())
                    .forEach { date ->
                        val dateWorkouts = workoutsByDate[date] ?: emptyList()
                        val dateDuration = dateWorkouts.sumOf { it.totalTime / 60.0 }
                        
                        totalDuration += dateDuration
                        val value = when (chartStyle) {
                            ChartStyle.SINGLE -> dateDuration
                            ChartStyle.CUMULATIVE -> totalDuration
                        }
                        maxY = max(maxY, value)
                        
                        val shouldAddEntry = when (chartStyle) {
                            ChartStyle.SINGLE -> dateDuration > 0
                            ChartStyle.CUMULATIVE -> !date.isAfter(LocalDate.now())
                        }
                        
                        if (shouldAddEntry) {
                            val entry = ChartData.Entry(
                                x = date.toEpochDay(),
                                y = value
                            )
                            add(entry)
                        }
                    }
                
                minX = from.toEpochDay()
                maxX = to.toEpochDay()
            }
            
            SIXTY_DAYS,
            ONE_HUNDRED_EIGHTY_DAYS,
            THREE_HUNDRED_SIXTY_FIVE_DAYS -> throw IllegalArgumentException("this $chartGranularity is not supported for duration data")
        }

        maxY = adjustMaxY(maxY, goal)
        
        val color = if (isComparison) {
            context.getColor(BaseR.color.medium_grey)
        } else {
            context.getColor(BaseR.color.dashboard_widget_duration)
        }

        val (hours, minutes) = minutesToHourMinute(totalDuration)
        val formattedTotalDuration = formatDuration(hours, minutes)
        
        val displayValue = when (chartGranularity) {
            EIGHT_YEARS -> calculateYearlyAverageDuration(totalDuration, yearsWithActivity)
            YEARLY -> calculateMonthlyAverageDuration(totalDuration, monthsWithActivity)
            else -> formattedTotalDuration
        }
        
        return ChartData.Series(
            chartType = when (chartStyle) {
                ChartStyle.SINGLE -> ChartType.BAR
                ChartStyle.CUMULATIVE -> ChartType.LINE
            },
            color = color,
            axisRange = ChartData.AxisRange(
                minX = minX.toDouble(),
                maxX = maxX.toDouble(),
                minY = 0.0,
                maxY = maxY
            ),
            entries = entries.toImmutableList(),
            value = displayValue,
            candlestickEntries = persistentListOf(),
            lineConfig = LineChartConfig(
                showAreaFill = !isComparison
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
        )
    }


    private fun calculateMonthlyAverageDuration(
        totalDuration: Double,
        monthsWithActivity: Int
    ): AnnotatedString {
        return if (monthsWithActivity > 0) {
            val avgMonthlyDuration = totalDuration / monthsWithActivity
            val (avgHours, avgMinutes) = minutesToHourMinute(avgMonthlyDuration)
            formatDuration(avgHours, avgMinutes)
        } else {
            val (hours, minutes) = minutesToHourMinute(totalDuration)
            formatDuration(hours, minutes)
        }
    }

    private fun calculateYearlyAverageDuration(
        totalDuration: Double,
        yearsWithActivity: Int
    ): AnnotatedString {
        return if (yearsWithActivity > 0) {
            val avgYearlyDuration = totalDuration / yearsWithActivity
            val (avgHours, avgMinutes) = minutesToHourMinute(avgYearlyDuration)
            formatDuration(avgHours, avgMinutes)
        } else {
            val (hours, minutes) = minutesToHourMinute(totalDuration)
            formatDuration(hours, minutes)
        }
    }

    private fun minutesToHourMinute(minutes: Double): Pair<Long, Long> {
        val totalMinutes = minutes.roundToInt()
        val hours = totalMinutes / 60
        val mins = totalMinutes % 60
        return Pair(hours.toLong(), mins.toLong())
    }
    
    private fun formatDuration(hours: Long, minutes: Long): AnnotatedString {
        val hourUnit = context.getString(CR.string.hour)
        val minuteUnit = context.getString(CR.string.minute)
        
        if (hours == 0L && minutes == 0L) return generateWidgetTitle("0", hourUnit)
        
        return if (hours == 0L) {
            generateWidgetTitle("$minutes", minuteUnit)
        } else if (minutes == 0L) {
            generateWidgetTitle("$hours", hourUnit)
        } else {
            generateWidgetTitle("$hours", hourUnit) +
                AnnotatedString(" ") +
                generateWidgetTitle("$minutes", minuteUnit)
        }
    }
    
    private fun generateWidgetTitle(value: String, unit: String): AnnotatedString {
        return buildAnnotatedString {
            append(value)
            withStyle(SpanStyle(fontSize = 12.sp)) {
                append(" ")
                append(unit)
            }
        }
    }

    fun formatHighlightData(value: Number?): String {
        if (value == null) {
            return "-"
        }
        val minutes = value.toDouble()
        val (hours, mins) = minutesToHourMinute(minutes)
        
        val hourUnit = context.getString(CR.string.hour)
        val minuteUnit = context.getString(CR.string.minute)
        
        return when {
            hours == 0L && mins == 0L -> "0 $hourUnit"
            hours == 0L -> "$mins $minuteUnit"
            mins == 0L -> "$hours $hourUnit"
            else -> "$hours $hourUnit $mins $minuteUnit"
        }
    }
} 
