package com.stt.android.chart.impl.screen

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.hilt.navigation.compose.hiltViewModel
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.chart.api.model.ChartComparison
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartStyle
import com.stt.android.compose.util.setContentWithM3Theme
import dagger.hilt.android.AndroidEntryPoint
import java.time.LocalDate

@AndroidEntryPoint
internal class ChartActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentWithM3Theme {
            val viewModel = hiltViewModel<ChartViewModel>()
            val viewData by viewModel.viewData.collectAsState()
            ChartScreen(
                viewData = viewData,
                onEvent = { event ->
                    if (event is ChartViewEvent.Close) {
                        // Special case to close the activity.
                        finish()
                    } else {
                        viewModel.onViewEvent(event)
                    }
                },
            )
        }
    }

    companion object {
        const val KEY_CHART_CONTENT: String = "KEY_CHART_CONTENT"
        const val KEY_CHART_STYLE: String = "KEY_CHART_STYLE"
        const val KEY_CHART_GRANULARITY: String = "KEY_CHART_GRANULARITY"
        const val KEY_CHART_COMPARISON: String = "KEY_CHART_COMPARISON"
        const val KEY_SOURCE: String = "KEY_SOURCE"
        const val KEY_TARGET_DATE: String = "KEY_TARGET_DATE"

        fun newStartIntent(
            context: Context,
            chartContent: ChartContent,
            chartStyle: ChartStyle,
            chartGranularity: ChartGranularity,
            chartComparison: ChartComparison,
            targetDate: LocalDate = LocalDate.now(),
            source: String = AnalyticsPropertyValue.WidgetDetailPageExposureSourceProperty.OTHER,
        ): Intent {
            return Intent(context, ChartActivity::class.java).apply {
                putExtra(KEY_CHART_CONTENT, chartContent)
                putExtra(KEY_CHART_STYLE, chartStyle)
                putExtra(KEY_CHART_GRANULARITY, chartGranularity)
                putExtra(KEY_CHART_COMPARISON, chartComparison)
                putExtra(KEY_SOURCE, source)
                putExtra(KEY_TARGET_DATE, targetDate)
            }
        }
    }
}
