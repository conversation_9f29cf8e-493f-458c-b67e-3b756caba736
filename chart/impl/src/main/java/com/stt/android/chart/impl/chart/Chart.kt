package com.stt.android.chart.impl.chart

import androidx.compose.foundation.pager.PagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import com.patrykandpatrick.vico.compose.cartesian.CartesianChartHost
import com.patrykandpatrick.vico.compose.cartesian.rememberCartesianChart
import com.patrykandpatrick.vico.compose.cartesian.rememberVicoZoomState
import com.patrykandpatrick.vico.core.cartesian.Zoom
import com.patrykandpatrick.vico.core.cartesian.data.CartesianChartModelProducer
import com.patrykandpatrick.vico.core.cartesian.marker.CartesianMarker
import com.patrykandpatrick.vico.core.cartesian.marker.CartesianMarkerVisibilityListener
import com.stt.android.chart.impl.chart.axis.calculateXAxisStep
import com.stt.android.chart.impl.chart.axis.rememberXAxis
import com.stt.android.chart.impl.chart.axis.rememberYAxis
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.compose.modifiers.onLongPress
import kotlinx.coroutines.delay
import kotlin.math.roundToLong

@Composable
fun Chart(
    chartData: ChartData,
    onEntrySelected: (Long) -> Unit,
    onNoEntrySelected: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current

    val modelProducer = remember { CartesianChartModelProducer() }
    val rangeProvider = remember { RangeProvider(chartData) }
    LaunchedEffect(chartData) {
        modelProducer.prepareData(chartData, rangeProvider)
    }

    val markerLine = rememberLineComponentWithTriangle()

    val backgroundDecorations = chartData.series.mapNotNull { series ->
        series.backgroundRegion?.let { bgRegion ->
            rememberBackgroundRegionDecoration(
                regions = bgRegion.backgroundRegions,
                color = Color(bgRegion.backgroundColorInt)
            )
        }
    }

    val goalDecoration = chartData.goal?.let { rememberGoalDecoration(it) }

    val highlightDecorations = chartData.highlightDecorationLines
        .takeIf { it.isNotEmpty() }
        ?.let { rememberHighlightDecorations(it) }
        .orEmpty()

    val colorIndicator = chartData.colorIndicator?.let { config ->
        rememberVerticalColorBar(
            thresholds = config.thresholds.toList(),
            colors = config.colors.map { Color(it) },
            labelTexts = config.labelTexts.toList(),
            barThicknessDp = config.barThicknessDp,
            barShiftXDp = config.barShiftXDp
        )
    }

    val averageDecoration = chartData.series.firstOrNull()?.let {  rememberAverageDecorations(it.average) }

    var defaultSelectedX by remember { mutableStateOf(chartData.selectEntryX) }

    var chartLongPressed by remember { mutableStateOf(false) }
    var selectedEntryX by remember(chartData.selectEntryX) { mutableStateOf(chartData.selectEntryX) }

    var highlightColumnEntryX by remember(chartData.selectEntryX) { mutableStateOf(chartData.selectEntryX) }

    LaunchedEffect(defaultSelectedX) {
        val currentDefaultX = defaultSelectedX
        if (currentDefaultX != null) {
            onEntrySelected(currentDefaultX)
            delay(5000)
            selectedEntryX = null
            onNoEntrySelected()
        }
    }
    val defaultMarkerDecoration = defaultSelectedX?.let { currentDefaultX ->
        if (!chartLongPressed && selectedEntryX == currentDefaultX) {
            rememberVerticalLineDecoration(currentDefaultX)
        } else {
            null
        }
    }

    val highlightMarker = remember(chartData.highlightEnabled, chartLongPressed) {
        HighlightMarker(
            markerLine = markerLine,
            drawOverLayers = chartLongPressed,
        )
    }

    val layers = rememberChartLayers(chartData, rangeProvider , highlightColumnEntryX)

    val decorations = listOfNotNull(goalDecoration) +
        highlightDecorations +
        backgroundDecorations +
        listOfNotNull(colorIndicator) +
        listOfNotNull(averageDecoration) +
        listOfNotNull(defaultMarkerDecoration)

    CartesianChartHost(
        chart = rememberCartesianChart(
            layers = layers.toTypedArray(),
            endAxis = rememberYAxis(chartData),
            bottomAxis = rememberXAxis(context, chartData),
            marker = remember(chartData.highlightEnabled) {
                if (chartData.highlightEnabled) highlightMarker else null
            },
            markerVisibilityListener = object : CartesianMarkerVisibilityListener {
                override fun onShown(marker: CartesianMarker, targets: List<CartesianMarker.Target>) {
                    defaultSelectedX = null
                    onUpdated(marker, targets)
                }

                override fun onUpdated(marker: CartesianMarker, targets: List<CartesianMarker.Target>) {
                    targets.firstOrNull()
                        ?.x
                        ?.roundToLong()
                        ?.let { entryX ->
                            selectedEntryX = entryX
                            if (chartLongPressed) {
                                highlightColumnEntryX = entryX
                                onEntrySelected(entryX)
                            }
                        }
                        ?: run(onNoEntrySelected)
                }

                override fun onHidden(marker: CartesianMarker) {
                    onNoEntrySelected()
                    selectedEntryX = null
                }
            },
            decorations = decorations,
            getXStep = calculateXAxisStep(chartData.chartGranularity),
        ),
        modelProducer = modelProducer,
        modifier = modifier.onLongPress(
            key = Unit,
            onLongPressed = { longPressed ->
                highlightMarker.drawOverLayers = longPressed
                chartLongPressed = longPressed
                if (longPressed) {
                    defaultSelectedX = null
                    selectedEntryX?.let(onEntrySelected)
                    highlightColumnEntryX = selectedEntryX
                } else {
                    selectedEntryX = null
                    highlightColumnEntryX = null
                    onNoEntrySelected()
                }
            },
        ),
        zoomState = rememberVicoZoomState(
            zoomEnabled = false,
            initialZoom = Zoom.Content,
        ),
    )
}

fun PagerState.targetByOffsetFraction(): Int {
    val offsetFraction = currentPageOffsetFraction
    return when {
        offsetFraction > 0.5f -> currentPage + 1
        offsetFraction < -0.5f -> currentPage - 1
        else -> currentPage
    }.coerceIn(0, pageCount - 1)
}
