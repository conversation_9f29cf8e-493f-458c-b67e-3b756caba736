package com.stt.android.chart.impl.screen.components

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.chart.impl.R
import com.stt.android.chart.impl.model.NestedListItem
import com.stt.android.chart.impl.model.NestedListSubItem
import com.stt.android.chart.impl.model.WidgetInstruction
import com.stt.android.compose.component.SuuntoCard
import com.stt.android.compose.modifiers.clickable
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.delay

@Composable
fun WidgetInstructions(
    instructions: ImmutableList<WidgetInstruction>,
    modifier: Modifier = Modifier,
    scrollState: ScrollState? = null
) {
    if (instructions.isEmpty()) {
        return
    }

    Column(modifier = modifier) {
        instructions.forEachIndexed { index, instruction ->
            Instruction(
                instruction = instruction,
                scrollState = scrollState,
                modifier = Modifier.then(
                    if (index == 0) {
                        Modifier
                    } else {
                        Modifier.padding(top = MaterialTheme.spacing.medium)
                    }
                ),
            )
        }
    }
}

@Composable
private fun Instruction(
    instruction: WidgetInstruction,
    modifier: Modifier = Modifier,
    scrollState: ScrollState? = null
) {
    SuuntoCard(
        modifier = modifier,
    ) {
        var showContent by remember { mutableStateOf(false) }
        
        LaunchedEffect(showContent) {
            if (showContent && scrollState != null) {
                delay(350)
                scrollState.animateScrollTo(scrollState.maxValue)
            }
        }
        
        Column(
            modifier = Modifier
                .clickable(onClick = { showContent = !showContent })
                .padding(MaterialTheme.spacing.medium),
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = stringResource(id = instruction.titleRes),
                    color = MaterialTheme.colorScheme.onSurface,
                    style = MaterialTheme.typography.bodyLargeBold,
                )
                Icon(
                    painter = if (showContent) {
                        painterResource(R.drawable.ic_up_small_outline)
                    } else {
                        painterResource(R.drawable.ic_down_small_outline)
                    },
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onSurface,
                )
            }

            if (!showContent) {
                return@Column
            }

            instruction.contentRes?.let { contentRes ->
                Text(
                    text = stringResource(contentRes),
                    modifier = Modifier.padding(top = MaterialTheme.spacing.medium),
                    color = MaterialTheme.colorScheme.onSurface,
                    style = MaterialTheme.typography.bodyLarge,
                )
            }

            if (instruction.items.isEmpty()) {
                return@Column
            }

            NestedListItems(
                items = instruction.items,
                modifier = Modifier.padding(top = MaterialTheme.spacing.small),
            )
        }
    }
}

@Composable
private fun NestedListItems(
    items: ImmutableList<NestedListItem>,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        items.forEach { item ->
            NestedListItemView(item = item)
        }
    }
}

@Composable
private fun NestedListItemView(
    item: NestedListItem,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.then(
            if (item.needPadding) {
                Modifier.padding(vertical = MaterialTheme.spacing.small)
            } else {
                Modifier
            }
        )
    ) {
        if (item.singleLine) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.Top
            ) {
                Text(
                    text = buildAnnotatedString {
                        if (item.titleRes != null) {
                            withStyle(SpanStyle(fontWeight = if (item.sameTitleStyle) FontWeight.Normal else FontWeight.Bold)) {
                                append(stringResource(id = item.titleRes))
                            }
                            append(" ")
                        }
                        if (item.descriptionRes != null) {
                            append(stringResource(id = item.descriptionRes))
                        }
                    },
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
        } else {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.Top
            ) {
                Column {
                    if (item.titleRes != null) {
                        Text(
                            text = stringResource(id = item.titleRes),
                            style = MaterialTheme.typography.bodyLarge.copy(
                                fontWeight = if (item.sameTitleStyle) FontWeight.Normal else FontWeight.Bold
                            ),
                            color = MaterialTheme.colorScheme.onSurface
                        )
                    }
                    
                    if (item.descriptionRes != null) {
                        Text(
                            text = stringResource(id = item.descriptionRes),
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurface,
                            modifier = Modifier.padding(top = MaterialTheme.spacing.xsmaller)
                        )
                    }
                }
            }
        }
        
        if (item.subItems.isNotEmpty()) {
            Column(
                if (item.needPadding) {
                    Modifier.padding(start = MaterialTheme.spacing.medium, top = MaterialTheme.spacing.small)
                } else {
                    Modifier.padding(start = MaterialTheme.spacing.medium)
                }
            ) {
                item.subItems.forEach { subItem ->
                    NestedListSubItemView(subItem = subItem)
                }
            }
        }
    }
}

@Composable
private fun NestedListSubItemView(
    subItem: NestedListSubItem,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.then(
            if (subItem.needPadding) {
                Modifier.padding(vertical = MaterialTheme.spacing.xsmaller)
            } else {
                Modifier
            }
        ),
        verticalAlignment = Alignment.Top
    ) {
        Text(
            text = buildAnnotatedString {
                if (subItem.prefixRes != null) {
                    withStyle(SpanStyle(fontWeight = FontWeight.Bold)) {
                        append(stringResource(id = subItem.prefixRes))
                        append(" ")
                    }
                }
                append(stringResource(id = subItem.contentRes))
            },
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewWidgetInstructions() {
    val instructions = remember {
        persistentListOf(
            WidgetInstruction(
                titleRes = R.string.about_calories,
                contentRes = R.string.calories_instruction
            ),
            WidgetInstruction(
                titleRes = R.string.how_to_read_data,
                contentRes = R.string.resource_states_intro,
                items = persistentListOf(
                    NestedListItem(
                        titleRes = R.string.recovering,
                        descriptionRes = R.string.resources_rise_quickly,
                        subItems = persistentListOf(
                            NestedListSubItem(
                                prefixRes = R.string.scenario_prefix,
                                contentRes = R.string.recovering_scenario
                            )
                        )
                    ),
                    NestedListItem(
                        titleRes = R.string.inactive,
                        descriptionRes = R.string.resources_change_slowly,
                        subItems = persistentListOf(
                            NestedListSubItem(
                                prefixRes = R.string.during_sleep_prefix,
                                contentRes = R.string.inactive_sleep_desc
                            ),
                            NestedListSubItem(
                                prefixRes = R.string.during_wakefulness_prefix,
                                contentRes = R.string.inactive_wakefulness_desc
                            )
                        )
                    )
                )
            )
        )
    }
    WidgetInstructions(instructions = instructions, scrollState = null)
}
