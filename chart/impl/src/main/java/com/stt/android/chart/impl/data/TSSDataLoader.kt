package com.stt.android.chart.impl.data

import android.content.Context
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.sp
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartGranularity.EIGHT_YEARS
import com.stt.android.chart.api.model.ChartGranularity.SIX_MONTHS
import com.stt.android.chart.api.model.ChartGranularity.YEARLY
import com.stt.android.chart.api.model.ChartStyle
import com.stt.android.chart.impl.model.ChartBarDisplayMode
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.ChartType
import com.stt.android.chart.impl.model.LineChartConfig
import com.stt.android.chart.impl.model.epochMonth
import com.stt.android.chart.impl.screen.GoalEditorViewData
import com.stt.android.chart.impl.screen.GoalViewData
import com.stt.android.controllers.UserSettingsController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.data.toEpochMilli
import com.stt.android.domain.user.CurrentUserDataSource
import com.stt.android.domain.workouts.tss.WorkoutTSSSummary
import com.stt.android.domain.workouts.tss.WorkoutTSSSummaryRepository.Companion.START_OF_TSS_SUPPORT
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.utils.sumByFloat
import com.stt.android.R as BaseR
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.collections.immutable.persistentHashMapOf
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.time.LocalDate
import java.time.ZoneId
import javax.inject.Inject
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters
import java.util.stream.Collectors
import kotlin.math.ceil
import kotlin.math.max
import kotlin.math.roundToInt

internal class TSSDataLoader @Inject constructor(
    @ApplicationContext private val appContext: Context,
    private val workoutHeaderController: WorkoutHeaderController,
    private val userSettingsController: UserSettingsController,
    private val currentUserDataSource: CurrentUserDataSource,
    private val infoModelFormatter: InfoModelFormatter,
) {
    fun loadGoalData(): GoalViewData = GoalViewData.None

    fun loadGoalEditorData(): GoalEditorViewData = GoalEditorViewData.None

    fun formatHighlightData(value: Number?): String {
        if (value == null) {
            return "-"
        }

        val tssData = infoModelFormatter.formatTssValue(value.toFloat())
        val unit = appContext.getString(BaseR.string.workout_values_headline_tss)

        return "$tssData $unit"
    }

    private companion object {
        fun InfoModelFormatter.formatTssValue(tss: Float): String = formatTss(tss).orEmpty()
    }

    fun load(
        chartStyle: ChartStyle,
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        loadComparison: Boolean
    ): Flow<ChartData> = flow {
        val currentUserName = currentUserDataSource.getCurrentUser().username
        val dataTSSSummary = loadWorkoutTSSSummaries(currentUserName)
        val dailyTSSScores = dataTSSSummary
            .groupBy { summary ->
                Instant.ofEpochMilli(summary.startTime)
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate()
            }
            .mapValues { (_, workouts) ->
                workouts.sumByFloat { it.tss.trainingStressScore }
            }
        val series = mutableListOf<ChartData.Series>()
        val currentSeries = createTSSDataSeries(
            chartStyle = chartStyle,
            chartGranularity = chartGranularity,
            from = from,
            to = to,
            dailyTSSData = dailyTSSScores
        )
        series.add(currentSeries)

        if (loadComparison) {
            val periodLength = ChronoUnit.DAYS.between(from, to) + 1
            val comparisonTo = from.minusDays(1)
            val comparisonFrom = comparisonTo.minusDays(periodLength - 1)

            val comparisonTSSData = createTSSDataSeries(
                chartStyle = chartStyle,
                chartGranularity = chartGranularity,
                from = comparisonFrom,
                to = comparisonTo,
                dailyTSSData = dailyTSSScores,
                isComparison = true
            )

            val processedSeries = processComparisonSeries(
                mainSeries = currentSeries,
                comparisonSeries = comparisonTSSData,
                currentDateRange = from..to,
                comparisonDateRange = comparisonFrom..comparisonTo,
                chartGranularity = chartGranularity
            )

            val finalMaxY = processedSeries.axisRange.maxY
            series[0] = currentSeries.copy(
                axisRange = currentSeries.axisRange.copy(maxY = finalMaxY)
            )

            series.add(processedSeries)
        }

        emit(
            ChartData(
                chartGranularity = chartGranularity,
                chartBarDisplayMode = ChartBarDisplayMode.GROUPED,
                series = series.toImmutableList(),
                highlightEnabled = true,
                goal = null,
                currentValues = persistentListOf(),
                highlightDecorationLines = persistentHashMapOf(),
                chartContent = ChartContent.TSS,
                colorIndicator = null,
            )
        )
    }

    private fun loadWorkoutTSSSummaries(username: String): List<WorkoutTSSSummary> {
        return workoutHeaderController.loadWorkoutTSSSummaries(
            username,
            START_OF_TSS_SUPPORT.toEpochMilli()
        )
    }

    private fun createTSSDataSeries(
        chartStyle: ChartStyle,
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        dailyTSSData: Map<LocalDate, Float>,
        isComparison: Boolean = false,
    ): ChartData.Series {
        val minX: Long
        val maxX: Long
        var maxY = 0.0
        var totalTSS = 0.0
        var totalTSSForCumulative = 0.0

        val entries = when (chartGranularity) {
            ChartGranularity.WEEKLY,
            ChartGranularity.MONTHLY,
            ChartGranularity.SEVEN_DAYS,
            ChartGranularity.THIRTY_DAYS,
            ChartGranularity.SIX_WEEKS -> buildList {
                from.datesUntil(to.plusDays(1))
                    .collect(Collectors.toList())
                    .forEach { date ->
                        val dateTSS = dailyTSSData[date] ?: 0f
                        totalTSS += dateTSS
                        totalTSSForCumulative += dateTSS

                        val value = when (chartStyle) {
                            ChartStyle.SINGLE -> dateTSS
                            ChartStyle.CUMULATIVE -> totalTSSForCumulative
                        }
                        maxY = maxOf(maxY, value.toDouble())

                        if (dateTSS > 0) {
                            val entry = ChartData.Entry(
                                x = date.toEpochDay(),
                                y = value.toDouble(),
                            )
                            add(entry)
                        }
                    }
                minX = from.toEpochDay()
                maxX = to.toEpochDay()
            }

            SIX_MONTHS -> buildList {
                val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek

                val weeklyData = mutableMapOf<LocalDate, Double>()
                from.datesUntil(to.plusDays(1))
                    .collect(Collectors.toList())
                    .forEach { date ->
                        val startOfWeek =
                            date.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
                        val dateTSS = dailyTSSData[date] ?: 0f

                        weeklyData[startOfWeek] = (weeklyData[startOfWeek] ?: 0.0) + dateTSS

                        totalTSS += dateTSS
                    }

                weeklyData.entries.sortedBy { it.key }.forEach { (startOfWeek, weekTSS) ->
                    totalTSSForCumulative += weekTSS
                    val value = when (chartStyle) {
                        ChartStyle.SINGLE -> weekTSS
                        ChartStyle.CUMULATIVE -> totalTSSForCumulative
                    }
                    maxY = max(maxY, value)

                    if (weekTSS > 0) {
                        val entry = ChartData.Entry(
                            x = startOfWeek.toEpochDay(),
                            y = value
                        )
                        add(entry)
                    }
                }

                minX = from.toEpochDay()
                maxX = to.toEpochDay()
            }

            YEARLY -> buildList {
                val monthlyData = mutableMapOf<Int, Double>()
                from.datesUntil(to.plusDays(1))
                    .collect(Collectors.toList())
                    .forEach { date ->
                        val epochMonth = date.epochMonth
                        val dateTSS = dailyTSSData[date] ?: 0f

                        monthlyData[epochMonth] = (monthlyData[epochMonth] ?: 0.0) + dateTSS

                        totalTSS += dateTSS
                    }
                monthlyData.entries.sortedBy { it.key }.forEach { (epochMonth, monthTSS) ->
                    totalTSSForCumulative += monthTSS
                    val value = when (chartStyle) {
                        ChartStyle.SINGLE -> monthTSS
                        ChartStyle.CUMULATIVE -> totalTSSForCumulative
                    }
                    maxY = max(maxY, value)

                    if (monthTSS > 0) {
                        val entry = ChartData.Entry(
                            x = epochMonth.toLong(),
                            y = value
                        )
                        add(entry)
                    }
                }
                minX = from.epochMonth.toLong()
                maxX = to.epochMonth.toLong()
            }

            EIGHT_YEARS -> {
                val result = createEightYearEntries(
                    chartStyle = chartStyle,
                    from = from,
                    to = to,
                    workoutByDate = dailyTSSData,
                    maxYRef = { maxY = max(maxY, it) }
                )
                minX = from.year.toLong()
                maxX = to.year.toLong()
                totalTSS = result.totalTSSEight
                result.entries
            }

            ChartGranularity.DAILY -> buildList {
                from.datesUntil(to.plusDays(1))
                    .collect(Collectors.toList())
                    .forEach { date ->
                        val dateTSS = dailyTSSData[date] ?: 0f
                        totalTSS += dateTSS
                        totalTSSForCumulative += dateTSS

                        val value = when (chartStyle) {
                            ChartStyle.SINGLE -> dateTSS
                            ChartStyle.CUMULATIVE -> totalTSSForCumulative
                        }
                        maxY = maxOf(maxY, value.toDouble())

                        val entry = ChartData.Entry(
                            x = date.atStartOfDay()
                                .toEpochSecond(ZoneId.systemDefault().rules.getOffset(date.atStartOfDay())) / 60,
                            y = value.toDouble()
                        )
                        add(entry)
                    }
                minX = from.toEpochDay()
                maxX = to.toEpochDay()
            }

            ChartGranularity.SIXTY_DAYS,
            ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
            ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS -> throw IllegalArgumentException("this $chartGranularity is not supported for tss data")
        }

        if (maxY == 0.0) {
            maxY = 12.0
        }

        val color = if (isComparison) {
            appContext.getColor(BaseR.color.medium_grey)
        } else {
            appContext.getColor(BaseR.color.dashboard_widget_tss)
        }

        val widgetTitle = generateWidgetTitle(
            value = totalTSS,
            unit = appContext.getString(BaseR.string.workout_values_headline_tss)
        )

        return ChartData.Series(
            chartType = when (chartStyle) {
                ChartStyle.SINGLE -> ChartType.BAR
                ChartStyle.CUMULATIVE -> ChartType.LINE
            },
            color = color,
            axisRange = ChartData.AxisRange(
                minX = minX.toDouble(),
                maxX = maxX.toDouble(),
                minY = 0.0,
                maxY = maxY.roundUpToNearestMultipleOf3().toDouble()
            ),
            entries = entries.toImmutableList(),
            value = widgetTitle,
            candlestickEntries = persistentListOf(),
            lineConfig = LineChartConfig(),
            backgroundRegion = null,
            groupStackBarStyle = null,
        )
    }

    private fun Double.roundUpToNearestMultipleOf3(): Int {
        val intValue = ceil(this).toInt()
        if (intValue % 3 == 0) return intValue
        return ((intValue + 2) / 3) * 3
    }

    data class EightYearResult(
        val entries: List<ChartData.Entry>,
        val totalTSSEight: Double
    )

    private fun createEightYearEntries(
        chartStyle: ChartStyle,
        from: LocalDate,
        to: LocalDate,
        workoutByDate: Map<LocalDate, Float>,
        maxYRef: (Double) -> Unit
    ): EightYearResult {
        val yearlyData = mutableMapOf<Int, Float>()
        val yearlyCount = mutableMapOf<Int, Int>()
        var totalTSSEight = 0.0
        var totalTSSForCumulativeEight = 0.0
        var totalDays = 0

        from.datesUntil(to.plusDays(1))
            .collect(Collectors.toList())
            .forEach { date ->
                val year = date.year
                val dateTSS = workoutByDate[date] ?: 0f

                yearlyData[year] = ((yearlyData[year] ?: 0f) + dateTSS)

                if (dateTSS > 0) {
                    yearlyCount[year] = (yearlyCount[year] ?: 0) + 1
                }
            }

        val entry = yearlyData.entries.sortedBy { it.key }.map { (year, yearTSS) ->
            totalTSSForCumulativeEight += yearTSS
            val daysHaveActivity = yearlyCount[year] ?: 0
            totalDays += daysHaveActivity
            val value = when (chartStyle) {
                ChartStyle.SINGLE -> {
                    maxYRef(yearTSS.toDouble())
                    yearTSS
                }

                ChartStyle.CUMULATIVE -> {
                    maxYRef(totalTSSForCumulativeEight)
                    totalTSSForCumulativeEight
                }
            }
            if (yearTSS > 0) {
                ChartData.Entry(
                    x = year.toLong(),
                    y = value
                )
            } else {
                null
            }

        }.filterNotNull()

        totalTSSEight = if (totalDays > 0) (totalTSSForCumulativeEight / totalDays) else 0.0
        return EightYearResult(entry, totalTSSEight)
    }

    private fun generateWidgetTitle(value: Double, unit: String): AnnotatedString {
        return buildAnnotatedString {
            append(value.roundToInt().toString())
            withStyle(SpanStyle(fontSize = 12.sp)) {
                append(" ")
                append(unit)
            }
        }
    }

    private fun calculateAverageTSS(
        dataTSSDaraCore: Map<LocalDate, Float>,
        totalTSS: Float
    ): AnnotatedString {
        val totalDays = dataTSSDaraCore.count()

        return if (totalDays > 0) {
            val avgDailyTSS = (totalTSS / totalDays).toDouble()
            generateWidgetTitle(
                value = avgDailyTSS,
                unit = appContext.getString(BaseR.string.workout_values_headline_tss)
            )
        } else {
            generateWidgetTitle(
                value = 0.0,
                unit = appContext.getString(BaseR.string.workout_values_headline_tss)
            )
        }
    }

    private fun processComparisonSeries(
        mainSeries: ChartData.Series,
        comparisonSeries: ChartData.Series,
        currentDateRange: ClosedRange<LocalDate>,
        comparisonDateRange: ClosedRange<LocalDate>,
        chartGranularity: ChartGranularity
    ): ChartData.Series {
        val xOffset = when (chartGranularity) {
            ChartGranularity.DAILY -> {
                val currentStart = currentDateRange.start.atStartOfDay()
                    .toEpochSecond(ZoneId.systemDefault().rules.getOffset(currentDateRange.start.atStartOfDay())) / 60
                val comparisonStart = comparisonDateRange.start.atStartOfDay()
                    .toEpochSecond(ZoneId.systemDefault().rules.getOffset(comparisonDateRange.start.atStartOfDay())) / 60
                (currentStart - comparisonStart).toDouble()
            }

            ChartGranularity.WEEKLY,
            ChartGranularity.MONTHLY,
            ChartGranularity.SEVEN_DAYS,
            ChartGranularity.THIRTY_DAYS,
            ChartGranularity.SIX_WEEKS,
            ChartGranularity.SIX_MONTHS -> ChronoUnit.DAYS.between(
                comparisonDateRange.start,
                currentDateRange.start
            ).toDouble()

            ChartGranularity.YEARLY -> ChronoUnit.MONTHS.between(
                comparisonDateRange.start.withDayOfMonth(1),
                currentDateRange.start.withDayOfMonth(1)
            ).toDouble()

            ChartGranularity.EIGHT_YEARS -> ChronoUnit.YEARS.between(
                comparisonDateRange.start,
                currentDateRange.start
            ).toDouble()

            else -> throw IllegalArgumentException("Unsupported granularity for comparison: $chartGranularity")
        }

        val adjustedEntries = comparisonSeries.entries.map { entry ->
            entry.copy(x = (entry.x + xOffset).toLong())
        }.toImmutableList()

        val mainMaxY = mainSeries.axisRange.maxY.takeIf { it > 0.0 } ?: 12.0
        val comparisonMaxY = comparisonSeries.axisRange.maxY.takeIf { it > 0.0 } ?: 12.0
        val finalMaxY = max(mainMaxY, comparisonMaxY)

        return comparisonSeries.copy(
            entries = adjustedEntries,
            axisRange = mainSeries.axisRange.copy(maxY = finalMaxY)
        )
    }
}

