package com.stt.android.chart.impl.usecases

import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.user.UserSettings
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import java.time.DayOfWeek
import java.time.LocalDate
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals

class CalculateDateRangeUseCaseTest {
    @MockK
    lateinit var userSettingsController: UserSettingsController

    private val today = LocalDate.of(2025, 2, 27)

    private lateinit var calculateDateRangeUseCase: CalculateDateRangeUseCase

    @BeforeTest
    fun setup() {
        MockKAnnotations.init(this)

        calculateDateRangeUseCase = CalculateDateRangeUseCase(userSettingsController)
    }

    @Test
    fun `test invoke() with ChartGranularity_DAILY`() {
        assertEquals(
            LocalDate.of(2025, 2, 27)..LocalDate.of(2025, 2, 27),
            calculateDateRangeUseCase(
                chartGranularity = ChartGranularity.DAILY,
                chartPageIndex = 49,
                chartPageCount = 50,
                today = today,
            )
        )

        assertEquals(
            LocalDate.of(2025, 2, 23)..LocalDate.of(2025, 2, 23),
            calculateDateRangeUseCase(
                chartGranularity = ChartGranularity.DAILY,
                chartPageIndex = 45,
                chartPageCount = 50,
                today = today,
            )
        )
    }

    @Test
    fun `test invoke() with ChartGranularity_WEEKLY`() {
        val settings = mockk<UserSettings>().apply {
            every { firstDayOfTheWeek } returns DayOfWeek.MONDAY
        }
        every { userSettingsController.settings } returns settings

        assertEquals(
            LocalDate.of(2025, 2, 24)..LocalDate.of(2025, 3, 2),
            calculateDateRangeUseCase(
                chartGranularity = ChartGranularity.WEEKLY,
                chartPageIndex = 49,
                chartPageCount = 50,
                today = today,
            )
        )

        assertEquals(
            LocalDate.of(2025, 1, 27)..LocalDate.of(2025, 2, 2),
            calculateDateRangeUseCase(
                chartGranularity = ChartGranularity.WEEKLY,
                chartPageIndex = 45,
                chartPageCount = 50,
                today = today,
            )
        )
    }

    @Test
    fun `test invoke() with ChartGranularity_SEVEN_DAYS`() {
        assertEquals(
            LocalDate.of(2025, 2, 21)..LocalDate.of(2025, 2, 27),
            calculateDateRangeUseCase(
                chartGranularity = ChartGranularity.SEVEN_DAYS,
                chartPageIndex = 49,
                chartPageCount = 50,
                today = today,
            )
        )

        assertEquals(
            LocalDate.of(2025, 1, 24)..LocalDate.of(2025, 1, 30),
            calculateDateRangeUseCase(
                chartGranularity = ChartGranularity.SEVEN_DAYS,
                chartPageIndex = 45,
                chartPageCount = 50,
                today = today,
            )
        )
    }

    @Test
    fun `test invoke() with ChartGranularity_MONTHLY`() {
        assertEquals(
            LocalDate.of(2025, 2, 1)..LocalDate.of(2025, 2, 28),
            calculateDateRangeUseCase(
                chartGranularity = ChartGranularity.MONTHLY,
                chartPageIndex = 49,
                chartPageCount = 50,
                today = today,
            )
        )

        assertEquals(
            LocalDate.of(2024, 10, 1)..LocalDate.of(2024, 10, 31),
            calculateDateRangeUseCase(
                chartGranularity = ChartGranularity.MONTHLY,
                chartPageIndex = 45,
                chartPageCount = 50,
                today = today,
            )
        )
    }

    @Test
    fun `test invoke() with ChartGranularity_THIRTY_DAYS`() {
        assertEquals(
            LocalDate.of(2025, 1, 29)..LocalDate.of(2025, 2, 27),
            calculateDateRangeUseCase(
                chartGranularity = ChartGranularity.THIRTY_DAYS,
                chartPageIndex = 49,
                chartPageCount = 50,
                today = today,
            )
        )

        assertEquals(
            LocalDate.of(2024, 10, 1)..LocalDate.of(2024, 10, 30),
            calculateDateRangeUseCase(
                chartGranularity = ChartGranularity.THIRTY_DAYS,
                chartPageIndex = 45,
                chartPageCount = 50,
                today = today,
            )
        )
    }

    @Test
    fun `test invoke() with ChartGranularity_SIX_WEEKS`() {
        val settings = mockk<UserSettings>().apply {
            every { firstDayOfTheWeek } returns DayOfWeek.MONDAY
        }
        every { userSettingsController.settings } returns settings

        assertEquals(
            LocalDate.of(2025, 1, 17)..LocalDate.of(2025, 2, 27),
            calculateDateRangeUseCase(
                chartGranularity = ChartGranularity.SIX_WEEKS,
                chartPageIndex = 49,
                chartPageCount = 50,
                today = today,
            )
        )

        assertEquals(
            LocalDate.of(2024, 8, 2)..LocalDate.of(2024, 9, 12),
            calculateDateRangeUseCase(
                chartGranularity = ChartGranularity.SIX_WEEKS,
                chartPageIndex = 45,
                chartPageCount = 50,
                today = today,
            )
        )
    }

    @Test
    fun `test invoke() with ChartGranularity_SIX_MONTHS`() {
        val settings = mockk<UserSettings>().apply {
            every { firstDayOfTheWeek } returns DayOfWeek.MONDAY
        }
        every { userSettingsController.settings } returns settings

        assertEquals(
            LocalDate.of(2024, 9, 2)..LocalDate.of(2025, 3, 2),
            calculateDateRangeUseCase(
                chartGranularity = ChartGranularity.SIX_MONTHS,
                chartPageIndex = 49,
                chartPageCount = 50,
                today = today,
            )
        )

        assertEquals(
            LocalDate.of(2022, 8, 15)..LocalDate.of(2023, 2, 19),
            calculateDateRangeUseCase(
                chartGranularity = ChartGranularity.SIX_MONTHS,
                chartPageIndex = 45,
                chartPageCount = 50,
                today = today,
            )
        )
    }

    @Test
    fun `test invoke() with ChartGranularity_YEARLY`() {
        assertEquals(
            LocalDate.of(2025, 1, 1)..LocalDate.of(2025, 12, 31),
            calculateDateRangeUseCase(
                chartGranularity = ChartGranularity.YEARLY,
                chartPageIndex = 49,
                chartPageCount = 50,
                today = today,
            )
        )

        assertEquals(
            LocalDate.of(2021, 1, 1)..LocalDate.of(2021, 12, 31),
            calculateDateRangeUseCase(
                chartGranularity = ChartGranularity.YEARLY,
                chartPageIndex = 45,
                chartPageCount = 50,
                today = today,
            )
        )
    }

    @Test
    fun `test invoke() with ChartGranularity_EIGHT_YEARS`() {
        assertEquals(
            LocalDate.of(2018, 1, 1)..LocalDate.of(2025, 12, 31),
            calculateDateRangeUseCase(
                chartGranularity = ChartGranularity.EIGHT_YEARS,
                chartPageIndex = 49,
                chartPageCount = 50,
                today = today,
            )
        )

        assertEquals(
            LocalDate.of(2010, 1, 1)..LocalDate.of(2017, 12, 31),
            calculateDateRangeUseCase(
                chartGranularity = ChartGranularity.EIGHT_YEARS,
                chartPageIndex = 48,
                chartPageCount = 50,
                today = today,
            )
        )
    }
}
