package com.stt.android.chart.impl.data

import android.content.Context
import androidx.compose.ui.text.AnnotatedString
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartStyle
import com.stt.android.chart.impl.R
import com.stt.android.chart.impl.model.ChartBarDisplayMode
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.screen.GoalEditorViewData
import com.stt.android.chart.impl.screen.GoalViewData
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.activitydata.dailyvalues.ActivityDataDailyRepository
import com.stt.android.domain.activitydata.goals.ActivityDataGoalRepository
import com.stt.android.domain.trenddata.FetchTrendDataUseCase
import com.stt.android.domain.trenddata.TrendData
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import java.time.LocalDate
import javax.inject.Inject
import com.stt.android.R as BaseR

internal class StepDataLoader @Inject constructor(
    @ApplicationContext private val appContext: Context,
    private val activityDataGoalRepository: ActivityDataGoalRepository,
    private val activityDataDailyRepository: ActivityDataDailyRepository,
    private val infoModelFormatter: InfoModelFormatter,
    userSettingsController: UserSettingsController,
    fetchTrendDataUseCase: FetchTrendDataUseCase,
) : TrendDataLoader(appContext, userSettingsController, fetchTrendDataUseCase) {
    fun formatHighlightData(y: Int?): String =
        appContext.getString(BaseR.string.daily_goal_setting_value_1_steps, y ?: 0)

    fun loadGoalData(): GoalViewData = GoalViewData.Goal(
        icon = BaseR.drawable.ic_activity_data_steps,
        iconColor = BaseR.color.activity_data_steps,
        title = R.string.chart_daily_step_target,
        goal = activityDataGoalRepository.fetchStepsGoal()
            .map { steps ->
                appContext.getString(BaseR.string.daily_goal_setting_value_1_steps, steps)
            },
    )

    suspend fun loadGoalEditorData(): GoalEditorViewData = GoalEditorViewData.Editor(
        chartContent = ChartContent.STEPS,
        requiresWatchConnection = true,
        currentGoal = activityDataGoalRepository.fetchStepsGoal().first()
    )

    fun loadChartData(
        chartStyle: ChartStyle,
        chartGranularity: ChartGranularity,
        dateRange: ClosedRange<LocalDate>,
        comparisonDateRange: ClosedRange<LocalDate>?,
    ): Flow<ChartData> = combine(
        loadTrendData(
            chartGranularity = chartGranularity,
            dateRange = dateRange,
        ),
        comparisonDateRange?.let {
            loadTrendData(
                chartGranularity = chartGranularity,
                dateRange = comparisonDateRange,
            )
        } ?: flowOf(emptyList()),
        activityDataDailyRepository.fetchSteps(),
        activityDataGoalRepository.fetchStepsGoal(),
    ) { currentTrendDataList, comparisonTrendDataList, todaySteps, stepsGoal ->
        val goal = stepsGoal.takeIf {
            chartStyle == ChartStyle.SINGLE && chartGranularity in listOf(
                ChartGranularity.WEEKLY,
                ChartGranularity.SEVEN_DAYS,
                ChartGranularity.THIRTY_DAYS,
                ChartGranularity.MONTHLY
            )
        }
        
        val series = listOfNotNull(
            createDataSeries(
                chartStyle = chartStyle,
                chartGranularity = chartGranularity,
                dateRange = dateRange,
                trendDataList = currentTrendDataList,
                todayValue = todaySteps.toFloat(),
                goal = goal,
                color = appContext.getColor(BaseR.color.activity_data_steps),
            ),
            createComparisonDataSeries(
                chartStyle = chartStyle,
                chartGranularity = chartGranularity,
                currentDateRange = dateRange,
                comparisonDateRange = comparisonDateRange,
                trendDataList = comparisonTrendDataList,
            ),
        ).toImmutableList()
        
        val needFormatYAxisLabel = series.any { seriesItem ->
            seriesItem.axisRange.maxY / 3 > 1_000_000
        }
        
        ChartData(
            chartGranularity = chartGranularity,
            series = series,
            highlightEnabled = true,
            goal = goal,
            highlightDecorationLines = persistentMapOf(),
            currentValues = persistentListOf(),
            chartBarDisplayMode = if (comparisonDateRange == null) {
                ChartBarDisplayMode.STACKED
            } else {
                ChartBarDisplayMode.GROUPED
            },
            chartContent = ChartContent.STEPS,
            colorIndicator = null,
            needFormatYAxisLabel = needFormatYAxisLabel,
        )
    }

    override fun getFormatedValue(value: Float): AnnotatedString =
        AnnotatedString(infoModelFormatter.formatValue(SummaryItem.STEPS, value).value.orEmpty())

    override fun getValue(trendData: TrendData): Number = trendData.steps
}
