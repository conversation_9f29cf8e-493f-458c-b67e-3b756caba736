package com.stt.android.chart.impl.data

import android.content.Context
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartStyle
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.ChartType
import com.stt.android.chart.impl.model.epochMonth
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.sleep.FetchSleepUseCase
import com.stt.android.domain.sleep.Sleep
import com.stt.android.domain.trenddata.TrendData
import com.stt.android.domain.trenddata.TrendDataRepository
import com.stt.android.utils.toEpochMilli
import com.suunto.algorithms.data.HeartRate
import com.suunto.algorithms.data.HeartRate.Companion.hz
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import timber.log.Timber
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters
import java.time.temporal.WeekFields
import java.util.Locale
import javax.inject.Inject
import kotlin.math.roundToInt

internal class SleepMinimumHeartRateDataLoader @Inject constructor(
    @ApplicationContext appContext: Context,
    private val fetchSleepUseCase: FetchSleepUseCase,
    private val trendDataRepository: TrendDataRepository,
) : BaseHeartRateDataLoader(appContext) {
    
    private var hasReturnedSelectEntryX = false
    
    private interface SleepMinHeartRateSeriesStrategy {
        fun createSeries(
            from: LocalDate,
            to: LocalDate, 
            dailySleepMinHrs: List<Int>,
            chartColor: Int
        ): ChartData.Series
    }
    
    private val seriesStrategies: Map<ChartGranularity, SleepMinHeartRateSeriesStrategy>
    
    init {
        seriesStrategies = mapOf(
            ChartGranularity.SEVEN_DAYS to DailySleepMinHeartRateSeriesStrategy(),
            ChartGranularity.THIRTY_DAYS to DailySleepMinHeartRateSeriesStrategy(),
            ChartGranularity.SIX_WEEKS to DailySleepMinHeartRateSeriesStrategy(),
            ChartGranularity.WEEKLY to DailySleepMinHeartRateSeriesStrategy(),
            ChartGranularity.MONTHLY to DailySleepMinHeartRateSeriesStrategy(),
            ChartGranularity.SIX_MONTHS to WeeklySleepMinHeartRateSeriesStrategy(),
            ChartGranularity.YEARLY to MonthlySleepMinHeartRateSeriesStrategy(),
            ChartGranularity.EIGHT_YEARS to YearlySleepMinHeartRateSeriesStrategy()
        )
    }
    
    fun loadChartData(
        chartStyle: ChartStyle,
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
    ): Flow<ChartData> {
        val fromMillis = from.atStartOfDay().toEpochMilli()
        val toMillis = to.atStartOfDay().toEpochMilli()

        val sleepFlow = fetchSleepUseCase.fetchSleeps(
            from = from,
            to = to,
        ).catch { emit(emptyList()) }
        
        val trendDataFlow = trendDataRepository.fetchTrendDataForDateRange(
            fromTimestamp = fromMillis,
            toTimestamp = toMillis,
            aggregated = false
        ).catch { emit(emptyList()) }
        
        return combine(trendDataFlow, sleepFlow) { trendDataList, sleepList ->
            val dailySleepMinHrs = calculateDailySleepMinHrs(trendDataList, sleepList, from, to)
            val series = createSleepMinHeartRateSeriesFromData(chartGranularity, from, to, dailySleepMinHrs)
            val selectEntryX = calculateSelectEntryX(chartGranularity, from, to, dailySleepMinHrs)
            
            Pair(series, selectEntryX)
        }.map { (series, selectEntryX) ->
            val modifiedSeries = series.copy(
                chartType = ChartType.LINE,
                lineConfig = singleLineChartConfig
            )
            
            ChartData(
                chartGranularity = chartGranularity,
                series = persistentListOf(modifiedSeries),
                highlightEnabled = chartStyle == ChartStyle.SINGLE,
                goal = null,
                highlightDecorationLines = persistentMapOf(),
                currentValues = persistentListOf(),
                chartContent = ChartContent.SLEEPING_MINIMUM_HEART_RATE,
                colorIndicator = null,
                selectEntryX = selectEntryX
            )
        }.catch {
            emit(ChartData(
                chartGranularity = chartGranularity,
                series = persistentListOf(),
                highlightEnabled = false,
                goal = null,
                highlightDecorationLines = persistentMapOf(),
                currentValues = persistentListOf(),
                chartContent = ChartContent.SLEEPING_MINIMUM_HEART_RATE,
                colorIndicator = null,
            ))
        }
    }
    
    private fun calculateSelectEntryX(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        dailySleepMinHrs: List<Int>
    ): Long? {
        if (hasReturnedSelectEntryX) {
            return null
        }
        if (chartGranularity != ChartGranularity.SEVEN_DAYS) {
            return null
        }
        val today = LocalDate.now()
        if (today.isBefore(from) || today.isAfter(to)) {
            return null
        }
        
        val dayIndex = ChronoUnit.DAYS.between(from, today).toInt()
        val todayHasData = if (dayIndex >= 0 && dayIndex < dailySleepMinHrs.size) {
            dailySleepMinHrs[dayIndex] != NO_DATA_VALUE
        } else {
            false
        }
        
        return if (todayHasData) {
            hasReturnedSelectEntryX = true
            today.toEpochDay()
        } else {
            null
        }
    }

    private fun createSleepMinHeartRateSeriesFromData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        dailySleepMinHrs: List<Int>
    ): ChartData.Series {
        val chartColor = getChartColor()
        
        val strategy = seriesStrategies[chartGranularity]
            ?: throw UnsupportedOperationException("Unsupported chart granularity: ${chartGranularity.name}")
            
        return strategy.createSeries(from, to, dailySleepMinHrs, chartColor)
    }
    
    private inner class DailySleepMinHeartRateSeriesStrategy : SleepMinHeartRateSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            dailySleepMinHrs: List<Int>,
            chartColor: Int
        ): ChartData.Series {
            val entries = mutableListOf<ChartData.Entry>()
            val days = ChronoUnit.DAYS.between(from, to.plusDays(1)).toInt()
            
            var totalValue = 0f
            var totalDays = 0
            
            val validDailySleepMinHrs = if (dailySleepMinHrs.size == days) {
                dailySleepMinHrs
            } else {
                val paddedList = dailySleepMinHrs.toMutableList()
                while (paddedList.size < days) {
                    paddedList.add(NO_DATA_VALUE)
                }
                paddedList.take(days)
            }
            
            var currentDate = from
            validDailySleepMinHrs.forEach { hr ->
                val x = currentDate.toEpochDay()
                
                if (hr != NO_DATA_VALUE) {
                    entries.add(ChartData.Entry(x = x, y = hr))
                    totalValue += hr
                    totalDays++
                }
                
                currentDate = currentDate.plusDays(1)
            }
            
            val entriesImmutable = entries.toImmutableList()

            val averageValue = if (totalDays > 0) totalValue / totalDays else 0f
            
            val heartRateValues = entriesImmutable.map { it.y.toFloat() }.filter { it > 0 }
            val minHeartRate = heartRateValues.minOrNull()?.toInt() ?: 0
            val maxHeartRate = heartRateValues.maxOrNull()?.toInt() ?: 0
            val (minY, maxY) = adjustYAxisRangeForLineChart(minHeartRate, maxHeartRate)
            
            return ChartData.Series(
                chartType = ChartType.LINE,
                color = chartColor,
                axisRange = ChartData.AxisRange(
                    minX = from.toEpochDay().toDouble(),
                    maxX = to.toEpochDay().toDouble(),
                    minY = minY,
                    maxY = maxY,
                ),
                entries = entriesImmutable,
                value = formatBpmValue(averageValue),
                candlestickEntries = persistentListOf(),
                lineConfig = combineLineChartConfig,
                backgroundRegion = null,
                groupStackBarStyle = null,
                average = averageValue.takeIf { it != 0f },
            )
        }
    }
    
    private inner class WeeklySleepMinHeartRateSeriesStrategy : SleepMinHeartRateSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            dailySleepMinHrs: List<Int>,
            chartColor: Int
        ): ChartData.Series {
            val entries = mutableListOf<ChartData.Entry>()
            
            var totalValue = 0f
            var totalDays = 0
            
            val dailyData = mutableListOf<Pair<LocalDate, Int>>()
            var currentDate = from
            for (i in dailySleepMinHrs.indices) {
                dailyData.add(currentDate to dailySleepMinHrs[i])
                currentDate = currentDate.plusDays(1)
            }
            
            val dataByWeek = dailyData.groupBy { (date, _) ->
                date.with(TemporalAdjusters.previousOrSame(WeekFields.of(Locale.getDefault()).firstDayOfWeek))
            }
            
            val weeks = dataByWeek.keys.sortedBy { it.toEpochDay() }
            weeks.forEach { weekStartDate ->
                val weekData = dataByWeek[weekStartDate] ?: emptyList()
                
                val weekHrValues = weekData
                    .mapNotNull { (_, hr) -> if (hr != NO_DATA_VALUE) hr else null }
                
                if (weekHrValues.isNotEmpty()) {
                    val weeklyAverage = weekHrValues.average()
                    val weeklyAverageInt = weeklyAverage.roundToInt()
                    
                    entries.add(ChartData.Entry(x = weekStartDate.toEpochDay(), y = weeklyAverageInt))
                    totalValue += weeklyAverageInt
                    totalDays++
                }
            }
            
            val entriesImmutable = entries.toImmutableList()
            
            val fromWeekStart = from.with(TemporalAdjusters.previousOrSame(WeekFields.of(Locale.getDefault()).firstDayOfWeek))
            val toWeekStart = to.with(TemporalAdjusters.previousOrSame(WeekFields.of(Locale.getDefault()).firstDayOfWeek))
            
            val averageValue = if (totalDays > 0) totalValue / totalDays else 0f
            
            val heartRateValues = entriesImmutable.map { it.y.toFloat() }.filter { it > 0 }
            val minHeartRate = heartRateValues.minOrNull()?.toInt() ?: 0
            val maxHeartRate = heartRateValues.maxOrNull()?.toInt() ?: 0
            val (minY, maxY) = adjustYAxisRangeForLineChart(minHeartRate, maxHeartRate)
            
            return ChartData.Series(
                chartType = ChartType.LINE,
                color = chartColor,
                axisRange = ChartData.AxisRange(
                    minX = fromWeekStart.toEpochDay().toDouble(),
                    maxX = toWeekStart.toEpochDay().toDouble(),
                    minY = minY,
                    maxY = maxY,
                ),
                entries = entriesImmutable,
                value = formatBpmValue(averageValue),
                candlestickEntries = persistentListOf(),
                lineConfig = combineLineChartConfig,
                backgroundRegion = null,
                groupStackBarStyle = null,
                average = averageValue.takeIf { it != 0f },
            )
        }
    }
    
    private inner class MonthlySleepMinHeartRateSeriesStrategy : SleepMinHeartRateSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            dailySleepMinHrs: List<Int>,
            chartColor: Int
        ): ChartData.Series {
            val entries = mutableListOf<ChartData.Entry>()
            
            var totalValue = 0f
            var totalDays = 0
            
            val dailyData = mutableListOf<Pair<LocalDate, Int>>()
            var currentDate = from
            for (i in dailySleepMinHrs.indices) {
                dailyData.add(currentDate to dailySleepMinHrs[i])
                currentDate = currentDate.plusDays(1)
            }
            
            val dataByMonth = dailyData.groupBy { (date, _) ->
                date.withDayOfMonth(1)
            }
            
            val months = dataByMonth.keys.sortedBy { it.epochMonth }
            months.forEach { monthStartDate ->
                val monthData = dataByMonth[monthStartDate] ?: emptyList()
                
                val monthHrValues = monthData
                    .mapNotNull { (_, hr) -> if (hr != NO_DATA_VALUE) hr else null }
                
                if (monthHrValues.isNotEmpty()) {
                    val monthlyAverage = monthHrValues.average()
                    val monthlyAverageInt = monthlyAverage.roundToInt()
                    
                    entries.add(ChartData.Entry(x = monthStartDate.epochMonth.toLong(), y = monthlyAverageInt))
                    totalValue += monthlyAverageInt
                    totalDays++
                }
            }
            
            val entriesImmutable = entries.toImmutableList()

            val averageValue = if (totalDays > 0) totalValue / totalDays else 0f
            
            val heartRateValues = entriesImmutable.map { it.y.toFloat() }.filter { it > 0 }
            val minHeartRate = heartRateValues.minOrNull()?.toInt() ?: 0
            val maxHeartRate = heartRateValues.maxOrNull()?.toInt() ?: 0
            val (minY, maxY) = adjustYAxisRangeForLineChart(minHeartRate, maxHeartRate)
            
            return ChartData.Series(
                chartType = ChartType.LINE,
                color = chartColor,
                axisRange = ChartData.AxisRange(
                    minX = from.withDayOfMonth(1).epochMonth.toDouble(),
                    maxX = to.withDayOfMonth(1).epochMonth.toDouble(),
                    minY = minY,
                    maxY = maxY,
                ),
                entries = entriesImmutable,
                value = formatBpmValue(averageValue),
                candlestickEntries = persistentListOf(),
                lineConfig = combineLineChartConfig,
                backgroundRegion = null,
                groupStackBarStyle = null,
                average = averageValue.takeIf { it != 0f },
            )
        }
    }

    private inner class YearlySleepMinHeartRateSeriesStrategy : SleepMinHeartRateSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            dailySleepMinHrs: List<Int>,
            chartColor: Int
        ): ChartData.Series {
            val entries = mutableListOf<ChartData.Entry>()
            
            var totalValue = 0f
            var totalYears = 0
            
            val dailyData = mutableListOf<Pair<LocalDate, Int>>()
            var currentDate = from
            for (i in dailySleepMinHrs.indices) {
                dailyData.add(currentDate to dailySleepMinHrs[i])
                currentDate = currentDate.plusDays(1)
            }
            
            val dataByYear = dailyData.groupBy { (date, _) ->
                date.year
            }
            
            val years = dataByYear.keys.sorted()
            years.forEach { year ->
                val yearData = dataByYear[year] ?: emptyList()
                
                val yearHrValues = yearData
                    .mapNotNull { (_, hr) -> if (hr != NO_DATA_VALUE) hr else null }
                
                if (yearHrValues.isNotEmpty()) {
                    val yearlyAverage = yearHrValues.average()
                    val yearlyAverageInt = yearlyAverage.roundToInt()
                    
                    entries.add(ChartData.Entry(x = year.toLong(), y = yearlyAverageInt))
                    totalValue += yearlyAverageInt
                    totalYears++
                }
            }
            
            val entriesImmutable = entries.toImmutableList()

            val averageValue = if (totalYears > 0) totalValue / totalYears else 0f
            
            val heartRateValues = entriesImmutable.map { it.y.toFloat() }.filter { it > 0 }
            val minHeartRate = heartRateValues.minOrNull()?.toInt() ?: 0
            val maxHeartRate = heartRateValues.maxOrNull()?.toInt() ?: 0
            val (minY, maxY) = adjustYAxisRangeForLineChart(minHeartRate, maxHeartRate)
            
            return ChartData.Series(
                chartType = ChartType.LINE,
                color = chartColor,
                axisRange = ChartData.AxisRange(
                    minX = from.year.toDouble(),
                    maxX = to.year.toDouble(),
                    minY = minY,
                    maxY = maxY,
                ),
                entries = entriesImmutable,
                value = formatBpmValue(averageValue),
                candlestickEntries = persistentListOf(),
                lineConfig = combineLineChartConfig,
                backgroundRegion = null,
                groupStackBarStyle = null,
                average = averageValue.takeIf { it != 0f },
            )
        }
    }

    private fun calculateDailySleepMinHrs(
        trendDataList: List<TrendData>,
        sleepList: List<Sleep>,
        from: LocalDate,
        to: LocalDate
    ): List<Int> {
        val trendDataByDate = trendDataList.groupBy { it.timeISO8601.toLocalDate() }
        val sleepTimeRanges = sleepList.mapNotNull { it.longSleep }.map { it.fellAsleep..it.wokeUp }
        val dailySleepMinHrs = mutableListOf<Int>()

        var currentDate = from
        while (currentDate <= to) {
            val dataForDay = trendDataByDate[currentDate] ?: emptyList()
            val minHrForDay = dataForDay
                .filter { trendData -> trendData.isInSleepTime(sleepTimeRanges) }
                // fallback to hr if separate hrMin is empty
                .mapNotNull { trendData ->
                    val hr = trendData.hr?.takeIf { trendData.hasHr }
                    val minHr = trendData.hrMin
                    if (minHr != null && hr != null) {
                        minOf(minHr, hr)
                    } else {
                        minHr ?: hr
                    }
                }.minOrNull()
                ?.hz
                ?: HeartRate.ZERO
                
            if (minHrForDay != HeartRate.ZERO) {
                dailySleepMinHrs.add(minHrForDay.inBpm.roundToInt())
            } else {
                dailySleepMinHrs.add(NO_DATA_VALUE)
            }
            
            currentDate = currentDate.plusDays(1)
        }

        return dailySleepMinHrs
    }

    private fun TrendData.isInSleepTime(sleepTimeRanges: List<LongRange>): Boolean {
        sleepTimeRanges.forEach { range ->
            if (timestamp in range) {
                return true
            }
        }
        return false
    }


    suspend fun getMinimumSleepHeartRate(from: LocalDate, to: LocalDate): Int {
        val trendDataList = loadTrendData(from, to)
        val sleepList = loadSleepData(from, to)
        
        val dailySleepMinHrs = calculateDailySleepMinHrs(trendDataList, sleepList, from, to)
        
        return dailySleepMinHrs.filter { it != NO_DATA_VALUE }.minOrNull() ?: NO_DATA_VALUE
    }


    suspend fun getMinimumSleepHeartRateRange(from: LocalDate, to: LocalDate): Pair<Int, Int> {
        val trendDataList = loadTrendData(from, to)
        val sleepList = loadSleepData(from, to)
        
        val dailySleepMinHrs = calculateDailySleepMinHrs(trendDataList, sleepList, from, to)
        
        val validValues = dailySleepMinHrs.filter { it != NO_DATA_VALUE }
        val minHeartRate = validValues.minOrNull() ?: NO_DATA_VALUE
        val maxHeartRate = validValues.maxOrNull() ?: NO_DATA_VALUE
        
        return Pair(minHeartRate, maxHeartRate)
    }


    private suspend fun loadTrendData(from: LocalDate, to: LocalDate): List<TrendData> {
        val fromMillis = from.atStartOfDay().toEpochMilli()
        val toMillis = to.atStartOfDay().toEpochMilli()
        
        return runSuspendCatching {
            trendDataRepository.fetchTrendDataForDateRange(
                fromTimestamp = fromMillis,
                toTimestamp = toMillis,
                aggregated = false
            ).first()
        }.getOrElse { e ->
            Timber.w(e, "Failed to load trend data")
            emptyList()
        }
    }


    private suspend fun loadSleepData(from: LocalDate, to: LocalDate): List<Sleep> {
        return runSuspendCatching {
            fetchSleepUseCase.fetchSleeps(
                from = from,
                to = to,
            ).first()
        }.getOrElse { e ->
            Timber.w(e, "Failed to load sleep data")
            emptyList()
        }
    }

    suspend fun createChartSeries(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        trendDataList: List<TrendData>,
        chartColor: Int
    ): List<ChartData.Series> {
        val sleepList = loadSleepData(from, to)
        val dailySleepMinHrs = calculateDailySleepMinHrs(trendDataList, sleepList, from, to)
        
        val strategy = seriesStrategies[chartGranularity]
            ?: throw IllegalArgumentException("Unsupported granularity: $chartGranularity")
            
        return listOf(strategy.createSeries(from, to, dailySleepMinHrs, chartColor))
    }
} 
