package com.stt.android.chart.impl.data

import android.content.Context
import com.soy.algorithms.recovery.calculateRestHeartRate
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartStyle
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.ChartType
import com.stt.android.chart.impl.model.epochMonth
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.sleep.FetchSleepUseCase
import com.stt.android.domain.sleep.Sleep
import com.stt.android.domain.trenddata.TrendData
import com.stt.android.domain.trenddata.TrendDataRepository
import com.stt.android.utils.averageOfDouble
import com.stt.android.utils.takeIfNotNaN
import com.stt.android.utils.toEpochMilli
import com.suunto.algorithms.data.HeartRate
import com.suunto.algorithms.data.HeartRate.Companion.hz
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import timber.log.Timber
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.temporal.TemporalAdjusters
import java.time.temporal.WeekFields
import java.util.Locale
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.math.roundToInt

internal class RestingHeartRateDataLoader @Inject constructor(
    @ApplicationContext appContext: Context,
    private val fetchSleepUseCase: FetchSleepUseCase,
    private val trendDataRepository: TrendDataRepository,
) : BaseHeartRateDataLoader(appContext) {
    
    private var hasReturnedSelectEntryX = false
    
    private interface RestingHeartRateSeriesStrategy {
        fun createSeries(
            from: LocalDate,
            to: LocalDate, 
            trendDataList: List<TrendData>,
            sleepList: List<Sleep>,
            chartColor: Int
        ): ChartData.Series
    }
    
    private val seriesStrategies: Map<ChartGranularity, RestingHeartRateSeriesStrategy>
    
    init {
        seriesStrategies = mapOf(
            ChartGranularity.SEVEN_DAYS to DailyRestingHeartRateSeriesStrategy(),
            ChartGranularity.THIRTY_DAYS to DailyRestingHeartRateSeriesStrategy(),
            ChartGranularity.SIX_WEEKS to DailyRestingHeartRateSeriesStrategy(),
            ChartGranularity.WEEKLY to DailyRestingHeartRateSeriesStrategy(),
            ChartGranularity.MONTHLY to DailyRestingHeartRateSeriesStrategy(),
            ChartGranularity.SIX_MONTHS to WeeklyRestingHeartRateSeriesStrategy(),
            ChartGranularity.YEARLY to MonthlyRestingHeartRateSeriesStrategy(),
            ChartGranularity.EIGHT_YEARS to YearlyRestingHeartRateSeriesStrategy()
        )
    }

    private fun getSleepRange(fromDate: LocalDate, toDate: LocalDate): Pair<Long, Long> {
        val fromMillis = fromDate.minusDays(1).atTime(12, 0).toEpochMilli()
        val toMillis = toDate.atTime(12, 0).toEpochMilli()
        return Pair(fromMillis, toMillis)
    }

    private fun calculateSelectEntryX(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        trendDataList: List<TrendData>,
        sleepList: List<Sleep>
    ): Long? {
        if (hasReturnedSelectEntryX) {
            return null
        }
        if (chartGranularity != ChartGranularity.SEVEN_DAYS) {
            return null
        }
        val today = LocalDate.now()
        if (today.isBefore(from) || today.isAfter(to)) {
            return null
        }
        
        val todayTrendData = trendDataList.filter { trendData ->
            Instant.ofEpochMilli(trendData.timestamp + TimeUnit.HOURS.toMillis(12))
                .atZone(ZoneId.systemDefault())
                .toLocalDate() == today
        }
        
        val todaySleepData = sleepList.filter { sleep ->
            Instant.ofEpochMilli(sleep.timestamp)
                .atZone(ZoneId.systemDefault())
                .toLocalDate() == today
        }
        
        val todayRestingHr = getRestingHeartRate(todayTrendData, todaySleepData)
        
        return if (todayRestingHr != null && todayRestingHr != NO_DATA_VALUE) {
            hasReturnedSelectEntryX = true
            today.toEpochDay()
        } else {
            null
        }
    }

    fun loadChartData(
        chartStyle: ChartStyle,
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
    ): Flow<ChartData> {
        val sleepTimeRange = getSleepRange(from, to)
        val fromMillis = sleepTimeRange.first
        val toMillis = sleepTimeRange.second
        
        val trendDataFlow = trendDataRepository.fetchTrendDataForDateRange(
            fromTimestamp = fromMillis,
            toTimestamp = toMillis,
            aggregated = false
        )
        
        val sleepFlow = fetchSleepUseCase.fetchSleeps(
            from = from,
            to = to,
        ).catch { emit(emptyList()) }
        
        return combine(trendDataFlow, sleepFlow) { trendDataList, sleepList ->
            val series = createRestingHeartRateSeries(chartGranularity, from, to, trendDataList, sleepList)
            val selectEntryX = calculateSelectEntryX(chartGranularity, from, to, trendDataList, sleepList)
            Pair(series, selectEntryX)
        }.map { (series, selectEntryX) ->
            val modifiedSeries = series.copy(
                chartType = ChartType.LINE,
                lineConfig = singleLineChartConfig
            )
            
            ChartData(
                chartGranularity = chartGranularity,
                series = persistentListOf(modifiedSeries),
                highlightEnabled = chartStyle == ChartStyle.SINGLE,
                goal = null,
                highlightDecorationLines = persistentMapOf(),
                currentValues = persistentListOf(),
                chartContent = ChartContent.RESTING_HEART_RATE,
                colorIndicator = null,
                selectEntryX = selectEntryX,
            )
        }
    }
    

    private fun createRestingHeartRateSeries(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        trendDataList: List<TrendData>,
        sleepList: List<Sleep>
    ): ChartData.Series {
        val chartColor = getChartColor()
        
        val strategy = seriesStrategies[chartGranularity]
            ?: throw UnsupportedOperationException("Unsupported chart granularity: ${chartGranularity.name}")
            
        return strategy.createSeries(from, to, trendDataList, sleepList, chartColor)
    }
    

    private fun getRestingHeartRate(
        dayTrendData: List<TrendData>,
        daySleepData: List<Sleep>
    ): Int? {
        val sleepTimeRanges = daySleepData.mapNotNull { sleep ->
            sleep.longSleep?.let { it.fellAsleep..it.wokeUp }
        }
        
        if (sleepTimeRanges.isEmpty()) {
            return null
        }
        
        val trendDataListByRange = mutableMapOf<LongRange, MutableList<TrendData>>()
        dayTrendData.forEach { trendData ->
            sleepTimeRanges.forEach { range ->
                if (trendData.timestamp in range) {
                    val list = trendDataListByRange.getOrPut(range) { mutableListOf() }
                    list.add(trendData)
                }
            }
        }
        
        if (trendDataListByRange.isEmpty()) {
            return null
        }
        
        val restHeartRates = trendDataListByRange.map { (_, trendList) ->
            calculateRestHeartRate(trendList.mapNotNull { it.hr?.hz })
        }
        
        return restHeartRates.averageOfDouble(HeartRate::inBpm)
            .takeIfNotNaN()
            ?.roundToInt()
            ?: 0
    }


    private fun calculateDailyRestingHrs(
        trendDataList: List<TrendData>,
        sleepList: List<Sleep>,
        from: LocalDate,
        to: LocalDate
    ): List<Int> {
        val trendDataListByDate = trendDataList.groupBy { trendData ->
            Instant.ofEpochMilli(trendData.timestamp + TimeUnit.HOURS.toMillis(12))
                .atZone(ZoneId.systemDefault())
                .toLocalDate()
        }
        
        val sleepListByDate = sleepList.groupBy { sleep ->
            Instant.ofEpochMilli(sleep.timestamp)
                .atZone(ZoneId.systemDefault())
                .toLocalDate()
        }
        
        val dailyRestingHrs = mutableListOf<Int>()

        var currentDate = from
        while (currentDate <= to) {
            val dataForDay = trendDataListByDate[currentDate] ?: emptyList()
            val sleepsForDay = sleepListByDate[currentDate] ?: emptyList()
            
            val restingHr = getRestingHeartRate(dataForDay, sleepsForDay) ?: NO_DATA_VALUE
            
            dailyRestingHrs.add(restingHr)
            currentDate = currentDate.plusDays(1)
        }

        return dailyRestingHrs
    }
    
    private inner class DailyRestingHeartRateSeriesStrategy : RestingHeartRateSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            sleepList: List<Sleep>,
            chartColor: Int
        ): ChartData.Series {
            val dailyRestingHrs = calculateDailyRestingHrs(trendDataList, sleepList, from, to)
            
            val entries = mutableListOf<ChartData.Entry>()
            var currentDate = from
            
            var totalValue = 0f
            var totalDays = 0
            
            dailyRestingHrs.forEach { hr ->
                val x = currentDate.toEpochDay()
                
                if (hr != NO_DATA_VALUE) {
                    entries.add(ChartData.Entry(x = x, y = hr))
                    totalValue += hr
                    totalDays++
                }
                
                currentDate = currentDate.plusDays(1)
            }
            
            val entriesImmutable = entries.toImmutableList()

            val averageValue = if (totalDays > 0) totalValue / totalDays else 0f
            
            val heartRateValues = entriesImmutable.map { it.y.toFloat() }.filter { it > 0 }
            val minHeartRate = heartRateValues.minOrNull()?.toInt() ?: 0
            val maxHeartRate = heartRateValues.maxOrNull()?.toInt() ?: 0
            val (minY, maxY) = adjustYAxisRangeForLineChart(minHeartRate, maxHeartRate)
            
            return ChartData.Series(
                chartType = ChartType.LINE,
                color = chartColor,
                axisRange = ChartData.AxisRange(
                    minX = from.toEpochDay().toDouble(),
                    maxX = to.toEpochDay().toDouble(),
                    minY = minY,
                    maxY = maxY,
                ),
                entries = entriesImmutable,
                value = formatBpmValue(averageValue),
                candlestickEntries = persistentListOf(),
                lineConfig = combineLineChartConfig,
                backgroundRegion = null,
                groupStackBarStyle = null,
                average = averageValue.takeIf { it != 0f },
            )
        }
    }
    
    private inner class WeeklyRestingHeartRateSeriesStrategy : RestingHeartRateSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            sleepList: List<Sleep>,
            chartColor: Int
        ): ChartData.Series {
            val dailyRestingHrs = calculateDailyRestingHrs(trendDataList, sleepList, from, to)
            
            val entries = mutableListOf<ChartData.Entry>()
            
            var totalValue = 0f
            var totalDays = 0
            
            val dailyData = mutableListOf<Pair<LocalDate, Int>>()
            var currentDate = from
            for (i in dailyRestingHrs.indices) {
                dailyData.add(currentDate to dailyRestingHrs[i])
                currentDate = currentDate.plusDays(1)
            }
            
            val dataByWeek = dailyData.groupBy { (date, _) ->
                date.with(TemporalAdjusters.previousOrSame(WeekFields.of(Locale.getDefault()).firstDayOfWeek))
            }
            
            val weeks = dataByWeek.keys.sortedBy { it.toEpochDay() }
            weeks.forEach { weekStartDate ->
                val weekData = dataByWeek[weekStartDate] ?: emptyList()
                
                val weekHrValues = weekData
                    .mapNotNull { (_, hr) -> if (hr != NO_DATA_VALUE) hr else null }
                
                if (weekHrValues.isNotEmpty()) {
                    val weeklyAverage = weekHrValues.average()
                    val weeklyAverageInt = weeklyAverage.roundToInt()
                    
                    if (weeklyAverageInt > 0) {
                        entries.add(ChartData.Entry(x = weekStartDate.toEpochDay(), y = weeklyAverageInt))
                        totalValue += weeklyAverageInt
                        totalDays++
                    }
                }
            }
            
            val entriesImmutable = entries.toImmutableList()
            
            val fromWeekStart = from.with(TemporalAdjusters.previousOrSame(WeekFields.of(Locale.getDefault()).firstDayOfWeek))
            val toWeekStart = to.with(TemporalAdjusters.previousOrSame(WeekFields.of(Locale.getDefault()).firstDayOfWeek))

            val averageValue = if (totalDays > 0) totalValue / totalDays else 0f
            
            val heartRateValues = entriesImmutable.map { it.y.toFloat() }.filter { it > 0 }
            val minHeartRate = heartRateValues.minOrNull()?.toInt() ?: 0
            val maxHeartRate = heartRateValues.maxOrNull()?.toInt() ?: 0
            val (minY, maxY) = adjustYAxisRangeForLineChart(minHeartRate, maxHeartRate)
            
            return ChartData.Series(
                chartType = ChartType.LINE,
                color = chartColor,
                axisRange = ChartData.AxisRange(
                    minX = fromWeekStart.toEpochDay().toDouble(),
                    maxX = toWeekStart.toEpochDay().toDouble(),
                    minY = minY,
                    maxY = maxY,
                ),
                entries = entriesImmutable,
                value = formatBpmValue(averageValue),
                candlestickEntries = persistentListOf(),
                lineConfig = combineLineChartConfig,
                backgroundRegion = null,
                groupStackBarStyle = null,
                average = averageValue.takeIf { it != 0f },
            )
        }
    }
    
    private inner class MonthlyRestingHeartRateSeriesStrategy : RestingHeartRateSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            sleepList: List<Sleep>,
            chartColor: Int
        ): ChartData.Series {
            val dailyRestingHrs = calculateDailyRestingHrs(trendDataList, sleepList, from, to)
            
            val entries = mutableListOf<ChartData.Entry>()
            
            var totalValue = 0f
            var totalDays = 0
            
            val dailyData = mutableListOf<Pair<LocalDate, Int>>()
            var currentDate = from
            for (i in dailyRestingHrs.indices) {
                dailyData.add(currentDate to dailyRestingHrs[i])
                currentDate = currentDate.plusDays(1)
            }
            
            val dataByMonth = dailyData.groupBy { (date, _) ->
                date.withDayOfMonth(1)
            }
            
            val months = dataByMonth.keys.sortedBy { it.epochMonth }
            months.forEach { monthStartDate ->
                val monthData = dataByMonth[monthStartDate] ?: emptyList()
                
                val monthHrValues = monthData
                    .mapNotNull { (_, hr) -> if (hr != NO_DATA_VALUE) hr else null }
                
                if (monthHrValues.isNotEmpty()) {
                    val monthlyAverage = monthHrValues.average()
                    val monthlyAverageInt = monthlyAverage.roundToInt()
                    
                    if (monthlyAverageInt > 0) {
                        entries.add(ChartData.Entry(x = monthStartDate.epochMonth.toLong(), y = monthlyAverageInt))
                        totalValue += monthlyAverageInt
                        totalDays++
                    }
                }
            }
            
            val entriesImmutable = entries.toImmutableList()

            val averageValue = if (totalDays > 0) totalValue / totalDays else 0f
            
            val heartRateValues = entriesImmutable.map { it.y.toFloat() }.filter { it > 0 }
            val minHeartRate = heartRateValues.minOrNull()?.toInt() ?: 0
            val maxHeartRate = heartRateValues.maxOrNull()?.toInt() ?: 0
            val (minY, maxY) = adjustYAxisRangeForLineChart(minHeartRate, maxHeartRate)
            
            return ChartData.Series(
                chartType = ChartType.LINE,
                color = chartColor,
                axisRange = ChartData.AxisRange(
                    minX = from.epochMonth.toDouble(),
                    maxX = to.epochMonth.toDouble(),
                    minY = minY,
                    maxY = maxY,
                ),
                entries = entriesImmutable,
                value = formatBpmValue(averageValue),
                candlestickEntries = persistentListOf(),
                lineConfig = combineLineChartConfig,
                backgroundRegion = null,
                groupStackBarStyle = null,
                average = averageValue.takeIf { it != 0f },
            )
        }
    }

    private inner class YearlyRestingHeartRateSeriesStrategy : RestingHeartRateSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            sleepList: List<Sleep>,
            chartColor: Int
        ): ChartData.Series {
            val dailyRestingHrs = calculateDailyRestingHrs(trendDataList, sleepList, from, to)
            
            val entries = mutableListOf<ChartData.Entry>()
            
            var totalValue = 0f
            var totalYears = 0
            
            val dailyData = mutableListOf<Pair<LocalDate, Int>>()
            var currentDate = from
            for (i in dailyRestingHrs.indices) {
                dailyData.add(currentDate to dailyRestingHrs[i])
                currentDate = currentDate.plusDays(1)
            }
            
            val dataByYear = dailyData.groupBy { (date, _) ->
                date.year
            }
            
            val years = dataByYear.keys.sorted()
            years.forEach { year ->
                val yearData = dataByYear[year] ?: emptyList()
                
                val yearHrValues = yearData
                    .mapNotNull { (_, hr) -> if (hr != NO_DATA_VALUE) hr else null }
                
                if (yearHrValues.isNotEmpty()) {
                    val yearlyAverage = yearHrValues.average()
                    val yearlyAverageInt = yearlyAverage.roundToInt()
                    
                    if (yearlyAverageInt > 0) {
                        entries.add(ChartData.Entry(x = year.toLong(), y = yearlyAverageInt))
                        totalValue += yearlyAverageInt
                        totalYears++
                    }
                }
            }
            
            val entriesImmutable = entries.toImmutableList()

            val averageValue = if (totalYears > 0) totalValue / totalYears else 0f
            
            val heartRateValues = entriesImmutable.map { it.y.toFloat() }.filter { it > 0 }
            val minHeartRate = heartRateValues.minOrNull()?.toInt() ?: 0
            val maxHeartRate = heartRateValues.maxOrNull()?.toInt() ?: 0
            val (minY, maxY) = adjustYAxisRangeForLineChart(minHeartRate, maxHeartRate)
            
            return ChartData.Series(
                chartType = ChartType.LINE,
                color = chartColor,
                axisRange = ChartData.AxisRange(
                    minX = from.year.toDouble(),
                    maxX = to.year.toDouble(),
                    minY = minY,
                    maxY = maxY,
                ),
                entries = entriesImmutable,
                value = formatBpmValue(averageValue),
                candlestickEntries = persistentListOf(),
                lineConfig = combineLineChartConfig,
                backgroundRegion = null,
                groupStackBarStyle = null,
                average = averageValue.takeIf { it != 0f },
            )
        }
    }

    suspend fun getRestingHeartRate(from: LocalDate, to: LocalDate): Int {
        val trendDataList = loadTrendData(from, to)
        val sleepList = loadSleepData(from, to)
        
        val trendDataByDate = groupTrendDataByDate(trendDataList)
        val sleepByDate = groupSleepByDate(sleepList)
        
        val dailyRestingHrs = calculateDailyRestingHrs(trendDataByDate, sleepByDate, from, to)
        
        return dailyRestingHrs.filter { it > 0 }.minOrNull() ?: NO_DATA_VALUE
    }


    suspend fun getRestingHeartRateRange(from: LocalDate, to: LocalDate): Pair<Int, Int> {
        val trendDataList = loadTrendData(from, to)
        val sleepList = loadSleepData(from, to)
        
        val trendDataByDate = groupTrendDataByDate(trendDataList)
        val sleepByDate = groupSleepByDate(sleepList)
        
        val dailyRestingHrs = calculateDailyRestingHrs(trendDataByDate, sleepByDate, from, to)
        
        val validValues = dailyRestingHrs.filter { it > 0 }
        val minRestingHr = validValues.minOrNull() ?: NO_DATA_VALUE
        val maxRestingHr = validValues.maxOrNull() ?: NO_DATA_VALUE
        
        return Pair(minRestingHr, maxRestingHr)
    }


    private suspend fun loadTrendData(from: LocalDate, to: LocalDate): List<TrendData> {
        val sleepTimeRange = getSleepRange(from, to)
        val fromMillis = sleepTimeRange.first
        val toMillis = sleepTimeRange.second
        
        return runSuspendCatching {
            trendDataRepository.fetchTrendDataForDateRange(
                fromTimestamp = fromMillis,
                toTimestamp = toMillis,
                aggregated = false
            ).first()
        }.getOrElse { e ->
            Timber.w(e, "Failed to fetch trend data")
            emptyList()
        }
    }


    private suspend fun loadSleepData(from: LocalDate, to: LocalDate): List<Sleep> {
        return runSuspendCatching {
            fetchSleepUseCase.fetchSleeps(
                from = from,
                to = to,
            ).first()
        }.getOrElse { e ->
            Timber.w(e, "Failed to fetch sleep data")
            emptyList()
        }
    }

    private fun groupTrendDataByDate(trendDataList: List<TrendData>): Map<LocalDate, List<TrendData>> {
        return trendDataList.groupBy { trendData ->
            Instant.ofEpochMilli(trendData.timestamp + TimeUnit.HOURS.toMillis(12))
                .atZone(ZoneId.systemDefault())
                .toLocalDate()
        }
    }

    private fun groupSleepByDate(sleepList: List<Sleep>): Map<LocalDate, List<Sleep>> {
        return sleepList.groupBy { sleep ->
            Instant.ofEpochMilli(sleep.timestamp)
                .atZone(ZoneId.systemDefault())
                .toLocalDate()
        }
    }


    private fun calculateDailyRestingHrs(
        trendDataByDate: Map<LocalDate, List<TrendData>>,
        sleepByDate: Map<LocalDate, List<Sleep>>,
        from: LocalDate,
        to: LocalDate
    ): List<Int> {
        val dailyRestingHrs = mutableListOf<Int>()
        
        var currentDate = from
        while (currentDate <= to) {
            val dataForDay = trendDataByDate[currentDate] ?: emptyList()
            val sleepsForDay = sleepByDate[currentDate] ?: emptyList()
            
            val restingHr = getRestingHeartRate(dataForDay, sleepsForDay) ?: NO_DATA_VALUE
            
            if (restingHr != NO_DATA_VALUE && restingHr > 0) {
                dailyRestingHrs.add(restingHr)
            }
            
            currentDate = currentDate.plusDays(1)
        }
        
        return dailyRestingHrs
    }

    suspend fun createChartSeries(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        trendDataList: List<TrendData>,
        chartColor: Int
    ): List<ChartData.Series> {
        val sleepList = loadSleepData(from, to)
        val strategy = seriesStrategies[chartGranularity]
            ?: throw IllegalArgumentException("Unsupported granularity: $chartGranularity")
        
        return listOf(strategy.createSeries(from, to, trendDataList, sleepList, chartColor))
    }
} 
