package com.stt.android.chart.impl.data

import android.content.Context
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.core.R as CR
import com.stt.android.R as BaseR
import dagger.hilt.android.qualifiers.ApplicationContext
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartStyle
import com.stt.android.chart.impl.R
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.HeartRateStatItem
import com.stt.android.chart.impl.model.highlightTitleRes
import com.stt.android.chart.impl.screen.HeartRateStatsViewData
import com.stt.android.chart.impl.usecases.FormatChartHighlightDateTimeUseCase
import com.stt.android.domain.trenddata.TrendData
import com.stt.android.domain.trenddata.TrendDataRepository
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import java.time.LocalDate
import java.time.ZoneId
import javax.inject.Inject
import kotlin.math.roundToInt

internal class HeartRateDataLoader @Inject constructor(
    @ApplicationContext appContext: Context,
    private val trendDataRepository: TrendDataRepository,
    private val minimumHeartRateDataLoader: MinimumHeartRateDataLoader,
    private val restingHeartRateDataLoader: RestingHeartRateDataLoader,
    private val sleepMinimumHeartRateDataLoader: SleepMinimumHeartRateDataLoader,
    private val avgHeartRateDataLoader: AvgHeartRateDataLoader,
    private val formatDateTimeUseCase: FormatChartHighlightDateTimeUseCase,
) : BaseHeartRateDataLoader(appContext) {

    companion object {
        const val AVG_HR_ID = "avg_hr"
        const val MIN_SLEEP_HR_ID = "min_sleep_hr"
        const val MIN_DAYTIME_HR_ID = "min_daytime_hr"
        const val RESTING_HR_ID = "resting_hr"
    }

    private fun formatChartHighlightData(
        entry: ChartData.Entry?,
        candlestickEntry: ChartData.CandlestickEntry?
    ): String {
        return when {
            candlestickEntry != null -> {
                val minBpm = candlestickEntry.open.toFloat()
                val maxBpm = candlestickEntry.close.toFloat()

                if (minBpm == maxBpm) {
                    formatHighlightData(minBpm)
                } else {
                    "${minBpm.roundToInt()}-${maxBpm.roundToInt()} ${appContext.getString(CR.string.bpm)}"
                }
            }
            entry != null -> {
                formatHighlightData(entry.y.toFloat())
            }
            else -> {
                appContext.getString(BaseR.string.widget_no_data_title)
            }
        }
    }

    fun loadChartData(
        chartStyle: ChartStyle,
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        selectedHeartRateStatId: String?
    ): Flow<ChartData> {
        val fromMillis = from.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
        val toMillis = to.plusDays(1).atStartOfDay(ZoneId.systemDefault()).minusNanos(1).toInstant().toEpochMilli()

        return trendDataRepository.fetchTrendDataForDateRange(
            fromTimestamp = fromMillis,
            toTimestamp = toMillis,
            aggregated = false
        ).map { trendDataList ->
            val heartRateStat = loadDailHeartRateStat(
                from = from,
                to = to,
                selectedHeartRateStatId = selectedHeartRateStatId,
                chartGranularity = chartGranularity
            )

            val chartColor = avgHeartRateDataLoader.getChartColor(selectedHeartRateStatId)

            val avgSeriesList = avgHeartRateDataLoader.createChartSeries(
                chartGranularity = chartGranularity,
                from = from,
                to = to,
                trendDataList = trendDataList,
                chartColor = chartColor,
                heartRateStat = heartRateStat
            )
            val mainSeries = avgSeriesList.firstOrNull()

            if (mainSeries == null) {
                ChartData(
                    chartGranularity = chartGranularity,
                    series = persistentListOf(),
                    highlightEnabled = chartStyle == ChartStyle.SINGLE,
                    goal = null,
                    highlightDecorationLines = persistentMapOf(),
                    currentValues = persistentListOf(),
                    chartContent = ChartContent.HEART_RATE,
                    colorIndicator = null,
                    selectEntryX = null,
                )
            } else {
                val seriesList = if (chartGranularity == ChartGranularity.DAILY) {
                    avgSeriesList.toImmutableList()
                } else {
                    createCombineSeries(
                        series = mainSeries,
                        chartGranularity = chartGranularity,
                        selectedHeartRateStatId = selectedHeartRateStatId,
                        from = from,
                        to = to,
                        trendDataList = trendDataList,
                    )
                }

                ChartData(
                    chartGranularity = chartGranularity,
                    series = seriesList,
                    highlightEnabled = chartStyle == ChartStyle.SINGLE,
                    goal = null,
                    highlightDecorationLines = heartRateStat?.let { stat -> 
                        val highlightColor = appContext.getColor(BaseR.color.dashboard_widget_minimum_heart_rate)
                        persistentMapOf(stat to highlightColor)
                    } ?: persistentMapOf(),
                    currentValues = persistentListOf(),
                    chartContent = ChartContent.HEART_RATE,
                    colorIndicator = null,
                )
            }
        }.catch {
            emit(ChartData(
                chartGranularity = chartGranularity,
                series = persistentListOf(),
                highlightEnabled = false,
                goal = null,
                highlightDecorationLines = persistentMapOf(),
                currentValues = persistentListOf(),
                chartContent = ChartContent.HEART_RATE,
                colorIndicator = null,
            ))
        }
    }

    private suspend fun createCombineSeries(
        series: ChartData.Series,
        chartGranularity: ChartGranularity,
        selectedHeartRateStatId: String?,
        from: LocalDate,
        to: LocalDate,
        trendDataList: List<TrendData>,
    ): ImmutableList<ChartData.Series> {
        if (selectedHeartRateStatId == null) {
            return persistentListOf(series)
        }
        
        val customSeries = createStatSeries(
            selectedHeartRateStatId = selectedHeartRateStatId,
            chartGranularity = chartGranularity,
            from = from,
            to = to,
            trendDataList = trendDataList,
            chartColor = getChartColor()
        )
        
        if (customSeries == null) {
            return persistentListOf(series)
        }
        
        return persistentListOf(series, customSeries)
    }

    private suspend fun createStatSeries(
        selectedHeartRateStatId: String,
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        trendDataList: List<TrendData>,
        chartColor: Int
    ): ChartData.Series? {
        return when (selectedHeartRateStatId) {
            AVG_HR_ID -> {
                avgHeartRateDataLoader.createChartSeries(
                    chartGranularity = chartGranularity,
                    from = from,
                    to = to,
                    trendDataList = trendDataList,
                    chartColor = chartColor,
                    isLineChart = true
                ).firstOrNull()
            }
            MIN_DAYTIME_HR_ID -> {
                minimumHeartRateDataLoader.createChartSeries(chartGranularity, from, to, trendDataList, chartColor).firstOrNull()
            }
            RESTING_HR_ID -> {
                restingHeartRateDataLoader.createChartSeries(chartGranularity, from, to, trendDataList, chartColor).firstOrNull()
            }
            MIN_SLEEP_HR_ID -> {
                sleepMinimumHeartRateDataLoader.createChartSeries(chartGranularity, from, to, trendDataList, chartColor).firstOrNull()
            }
            else -> null
        }
    }
    
    private fun formatHeartRateRangeString(min: Int, max: Int): String {
        return when {
            min == NO_DATA_VALUE || max == NO_DATA_VALUE -> appContext.getString(BaseR.string.widget_no_data_title)
            min == max -> "$min ${appContext.getString(CR.string.bpm)}"
            else -> "$min-$max ${appContext.getString(CR.string.bpm)}"
        }
    }

    suspend fun loadHeartRateStatsViewData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        selectedHeartRateStatId: String? = null
    ): HeartRateStatsViewData {
        return when (chartGranularity) {
            ChartGranularity.DAILY -> createDailyHeartRateStats(from, to, selectedHeartRateStatId)
            ChartGranularity.WEEKLY, 
            ChartGranularity.MONTHLY,
            ChartGranularity.SEVEN_DAYS,
            ChartGranularity.SIX_MONTHS,
            ChartGranularity.THIRTY_DAYS,
            ChartGranularity.SIX_WEEKS,
            ChartGranularity.EIGHT_YEARS,
            ChartGranularity.YEARLY -> createRangeBasedHeartRateStats(from, to, selectedHeartRateStatId)
            ChartGranularity.SIXTY_DAYS,
            ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
            ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS -> throw IllegalArgumentException("Unsupported granularity: $chartGranularity")
        }
    }

    private suspend fun loadDailHeartRateStat(
        from: LocalDate,
        to: LocalDate,
        selectedHeartRateStatId: String?,
        chartGranularity: ChartGranularity,
    ):Number?{
        if (chartGranularity != ChartGranularity.DAILY){
            return null
        }
        val value = when (selectedHeartRateStatId) {
            AVG_HR_ID -> avgHeartRateDataLoader.getAverageHeartRate(from, to)
            MIN_SLEEP_HR_ID -> sleepMinimumHeartRateDataLoader.getMinimumSleepHeartRate(from, to)
            MIN_DAYTIME_HR_ID -> minimumHeartRateDataLoader.getMinimumDaytimeHeartRate(from, to)
            RESTING_HR_ID -> restingHeartRateDataLoader.getRestingHeartRate(from, to)
            else -> return null
        }
        return if (value == NO_DATA_VALUE) null else value
    }

    private suspend fun createDailyHeartRateStats(
        from: LocalDate,
        to: LocalDate,
        selectedHeartRateStatId: String?
    ): HeartRateStatsViewData {
        val avgHeartRate = avgHeartRateDataLoader.getAverageHeartRate(from, to)
        val minSleepHr = sleepMinimumHeartRateDataLoader.getMinimumSleepHeartRate(from, to)
        val minDaytimeHr = minimumHeartRateDataLoader.getMinimumDaytimeHeartRate(from, to)
//        val restingHr = restingHeartRateDataLoader.getRestingHeartRate(from, to)
        
        val stats = persistentListOf(
//            HeartRateStatItem(
//                value = formatHeartRateValue(restingHr),
//                label = appContext.getString(R.string.heart_rate_stat_resting),
//                id = RESTING_HR_ID
//            ),
            HeartRateStatItem(
                value = formatHeartRateValue(avgHeartRate),
                label = appContext.getString(R.string.heart_rate_stat_average),
                id = AVG_HR_ID
            ),
            HeartRateStatItem(
                value = formatHeartRateValue(minSleepHr),
                label = appContext.getString(R.string.heart_rate_stat_min_sleep),
                id = MIN_SLEEP_HR_ID
            ),
            HeartRateStatItem(
                value = formatHeartRateValue(minDaytimeHr),
                label = appContext.getString(R.string.heart_rate_stat_min_daytime),
                id = MIN_DAYTIME_HR_ID
            )
        )

        return HeartRateStatsViewData.Loaded(
            stats = stats,
            selectedStatId = selectedHeartRateStatId
        )
    }

    private suspend fun createRangeBasedHeartRateStats(
        from: LocalDate,
        to: LocalDate,
        selectedHeartRateStatId: String?
    ): HeartRateStatsViewData {
        val avgHeartRateRange = avgHeartRateDataLoader.getAverageHeartRateRange(from, to)
        val minSleepHeartRateRange = sleepMinimumHeartRateDataLoader.getMinimumSleepHeartRateRange(from, to)
        val minDaytimeHeartRateRange = minimumHeartRateDataLoader.getMinimumDaytimeHeartRateRange(from, to)
//        val restingHeartRateRange = restingHeartRateDataLoader.getRestingHeartRateRange(from, to)
        
        val stats = persistentListOf(
//            HeartRateStatItem(
//                value = formatHeartRateRangeString(
//                    restingHeartRateRange.first,
//                    restingHeartRateRange.second
//                ),
//                label = appContext.getString(R.string.heart_rate_stat_resting),
//                id = RESTING_HR_ID
//            ),
            HeartRateStatItem(
                value = formatHeartRateRangeString(
                    avgHeartRateRange.first,
                    avgHeartRateRange.second
                ),
                label = appContext.getString(R.string.heart_rate_stat_average),
                id = AVG_HR_ID
            ),
            HeartRateStatItem(
                value = formatHeartRateRangeString(
                    minSleepHeartRateRange.first,
                    minSleepHeartRateRange.second
                ),
                label = appContext.getString(R.string.heart_rate_stat_min_sleep),
                id = MIN_SLEEP_HR_ID
            ),
            HeartRateStatItem(
                value = formatHeartRateRangeString(
                    minDaytimeHeartRateRange.first,
                    minDaytimeHeartRateRange.second
                ),
                label = appContext.getString(R.string.heart_rate_stat_min_daytime),
                id = MIN_DAYTIME_HR_ID
            )
        )

        return HeartRateStatsViewData.Loaded(
            stats = stats,
            selectedStatId = selectedHeartRateStatId
        )
    }


    private fun formatHeartRateValue(value: Int): String {
        return if (value == NO_DATA_VALUE) {
            appContext.getString(BaseR.string.widget_no_data_title)
        } else {
            "$value ${appContext.getString(CR.string.bpm)}"
        }
    }

    private fun formatHighlightStatValue(chartData: ChartData): String {
        val highlightValue = chartData.highlightDecorationLines.keys.firstOrNull()
            ?: return appContext.getString(BaseR.string.widget_no_data_title)
        if (highlightValue == NO_DATA_VALUE){
            return appContext.getString(BaseR.string.widget_no_data_title)
        }
        return "${highlightValue.toInt()} ${appContext.getString(CR.string.bpm)}"
    }

    private fun getHeartRateStatTitleRes(selectedHeartRateStatId: String): Int {
        return when (selectedHeartRateStatId) {
            AVG_HR_ID -> R.string.heart_rate_stat_average
            MIN_SLEEP_HR_ID -> R.string.heart_rate_stat_min_sleep
            MIN_DAYTIME_HR_ID -> R.string.heart_rate_stat_min_daytime
            RESTING_HR_ID -> R.string.heart_rate_stat_resting
            else -> R.string.heart_rate_stat_average
        }
    }
    
    fun formatHeartRateHighlightData(
        chartData: ChartData,
        selectedHeartRateStatId: String?,
        entryX: Long,
    ): Triple<String, Int, String> {
        val primaryDateTime = formatDateTimeUseCase.formatDateTime(
            chartGranularity = chartData.chartGranularity,
            x = entryX,
        )
        if (selectedHeartRateStatId == null) {
            val primaryEntry = chartData.series
                .asSequence()
                .mapNotNull { series ->
                    series.entries.firstOrNull { it.x == entryX }
                }
                .firstOrNull()
                
            val candlestickEntry = chartData.series
                .firstOrNull()
                ?.candlestickEntries
                ?.firstOrNull { it.x == entryX }
                
            val value = formatChartHighlightData(primaryEntry, candlestickEntry)
            val valueType = ChartContent.HEART_RATE.highlightTitleRes(chartGranularity = chartData.chartGranularity)
            
            return Triple(value, valueType, primaryDateTime)
        } 
        
        return when (chartData.chartGranularity) {
            ChartGranularity.DAILY -> {
                val value = formatHighlightStatValue(chartData)
                val valueType = getHeartRateStatTitleRes(selectedHeartRateStatId)
                val dateTimeStr = formatDateTimeUseCase.formatDateTimeWithoutTime(chartData.chartGranularity, entryX)
                
                Triple(value, valueType, dateTimeStr)
            }
            ChartGranularity.WEEKLY,
            ChartGranularity.MONTHLY,
            ChartGranularity.YEARLY,
            ChartGranularity.SEVEN_DAYS,
            ChartGranularity.SIX_MONTHS,
            ChartGranularity.SIXTY_DAYS,
            ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
            ChartGranularity.THIRTY_DAYS,
            ChartGranularity.SIX_WEEKS,
            ChartGranularity.EIGHT_YEARS,
            ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS -> {
                val primaryEntry = chartData.series
                    .getOrNull(1)
                    ?.entries
                    ?.firstOrNull { it.x == entryX }
                
                val value = if (primaryEntry != null) {
                    "${primaryEntry.y.toInt()} ${appContext.getString(CR.string.bpm)}"
                } else {
                    appContext.getString(BaseR.string.widget_no_data_title)
                }
                
                val valueType = getHeartRateStatTitleRes(selectedHeartRateStatId)
                
                Triple(value, valueType, primaryDateTime)
            }


        }
    }

}
