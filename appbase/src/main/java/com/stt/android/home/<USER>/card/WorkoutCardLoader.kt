package com.stt.android.home.dashboard.card

import android.annotation.SuppressLint
import android.content.Context
import android.content.SharedPreferences
import android.content.res.Resources
import android.net.Uri
import androidx.core.content.edit
import com.google.maps.android.PolyUtil
import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.DiveExtensionDataModel
import com.stt.android.controllers.PicturesController
import com.stt.android.controllers.ReactionModel
import com.stt.android.controllers.SlopeSkiDataModel
import com.stt.android.controllers.SummaryExtensionDataModel
import com.stt.android.controllers.VideoModel
import com.stt.android.controllers.WeatherExtensionDataModel
import com.stt.android.controllers.WorkoutCommentController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.controllers.currentUserWorkoutUpdated
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.achievements.Achievement
import com.stt.android.domain.achievements.GetAchievementUseCase
import com.stt.android.domain.mapbox.FetchLocationNameUseCase
import com.stt.android.domain.review.isUnknownOrPassed
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.user.ReactionSummary
import com.stt.android.domain.user.User
import com.stt.android.domain.user.VideoInformation
import com.stt.android.domain.user.followees.ListFolloweesUseCase
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.WeatherExtension
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.domain.workouts.isDiving
import com.stt.android.domain.workouts.isPrivate
import com.stt.android.extensions.getWindSpeedText
import com.stt.android.extensions.weatherDrawableRes
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.newfeed.WorkoutCardInfo
import com.stt.android.remote.UserAgent
import com.stt.android.ui.components.workout.WorkoutCardViewData
import com.stt.android.ui.components.workout.WorkoutCardViewModel
import com.stt.android.ui.extensions.defaultMapType
import com.stt.android.ui.extensions.getSummaryData
import com.stt.android.ui.extensions.supportWorkoutAnalysisOnMap
import com.stt.android.utils.STTConstants
import com.stt.android.utils.firstOfType
import com.stt.android.viewmodel.sort
import com.stt.android.workoutdetail.comments.WorkoutComment
import com.stt.android.workoutdetail.tags.TagsData
import com.stt.android.workouts.details.values.WorkoutValue
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.merge
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

class WorkoutCardLoader @Inject constructor(
    @ApplicationContext private val context: Context,
    @UserAgent private val userAgent: String,
    private val currentUserController: CurrentUserController,
    private val workoutHeaderController: WorkoutHeaderController,
    private val workoutCommentController: WorkoutCommentController,
    private val reactionModel: ReactionModel,
    private val getAchievementUseCase: GetAchievementUseCase,
    private val picturesController: PicturesController,
    private val videoModel: VideoModel,
    private val summaryExtensionDataModel: SummaryExtensionDataModel,
    private val slopeSkiDataModel: SlopeSkiDataModel,
    private val diveExtensionDataModel: DiveExtensionDataModel,
    private val weatherExtensionDataModel: WeatherExtensionDataModel,
    private val fetchLocationNameUseCase: FetchLocationNameUseCase,
    private val listFolloweesUseCase: ListFolloweesUseCase,
    private val sharedPreferences: SharedPreferences,
    private val isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase,
    private val infoModelFormatter: InfoModelFormatter,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) {
    data class NewWorkouts(val hasNewOwnWorkout: Boolean, val hasNewFolloweeWorkout: Boolean)

    private var latestOwnWorkoutTimestamp = -1L
    private var latestFolloweeWorkoutTimestamp = -1L

    suspend fun hasNewWorkouts(): NewWorkouts = withContext(coroutinesDispatchers.io) {
        latestOwnWorkoutTimestamp = workoutHeaderController
            .getLatestWorkoutStopTime(listOf(currentUserController.username))
        val lastShownOwnWorkoutTimestamp = sharedPreferences.getLong(
            STTConstants.DefaultPreferences.KEY_DASHBOARD_LAST_SHOW_WORKOUT_ID_ME,
            0L
        )
        val hasNewOwnWorkout = latestOwnWorkoutTimestamp > lastShownOwnWorkoutTimestamp


        latestFolloweeWorkoutTimestamp = workoutHeaderController
            .getLatestWorkoutStopTime(listFolloweesUseCase.listFolloweeUsernames())
        val lastShownFolloweeWorkoutTimestamp = sharedPreferences.getLong(
            STTConstants.DefaultPreferences.KEY_DASHBOARD_LAST_SHOW_WORKOUT_ID_FOLLOWING,
            0L
        )
        val hasNewFolloweeWorkout =
            latestFolloweeWorkoutTimestamp > lastShownFolloweeWorkoutTimestamp

        NewWorkouts(
            hasNewOwnWorkout = hasNewOwnWorkout,
            hasNewFolloweeWorkout = hasNewFolloweeWorkout,
        )
    }

    fun markOwnNewWorkoutAsShown() {
        if (latestOwnWorkoutTimestamp > 0L) {
            sharedPreferences.edit {
                putLong(
                    STTConstants.DefaultPreferences.KEY_DASHBOARD_LAST_SHOW_WORKOUT_ID_ME,
                    latestOwnWorkoutTimestamp
                )
            }
        }
    }

    fun markFolloweeNewWorkoutAsShown() {
        if (latestFolloweeWorkoutTimestamp > 0L) {
            sharedPreferences.edit {
                putLong(
                    STTConstants.DefaultPreferences.KEY_DASHBOARD_LAST_SHOW_WORKOUT_ID_FOLLOWING,
                    latestFolloweeWorkoutTimestamp
                )
            }
        }
    }

    suspend fun loadOwnWorkouts(
        limit: Long,
        includeCover: Boolean = true,
    ): List<WorkoutCardInfo> = withContext(coroutinesDispatchers.io) {
        val currentUser = currentUserController.currentUser
        val workoutHeaders = workoutHeaderController
            .getLatestNotDeletedWorkoutHeadersAndMatchingUserTags(currentUser.username, limit)
            .takeUnless { it.isEmpty() }
            ?: return@withContext emptyList()
        buildWorkoutCards(
            userWorkoutPairs = workoutHeaders.map { currentUser to it },
            isOwnWorkout = true,
            includeCover = includeCover,
        )
    }

    fun loadOwnLatestWorkout(): Flow<WorkoutCardInfo?> = merge(
        workoutHeaderController.currentUserWorkoutUpdated,
        currentUserController.currentUserFlow
            .distinctUntilChangedBy { it.realNameOrUsername to it.profileImageUrl },
    ).onStart { emit(Unit) }
        .map {
            loadOwnWorkouts(limit = 1L, includeCover = false)
                .firstOrNull()
                ?.let { loaded ->
                    loaded.copy(
                        workoutCardViewData = loaded.workoutCardViewData
                            .copy(userImageUrl = null), // We do not show user image for latest workout
                    )
                }
        }
        .distinctUntilChanged()

    private suspend fun buildWorkoutCards(
        userWorkoutPairs: List<Pair<User, WorkoutHeader>>,
        isOwnWorkout: Boolean,
        includeCover: Boolean,
    ): List<WorkoutCardInfo> {
        val workoutHeaders = userWorkoutPairs.map { it.second }

        val workoutKeys = workoutHeaders.mapNotNull(WorkoutHeader::key)
        val commentsByWorkoutKeys = workoutCommentController.find(workoutKeys)
        val likesByWorkoutKeys =
            reactionModel.findSummary(workoutKeys, ReactionSummary.REACTION_LIKE)
        val achievementsByWorkoutKeys = getAchievementUseCase(workoutKeys)

        val workoutIds = workoutHeaders.map(WorkoutHeader::id)
        val imagesByWorkoutIds = picturesController.findByWorkoutIds(workoutIds)
        val videosByWorkoutIds = videoModel.findByWorkoutIds(workoutIds)
        val summaryExtensionsByWorkoutIds = summaryExtensionDataModel.findByWorkoutIds(workoutIds)
        val weatherExtensionsByWorkoutIds = weatherExtensionDataModel.findByWorkoutIds(workoutIds)

        val diveWorkoutIds = workoutHeaders.mapNotNull { workoutHeader ->
            workoutHeader.id
                .takeIf {
                    workoutHeader.activityType.isDiving || workoutHeader.activityType.isSnorkelingOrMermaid()
                }
        }
        val diveExtensionsByWorkoutIds = diveExtensionDataModel.findByWorkoutIds(diveWorkoutIds)

        val slopeSkiWorkoutIds = workoutHeaders.mapNotNull { workoutHeader ->
            workoutHeader.id
                .takeIf { workoutHeader.activityType.isSlopeSki }
        }
        val slopeSkiExtensionsByWorkoutIds = slopeSkiDataModel.findByWorkoutIds(slopeSkiWorkoutIds)

        val isSubscribedToPremium = isSubscribedToPremiumUseCase.invoke().first()

        return userWorkoutPairs.map { (user, workoutHeader) ->
            val extensions = listOfNotNull(
                summaryExtensionsByWorkoutIds[workoutHeader.id],
                weatherExtensionsByWorkoutIds[workoutHeader.id],
                diveExtensionsByWorkoutIds[workoutHeader.id],
                slopeSkiExtensionsByWorkoutIds[workoutHeader.id],
            )

            val suuntoTags = workoutHeader.suuntoTags
                .filter { suuntoTag ->
                    isSubscribedToPremium || suuntoTag.editable
                }.sort()
            val userTags = workoutHeader.userTags.sort()
            val tagsData = TagsData(
                deviceTag = null,
                suuntoTags = suuntoTags,
                userTags = userTags,
                isOwnWorkout = isOwnWorkout,
            )

            WorkoutCardInfo.builder()
                .user(user)
                .workoutHeader(workoutHeader)
                .likes(workoutHeader.key?.let(likesByWorkoutKeys::get))
                .workoutCardViewData(
                    createWorkoutCardViewData(
                        workoutHeader = workoutHeader,
                        user = user,
                        images = imagesByWorkoutIds[workoutHeader.id].orEmpty(),
                        videos = videosByWorkoutIds[workoutHeader.id].orEmpty(),
                        extensions = extensions,
                        tagsData = tagsData,
                        isOwnWorkout = isOwnWorkout,
                        isSubscribedToPremium = isSubscribedToPremium,
                        achievement = achievementsByWorkoutKeys[workoutHeader.key],
                        reactionSummary = workoutHeader.key?.let(likesByWorkoutKeys::get),
                        comments = commentsByWorkoutKeys[workoutHeader.key].orEmpty(),
                        includeCover = includeCover,
                    )
                )
                .build()
        }
    }

    @SuppressLint("InlinedApi")
    private suspend fun createWorkoutCardViewData(
        workoutHeader: WorkoutHeader,
        user: User,
        images: List<ImageInformation>,
        videos: List<VideoInformation>,
        extensions: List<WorkoutExtension>,
        tagsData: TagsData,
        isOwnWorkout: Boolean,
        isSubscribedToPremium: Boolean,
        achievement: Achievement?,
        reactionSummary: ReactionSummary?,
        comments: List<WorkoutComment>,
        includeCover: Boolean,
    ): WorkoutCardViewData {
        val coverInfo = if (includeCover) {
            buildList {
                videos.forEach { videoInfo ->
                    if (videoInfo.reviewState.isUnknownOrPassed()) {
                        videoInfo.url
                            ?.let(Uri::parse)
                            ?.let { uri ->
                                WorkoutCardViewData.CoverInfo.Video(
                                    uri = uri,
                                    userAgent = userAgent,
                                )
                            }
                            ?.let(::add)
                    }
                }

                images.forEach { imageInfo ->
                    if (imageInfo.reviewState.isUnknownOrPassed()) {
                        imageInfo.getFeedUri(context)
                            .let(WorkoutCardViewData.CoverInfo::Image)
                            .let(::add)
                    }
                }

                workoutHeader.polyline
                    ?.takeUnless(String::isEmpty)
                    ?.let { polyline ->
                        WorkoutCardViewData.CoverInfo.Map(
                            mapType = workoutHeader.defaultMapType,
                            polyline = polyline,
                            disableZoomToBounds = workoutHeader.isDiving,
                        )
                    }
                    ?.let(::add)
            }
        } else {
            emptyList()
        }
        val route = workoutHeader.polyline
            ?.takeIf(String::isNotBlank)
            ?.let(PolyUtil::decode)
            ?: emptyList()
        val weather = extensions.firstOfType<WeatherExtension>()
            ?.toDomainEntity()
        val workoutSummaryData = workoutHeader.getSummaryData(
            extensions = extensions,
            infoModelFormatter = infoModelFormatter,
        )
        return WorkoutCardViewData(
            coverInfo = coverInfo,
            locationName = loadLocationName(workoutHeader).orEmpty(),
            weatherDrawableRes = weather?.weatherDrawableRes ?: Resources.ID_NULL,
            temperature = weather?.temperature
                ?.let { temperature ->
                    infoModelFormatter.formatValue(SummaryItem.AVGTEMPERATURE, temperature)
                        .toValueString(context)
                }.orEmpty(),
            windDirection = weather?.windDirection,
            windSpeed = weather.getWindSpeedText(infoModelFormatter).orEmpty(),
            showAddPhotoButton = isOwnWorkout &&
                coverInfo.none { cover ->
                    cover is WorkoutCardViewData.CoverInfo.Image ||
                        cover is WorkoutCardViewData.CoverInfo.Video
                },
            showPlayButton = workoutHeader.supportWorkoutAnalysisOnMap(route),
            title = context.getString(workoutHeader.activityType.localizedStringId),
            subtitle = WorkoutCardViewModel.formatSubtitle(
                context = context,
                workoutHeader = workoutHeader,
                user = user,
                showDate = true,
                showUser = true,
            ),
            username = workoutHeader.username,
            userImageUrl = user.profileImageUrl.orEmpty(),
            workoutValues = listOfNotNull(
                workoutSummaryData.left?.toWorkoutCardValue(context),
                workoutSummaryData.middle?.toWorkoutCardValue(context),
                workoutSummaryData.right?.toWorkoutCardValue(context),
            ),
            description = workoutHeader.description.orEmpty(),
            maxDescriptionLines = 3,
            achievementIcons = buildList {
                repeat(achievement?.personalBestAchievements?.size ?: 0) {
                    add(R.drawable.achievement_trophy_icon)
                }
                repeat(achievement?.cumulativeAchievements?.size ?: 0) {
                    add(R.drawable.achievement_star_icon)
                }
            },
            tags = tagsData,
            isSubscribedToPremium = isSubscribedToPremium,
            showReactions = true,
            commentCount = workoutHeader.commentCount,
            reactionCount = workoutHeader.reactionCount,
            reactedByCurrentUser = reactionSummary?.isUserReacted ?: false,
            showShareAndEdit = isOwnWorkout,
            commentsToShow = comments.sortedBy(WorkoutComment::getTimestamp)
                .takeLast(2)
                .map { workoutComment ->
                    WorkoutCardViewData.Comment(
                        username = workoutComment.username,
                        userRealName = workoutComment.realNameOrUsername,
                        userImageUrl = workoutComment.profilePictureUrl.orEmpty(),
                        comment = workoutComment.message,
                    )
                },
            activityTypeId = workoutHeader.activityTypeId,
            isPrivate = workoutHeader.isPrivate,
        )
    }

    private suspend fun loadLocationName(
        workoutHeader: WorkoutHeader,
    ): String? = runSuspendCatching {
        workoutHeader.startPosition
            ?.let { startPosition ->
                fetchLocationNameUseCase(
                    FetchLocationNameUseCase.Params(
                        latitude = startPosition.latitude,
                        longitude = startPosition.longitude,
                        useCoarseAccuracy = true,
                        useCacheOnly = false,
                    )
                )?.city
            }
    }.getOrElse { e ->
        Timber.w(e, "Failed to load location name")
        null
    }

    suspend fun loadFolloweeWorkouts(
        limit: Long,
    ): List<WorkoutCardInfo> = withContext(coroutinesDispatchers.io) {
        val followees = listFolloweesUseCase.listFollowees()
            .takeUnless { it.isEmpty() }
            ?: return@withContext emptyList()
        val workoutHeaders = workoutHeaderController.findAllWhereOwner(followees, limit)
            .takeUnless { it.isEmpty() }
            ?: return@withContext emptyList()
        val followeesByUsername = followees.associateBy(User::username)

        val userWorkoutPairs = workoutHeaders.mapNotNull { workoutHeader ->
            followeesByUsername[workoutHeader.username]?.let { followee -> followee to workoutHeader }
        }
        buildWorkoutCards(
            userWorkoutPairs = userWorkoutPairs,
            isOwnWorkout = false,
            includeCover = true,
        )
    }

    private companion object {
        private fun WorkoutValue.toWorkoutCardValue(
            context: Context,
        ): WorkoutCardViewData.WorkoutValue = WorkoutCardViewData.WorkoutValue(
            label = label,
            value = value.orEmpty(),
            unit = getUnitLabel(context),
        )

        fun WorkoutValue.toValueString(context: Context): String =
            "$value ${getUnitLabel(context)}"
    }
}
