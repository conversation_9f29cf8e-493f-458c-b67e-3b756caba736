package com.stt.android.home.dashboard.card

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Resources
import android.location.Location
import android.net.Uri
import android.text.format.DateUtils
import com.google.android.gms.location.LocationServices
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.core.utils.TimeProvider
import com.stt.android.domain.review.ReviewState
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.user.User
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.DomainWorkout
import com.stt.android.domain.workouts.FetchPublicWorkoutsUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.WeatherExtension
import com.stt.android.domain.workouts.isDiving
import com.stt.android.domain.workouts.isPrivate
import com.stt.android.extensions.getWindSpeedText
import com.stt.android.extensions.weatherDrawableRes
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.newfeed.ExploreCardData
import com.stt.android.newfeed.WorkoutCardInfo
import com.stt.android.remote.UserAgent
import com.stt.android.ui.components.workout.WorkoutCardViewData
import com.stt.android.ui.components.workout.WorkoutCardViewModel
import com.stt.android.ui.extensions.defaultMapType
import com.stt.android.ui.extensions.getSummaryData
import com.stt.android.utils.CoordinateUtils
import com.stt.android.utils.PermissionUtils
import com.stt.android.workouts.details.values.WorkoutValue
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import pub.devrel.easypermissions.EasyPermissions
import javax.inject.Inject

class ExploreCardLoader @Inject constructor(
    @ApplicationContext private val context: Context,
    @UserAgent private val userAgent: String,
    private val fetchPublicWorkoutsUseCase: FetchPublicWorkoutsUseCase,
    private val timeProvider: TimeProvider,
    private val infoModelFormatter: InfoModelFormatter,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) {
    private val mutex: Mutex = Mutex()

    private var lastExploredResult: ExploreCardData? = null
    private var lastExploredTimestamp = 0L
    private var lastExploredLocation: Location? = null

    suspend fun loadExploreCard(localCacheOnly: Boolean): ExploreCardData? = withContext(coroutinesDispatchers.io) {
        // This function could be called multiple times in a short period of time, and we want to
        // make sure it touches backend only once.
        mutex.withLock {
            loadExploreCardInternal(localCacheOnly)
        }
    }

    private suspend fun loadExploreCardInternal(localCacheOnly: Boolean): ExploreCardData? {
        val now = timeProvider.currentTimeMillis()
        val lastLocation = lastExploredLocation
        val currentLocation = lastLocation?.takeIf {
            now - it.time < EXPLORE_LOCATION_TTL
        } ?: getLastLocation()
        ?: return null

        if (lastExploredResult != null &&
            now - lastExploredTimestamp < EXPLORE_WORKOUTS_TTL &&
            (lastLocation?.distanceTo(currentLocation) ?: Float.MAX_VALUE) < EXPLORE_LOCATION_DELTA_THRESHOLD
        ) {
            // cached result is still fresh, and user hasn't moved much, use the cached result
            return lastExploredResult
        }

        if (localCacheOnly) {
            return null
        }

        val exploreBounds = currentLocation.toExploreBounds()
        return fetchPublicWorkoutsUseCase(
            lowerlat = exploreBounds.southwest.latitude,
            lowerlng = exploreBounds.southwest.longitude,
            upperlat = exploreBounds.northeast.latitude,
            upperlng = exploreBounds.northeast.longitude,
        ).filter { (user, workout) ->
            val workoutHeader = workout.header
            user.hasProfileImage &&
                workoutHeader.isRecent(now) &&
                workoutHeader.hasEnoughDuration &&
                workoutHeader.isSuitableType &&
                workoutHeader.hasEnoughDistance
        }.takeIf { userWorkouts ->
            userWorkouts.size >= EXPLORED_WORKOUTS_TO_SHOW
        }?.sortedBy { (user, workout) ->
            (workout.pictures?.size ?: 0) * 2 +
                (workout.videos?.size ?: 0) * 2 +
                (workout.reactions?.size ?: 0) * 3 +
                (workout.comments?.count { it.username != user.username } ?: 0) * 5
        }?.map { (user, workout) ->
            WorkoutCardInfo.builder()
                .workoutHeader(workout.header)
                .workoutCardViewData(createWorkoutCardViewData(context, userAgent, user, workout, infoModelFormatter))
                .user(user)
                .build()
        }?.let { workoutCards ->
            ExploreCardData(workoutCards)
        }?.also {
            lastExploredResult = it
            lastExploredTimestamp = now
            lastExploredLocation = currentLocation
        }
    }

    @SuppressLint("MissingPermission")
    private suspend fun getLastLocation(): Location? {
        if (!EasyPermissions.hasPermissions(context, *PermissionUtils.LOCATION_PERMISSIONS)) {
            return null
        }

        return LocationServices.getFusedLocationProviderClient(context)
            .lastLocation
            .await()
    }

    private companion object {
        const val EXPLORE_LOCATION_TTL = 10L * DateUtils.MINUTE_IN_MILLIS
        const val EXPLORE_WORKOUTS_TTL = 1L * DateUtils.HOUR_IN_MILLIS
        const val EXPLORE_LOCATION_DELTA_THRESHOLD = 1000.0F

        const val EXPLORE_AREA_BOUNDS_SIZE = 15000.0
        const val MINIMUM_RECENT_WORKOUTS_TIME_THRESHOLD = 30L * DateUtils.DAY_IN_MILLIS
        const val EXPLORE_DURATION_LOWER_LIMIT = 300.0
        val EXPLORE_DISTANCE_RANGE = 500.0..300000.0
        const val EXPLORED_WORKOUTS_TO_SHOW = 6

        val User.hasProfileImage: Boolean get() = !profileImageUrl.isNullOrBlank()
        val WorkoutHeader.hasEnoughDuration: Boolean get() = totalTime >= EXPLORE_DURATION_LOWER_LIMIT
        val WorkoutHeader.hasEnoughDistance: Boolean
            get() =
                activityType.isIndoor ||
                    activityType == ActivityType.WATER_SPORTS ||
                    activityType == ActivityType.SWIMMING ||
                    totalDistance in EXPLORE_DISTANCE_RANGE
        val WorkoutHeader.isSuitableType: Boolean get() = !activityType.isOther && !activityType.isUnknown

        fun WorkoutHeader.isRecent(now: Long): Boolean =
            startTime >= now - MINIMUM_RECENT_WORKOUTS_TIME_THRESHOLD

        fun Location.toExploreBounds(): LatLngBounds {
            val latitudeDiff =
                CoordinateUtils.distanceToLatitudeDiff(EXPLORE_AREA_BOUNDS_SIZE, latitude)
            val longitudeDiff =
                CoordinateUtils.distanceToLongitudeDiff(EXPLORE_AREA_BOUNDS_SIZE, latitude)
            val southwest = LatLng(latitude - latitudeDiff, longitude - longitudeDiff)
            val northeast = LatLng(latitude + latitudeDiff, longitude + longitudeDiff)
            return LatLngBounds(southwest, northeast)
        }

        @SuppressLint("InlinedApi")
        fun createWorkoutCardViewData(
            context: Context,
            userAgent: String,
            user: User,
            workout: DomainWorkout,
            infoModelFormatter: InfoModelFormatter,
        ): WorkoutCardViewData {
            val weather = (workout.extensions?.get(WeatherExtension::class) as? WeatherExtension)
                ?.toDomainEntity()
            val workoutSummaryData = workout.header.getSummaryData(
                extensions = workout.extensions.orEmpty().values.toList(),
                infoModelFormatter = infoModelFormatter,
            )
            return WorkoutCardViewData(
                coverInfo = buildList {
                    workout.videos
                        ?.forEach { videoInfo ->
                            if (videoInfo.reviewState.isUnknownOrPassed()) {
                                videoInfo.url
                                    ?.let(Uri::parse)
                                    ?.let { uri ->
                                        WorkoutCardViewData.CoverInfo.Video(
                                            uri = uri,
                                            userAgent = userAgent,
                                        )
                                    }
                                    ?.let(::add)
                            }
                        }

                    workout.pictures
                        ?.forEach { picture ->
                            if (picture.reviewState.isUnknownOrPassed()) {
                                ImageInformation.fromPicture(picture)
                                    .getFeedUri(context)
                                    .let(WorkoutCardViewData.CoverInfo::Image)
                                    .let(::add)
                            }
                        }

                    workout.header
                        .polyline
                        ?.takeUnless(String::isEmpty)
                        ?.let { polyline ->
                            WorkoutCardViewData.CoverInfo.Map(
                                mapType = workout.header.defaultMapType,
                                polyline = polyline,
                                disableZoomToBounds = workout.header.isDiving,
                            )
                        }
                        ?.let(::add)
                },
                locationName = "",
                weatherDrawableRes = weather?.weatherDrawableRes ?: Resources.ID_NULL,
                temperature = weather?.temperature
                    ?.let { temperature ->
                        infoModelFormatter.formatValue(SummaryItem.AVGTEMPERATURE, temperature)
                            .toValueString(context)
                    }.orEmpty(),
                windDirection = weather?.windDirection,
                windSpeed = weather.getWindSpeedText(infoModelFormatter).orEmpty(),
                showAddPhotoButton = false,
                showPlayButton = false,
                title = context.getString(workout.header.activityType.localizedStringId),
                subtitle = WorkoutCardViewModel.formatSubtitle(
                    context = context,
                    workoutHeader = workout.header,
                    user = user,
                    showDate = true,
                    showUser = true,
                ),
                username = workout.header.username,
                userImageUrl = user.profileImageUrl.orEmpty(),
                workoutValues = listOfNotNull(
                    workoutSummaryData.left?.toWorkoutCardValue(context),
                    workoutSummaryData.middle?.toWorkoutCardValue(context),
                    workoutSummaryData.right?.toWorkoutCardValue(context),
                ),
                description = "",
                maxDescriptionLines = 0,
                achievementIcons = emptyList(),
                tags = null,
                isSubscribedToPremium = false,
                showReactions = false,
                commentCount = 0,
                reactionCount = 0,
                reactedByCurrentUser = false,
                showShareAndEdit = false,
                commentsToShow = emptyList(),
                activityTypeId = workout.header.activityTypeId,
                isPrivate = workout.header.isPrivate,
            )
        }

        private fun ReviewState?.isUnknownOrPassed(): Boolean =
            this == null || this == ReviewState.PASS

        private fun WorkoutValue.toWorkoutCardValue(
            context: Context,
        ): WorkoutCardViewData.WorkoutValue = WorkoutCardViewData.WorkoutValue(
            label = label,
            value = value.orEmpty(),
            unit = getUnitLabel(context),
        )

        private fun WorkoutValue.toValueString(context: Context): String =
            "$value ${getUnitLabel(context)}"
    }
}
