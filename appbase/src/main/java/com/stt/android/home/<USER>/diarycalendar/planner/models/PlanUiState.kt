package com.stt.android.home.diary.diarycalendar.planner.models

import android.content.Context
import android.os.Parcelable
import androidx.annotation.StringRes
import androidx.core.graphics.toColorInt
import com.soy.algorithms.impact.WorkoutImpact
import com.soy.algorithms.impact.WorkoutImpactType
import com.stt.android.R
import com.stt.android.compose.component.ActivityIconFlavor
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.domain.tags.UserTag
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.intensity.nameRes
import com.stt.android.home.diary.diarycalendar.planner.composables.WeeklyEntries
import com.stt.android.home.diary.diarycalendar.planner.composables.WorkoutEntry
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.TargetMinMax
import com.stt.android.home.diary.diarycalendar.planner.domain.models.PlannedWorkout
import com.stt.android.ui.components.workout.WorkoutCardViewData
import com.stt.android.workoutdetail.tags.TagsData
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import java.time.LocalDate
import com.stt.android.core.R as CR

data class PlanUiState(
    val planId: String,
    val metaPlanId: String?,
    val name: String,
    val description: String?,
    val richInfo: String?,
    val durationInWeeks: Int,
    val startDate: LocalDate,
    val currentWeek: Int,
    val weekUiState: WeekUiState,
    val weekTargetsUiState: WeekTargetsUiState,
    val workoutOverViewUiState: WorkoutOverViewUiState,
    val isMetric: Boolean,
    val isActive: Boolean,
    val answers: List<AnsweredQuestion>,
    val focus: String?,
    val level: String?,
    val sports: List<CoreActivityType>,
    val eventInfoUiState: EventInfoUiState?,
) {
    val isCurrentWeek = currentWeek == weekUiState.weekNumber
}

data class AnsweredQuestion(
    val question: String,
    val answer: String,
)

data class WeeklyPlanUiState(
    val planId: String,
    val name: String,
    val durationInWeeks: Int,
    val startDate: LocalDate,
    val currentWeek: Int,
    val weeks: List<Week>,
    val isMetric: Boolean,
    val isActive: Boolean,
)

data class Week(
    val weekUiState: WeekUiState,
    val weekTargetsUiState: WeekTargetsUiState,
    val workoutOverviewUiState: WorkoutOverViewUiState,
)

data class WeekUiState(
    val weekNumber: Int,
    val isNextEnabled: Boolean,
    val isPreviousEnabled: Boolean,
    val weekRange: ClosedRange<LocalDate>,
)

data class WeekTargetsUiState(
    val tss: WeekTargetUiState,
    val duration: WeekTargetUiState,
    val distance: WeekTargetUiState?,
    val goal: String? = null,
    @StringRes val runningUnit: Int? = null,
    val note: String? = null,
    val compliance: Double? = null,
)

data class WeekTargetUiState(
    val value: Double,
    val target: Double,
    val formattedValue: String,
    val formattedTarget: String,
    @StringRes val unit: Int?,
) {
    val progress: Float = if (target > 0.0) (value / target).toFloat().coerceAtLeast(0f) else 0f
}

data class WorkoutOverViewUiState(
    val workoutSessionUiStates: ImmutableList<WorkoutSessionUiState>,
    val workoutImpacts: Map<WorkoutImpact, Int>,
    val charts: Map<WeeklyChartType, WeeklyEntries>,
    val legends: Map<WeeklyChartType, List<WeeklyLegend>>,
) {
    val plannedWorkouts = workoutSessionUiStates.flatMap { it.plannedWorkouts }
    val completedWorkouts = workoutSessionUiStates.flatMap { it.completedWorkouts }
    val cardioImpactTargets: Map<WorkoutImpact, Int> =
        workoutSessionUiStates
            .flatMap { it.plannedWorkoutUiStates }
            .flatMap { it.workoutTargetsUiState.cardioImpacts }
            .groupingBy { it }
            .eachCount()

    val muscularImpactTargets: Map<WorkoutImpact, Int> =
        workoutSessionUiStates
            .flatMap { it.plannedWorkoutUiStates }
            .flatMap { it.workoutTargetsUiState.muscularImpacts }
            .groupingBy { it }
            .eachCount()
}

data class WorkoutSessionUiState(
    val id: String,
    val date: LocalDate,
    val dateString: String,
    val plannedWorkouts: ImmutableList<PlannedWorkout>,
    val plannedWorkoutUiStates: ImmutableList<PlannedWorkoutUiState>,
    val completedWorkouts: ImmutableList<WorkoutHeader>,
    val plannedWorkoutsTotalDuration: String,
    val completedWorkoutsTotalDuration: String,
)

@Parcelize
data class PlannedWorkoutUiState(
    val id: String,
    val date: String?,
    @StringRes val activityNameRes: Int,
    val activityTypeId: Int,
    val note: String,
    val tss: String,
    val workoutTargetsUiState: WorkoutTargetsUiState
) : Parcelable {
    @IgnoredOnParcel
    val duration: String = workoutTargetsUiState.duration

    @IgnoredOnParcel
    val distance: String? = workoutTargetsUiState.distance

    @IgnoredOnParcel
    @StringRes
    val trainingLoadUnit: Int = workoutTargetsUiState.trainingLoadUnit

    @IgnoredOnParcel
    @StringRes
    val distanceUnit: Int? = workoutTargetsUiState.distanceUnit

    @IgnoredOnParcel
    val tagsRes: List<Int> = workoutTargetsUiState.impacts
        .sortedBy { it.name }
        .mapNotNull { it.nameRes() }
}

fun PlannedWorkoutUiState.toViewData(context: Context): WorkoutCardViewData {
    val distanceUnit = distanceUnit?.run { context.getString(this) } ?: ""
    return WorkoutCardViewData(
        coverInfo = emptyList(),
        locationName = "",
        weatherDrawableRes = R.drawable.ic_weather_broken_clouds_fill,
        temperature = "",
        windDirection = null,
        windSpeed = "",
        showAddPhotoButton = false,
        showPlayButton = false,
        title = context.getString(activityNameRes),
        subtitle = date ?: "",
        username = "",
        userImageUrl = null,
        workoutValues = buildList {
            add(
                WorkoutCardViewData.WorkoutValue(
                    label = context.getString(CR.string.workout_values_headline_duration),
                    value = duration,
                    unit = "",
                )
            )
            if (distance != null) {
                add(
                    WorkoutCardViewData.WorkoutValue(
                        label = context.getString(R.string.distance),
                        value = distance,
                        unit = distanceUnit,
                    )
                )
            }
            add(
                WorkoutCardViewData.WorkoutValue(
                    label = context.getString(trainingLoadUnit),
                    value = tss,
                    unit = context.getString(trainingLoadUnit),
                )
            )
        },
        description = note,
        maxDescriptionLines = Int.MAX_VALUE,
        achievementIcons = emptyList(),
        tags = TagsData(
            deviceTag = null,
            suuntoTags = emptyList(),
            userTags = listOf(
                UserTag(
                    id = null,
                    key = null,
                    name = context.getString(R.string.workout_planner_plan_tag),
                    backgroundColor = R.color.light_grey,
                )
            ) + tagsRes.map { UserTag(id = null, key = null, name = context.getString(it)) },
            isOwnWorkout = false,
        ),
        isSubscribedToPremium = true,
        showReactions = false,
        commentCount = 0,
        reactionCount = 0,
        reactedByCurrentUser = false,
        showShareAndEdit = false,
        commentsToShow = emptyList(),
        activityTypeId = activityTypeId,
        activityIconFlavor = ActivityIconFlavor.FILL_WITH_OPACITY,
        isPrivate = false,
    )
}

@Parcelize
data class WorkoutTargetsUiState(
    val duration: String,
    val distance: String? = null,
    @StringRes val distanceUnit: Int? = null,
    val avgPace: String? = null,
    @StringRes val avgPaceUnit: Int? = null,
    val targetHrZone: String? = null,
    @StringRes val targetHrZoneUnit: Int? = null,
    val targetPaceZone: String? = null,
    @StringRes val targetPaceZoneUnit: Int? = null,
    val trainingLoad: String,
    @StringRes val trainingLoadUnit: Int,
    val intensityZone: Int,
    val impacts: List<WorkoutImpact> = emptyList(),
    val targetPowerZone: String? = null,
    @StringRes val targetPowerZoneUnit: Int? = null,
) : Parcelable {
    @IgnoredOnParcel
    val cardioImpacts: List<WorkoutImpact> = impacts.filter { it.type == WorkoutImpactType.CARDIO }

    @IgnoredOnParcel
    val muscularImpacts: List<WorkoutImpact> =
        impacts.filter { it.type == WorkoutImpactType.MUSCULAR }
}

data class WeeklyLegend(
    val plannedLegend: Legend?,
    val completedLegend: Legend,
)

data class Legend(
    val text: String,
    val color: Int,
)

val fakeWorkout1 = PlannedWorkout(
    id = "WorkoutUiState1",
    activityType = CoreActivityType.TRAIL_RUNNING,
    durationInSeconds = 85,
    estimatedDistanceInMeters = 20,
    intensityZone = 1,
    impacts = listOf(WorkoutImpact.HARD_ANAEROBIC_EFFORT, WorkoutImpact.SPEED_AND_AGILITY),
    name = "Running",
    notes = "Aerobic running session to create base fitness level",
    trainingDate = LocalDate.of(2023, 12, 20),
    trainingStressScore = 80,
    targetPace = TargetMinMax(
        min = 5.***************,
        max = 4.***************,
    ),
    targetHeartRate = TargetMinMax(
        min = 123.0,
        max = 140.0,
    ),
    targetPower = TargetMinMax(
        min = 233.0,
        max = 256.0,
    ),
    avgSpeed = 3.0858333333333334,
)

val fakeWorkout2 = PlannedWorkout(
    id = "WorkoutUiState2",
    activityType = CoreActivityType.GYM,
    durationInSeconds = 85,
    estimatedDistanceInMeters = 20,
    intensityZone = 1,
    impacts = listOf(WorkoutImpact.FLEXIBILITY, WorkoutImpact.LONG_AEROBIC_BASE),
    name = "Gym",
    notes = "Building strength for injury prevention",
    trainingDate = LocalDate.of(2023, 12, 21),
    trainingStressScore = 80,
    targetPace = TargetMinMax(
        min = 5.***************,
        max = 4.***************,
    ),
    targetHeartRate = TargetMinMax(
        min = 123.0,
        max = 140.0,
    ),
    targetPower = TargetMinMax(
        min = 233.0,
        max = 256.0,
    ),
    avgSpeed = 3.0858333333333334,
)

val fakePlannedWorkoutUiState1 = PlannedWorkoutUiState(
    id = "WorkoutUiState1",
    date = "Today 20.12",
    activityNameRes = CoreActivityType.TRAIL_RUNNING.nameRes,
    activityTypeId = CoreActivityType.TRAIL_RUNNING.id,
    note = "Aerobic running session to create base fitness level",
    tss = "80",
    workoutTargetsUiState = WorkoutTargetsUiState(
        duration = "1:25",
        distance = "20",
        distanceUnit = CR.string.TXT_KM,
        trainingLoad = "80",
        trainingLoadUnit = R.string.workout_values_headline_tss,
        intensityZone = 1,
        impacts = listOf(WorkoutImpact.HARD_ANAEROBIC_EFFORT, WorkoutImpact.SPEED_AND_AGILITY),
        targetHrZone = "125-145",
        targetHrZoneUnit = com.stt.android.core.R.string.bpm,
        targetPaceZone = "04'28-05'24",
        targetPaceZoneUnit = com.stt.android.core.R.string.per_km,
        targetPowerZone = "100-150",
        targetPowerZoneUnit = com.stt.android.core.R.string.watt,
    )
)

val fakePlannedWorkoutUiState2 = PlannedWorkoutUiState(
    id = "WorkoutUiState2",
    date = "Tuesday 21.12",
    activityNameRes = CoreActivityType.GYM.nameRes,
    activityTypeId = CoreActivityType.GYM.id,
    note = "Building strength for injury prevention",
    tss = "65",
    workoutTargetsUiState = WorkoutTargetsUiState(
        duration = "1:25",
        trainingLoad = "80",
        trainingLoadUnit = R.string.workout_values_headline_tss,
        intensityZone = 1,
        impacts = listOf(WorkoutImpact.FLEXIBILITY, WorkoutImpact.LONG_AEROBIC_BASE),
    )
)

val fakeWorkoutOverViewUiState = WorkoutOverViewUiState(
    workoutSessionUiStates = persistentListOf(
        WorkoutSessionUiState(
            id = "WorkoutGroupUiState0",
            date = LocalDate.now().minusDays(1),
            dateString = "Sunday 19.12",
            plannedWorkouts = persistentListOf(fakeWorkout1),
            plannedWorkoutUiStates = persistentListOf(fakePlannedWorkoutUiState1),
            completedWorkouts = persistentListOf(),
            plannedWorkoutsTotalDuration = "1:30",
            completedWorkoutsTotalDuration = "1:57'47",
        ),
        WorkoutSessionUiState(
            id = "WorkoutGroupUiState1",
            date = LocalDate.now(),
            dateString = "Monday 20.12",
            plannedWorkouts = persistentListOf(fakeWorkout1),
            plannedWorkoutUiStates = persistentListOf(fakePlannedWorkoutUiState1),
            completedWorkouts = persistentListOf(),
            plannedWorkoutsTotalDuration = "1:30",
            completedWorkoutsTotalDuration = "1:57'47",
        ),
        WorkoutSessionUiState(
            id = "WorkoutGroupUiState2",
            date = LocalDate.now(),
            dateString = "Tuesday 21.12",
            plannedWorkouts = persistentListOf(),
            plannedWorkoutUiStates = persistentListOf(),
            completedWorkouts = persistentListOf(),
            plannedWorkoutsTotalDuration = "1:30",
            completedWorkoutsTotalDuration = "1:57'47",
        ),
        WorkoutSessionUiState(
            id = "WorkoutGroupUiState3",
            date = LocalDate.now(),
            dateString = "Wednesday 22.12",
            plannedWorkouts = persistentListOf(fakeWorkout2),
            plannedWorkoutUiStates = persistentListOf(fakePlannedWorkoutUiState2),
            completedWorkouts = persistentListOf(),
            plannedWorkoutsTotalDuration = "1:30",
            completedWorkoutsTotalDuration = "1:57'47",
        ),
    ),
    workoutImpacts = emptyMap(),
    charts = mapOf(
        WeeklyChartType.TSS to WeeklyEntries(
            range = LocalDate.now().minusDays(1)..LocalDate.now().plusDays(5),
            plannedWorkouts = listOf(
                WorkoutEntry(
                    date = LocalDate.now().minusDays(1),
                    value = 80f,
                    activityType = CoreActivityType.RUNNING
                ),
                WorkoutEntry(
                    date = LocalDate.now().plusDays(1),
                    value = 180f,
                    activityType = CoreActivityType.TRAIL_RUNNING
                ),
                WorkoutEntry(
                    date = LocalDate.now().plusDays(3),
                    value = 120f,
                    activityType = CoreActivityType.WALKING
                ),
            ),
            doneWorkouts = listOf(
                WorkoutEntry(
                    date = LocalDate.now().minusDays(1),
                    value = 70f,
                    activityType = CoreActivityType.RUNNING
                ),
                WorkoutEntry(
                    date = LocalDate.now().plusDays(1),
                    value = 200f,
                    activityType = CoreActivityType.TRAIL_RUNNING
                ),
                WorkoutEntry(
                    date = LocalDate.now().plusDays(3),
                    value = 150f,
                    activityType = CoreActivityType.WALKING
                ),
            )
        )
    ),
    legends = mapOf(
        WeeklyChartType.TSS to listOf(
            WeeklyLegend(
                plannedLegend = Legend(
                    text = "Running 80",
                    color = "#FDD300".toColorInt(),
                ),
                completedLegend = Legend(
                    text = "Running 70",
                    color = "#FDD300".toColorInt(),
                )
            ),
            WeeklyLegend(
                plannedLegend = Legend(
                    text = "Trail running 180",
                    color = "#FDD300".toColorInt(),
                ),
                completedLegend = Legend(
                    text = "Trail running 200",
                    color = "#FDD300".toColorInt(),
                )
            ),
            WeeklyLegend(
                plannedLegend = Legend(
                    text = "Walking 120",
                    color = "#55D781".toColorInt(),
                ),
                completedLegend = Legend(
                    text = "Walking 150",
                    color = "#55D781".toColorInt(),
                )
            ),
        )
    ),
)

val fakePlanUiState = PlanUiState(
    planId = "planId1",
    metaPlanId = "metaProgramId1",
    name = "Fake plan",
    durationInWeeks = 8,
    startDate = LocalDate.now(),
    currentWeek = 1,
    weekUiState = WeekUiState(
        weekNumber = 1,
        isNextEnabled = false,
        isPreviousEnabled = false,
        weekRange = LocalDate.of(2024, 3, 20)..LocalDate.of(2024, 3, 26),
    ),
    weekTargetsUiState = WeekTargetsUiState(
        WeekTargetUiState(
            value = 130.0,
            target = 155.0,
            formattedValue = "130",
            formattedTarget = "155",
            unit = null,
        ),
        duration = WeekTargetUiState(
            value = 3_600.0,
            target = 5_400.0,
            formattedValue = "1:00'00",
            formattedTarget = "1:30'00",
            unit = null,
        ),
        distance = WeekTargetUiState(
            value = 5_000.0,
            target = 10_000.0,
            formattedValue = "5",
            formattedTarget = "10",
            unit = com.stt.android.core.R.string.km,
        ),
        goal = "My dummy goal",
        runningUnit = com.stt.android.core.R.string.km,
        note = "My dummy note",
        compliance = 0.667,
    ),
    workoutOverViewUiState = fakeWorkoutOverViewUiState,
    isMetric = true,
    isActive = true,
    answers = listOf(
        AnsweredQuestion(
            question = "How much are you ready to train in maximum in a week?",
            answer = "10-15 hours"
        ),
        AnsweredQuestion(
            question = "Which days are your preferred rest days?",
            answer = "Monday, Wednesday, Saturday"
        ),
        AnsweredQuestion(
            question = "Tuesday, Sunday",
            answer = "10-15 hours"
        ),
        AnsweredQuestion(
            question = "End date",
            answer = "25.6.2024"
        ),
    ),
    focus = "Race",
    level = "Beginner",
    sports = listOf(CoreActivityType.COMBAT_SPORTS),
    description = "Description",
    richInfo = "Rich info",
    eventInfoUiState = null,
)

val fakeWeeklyPlanUiState = WeeklyPlanUiState(
    planId = "planId1",
    name = "Fake plan",
    durationInWeeks = 8,
    startDate = LocalDate.now(),
    currentWeek = 1,
    weeks = listOf(
        Week(
            weekUiState = WeekUiState(
                weekNumber = 1,
                isNextEnabled = false,
                isPreviousEnabled = false,
                weekRange = LocalDate.of(2024, 3, 20)..LocalDate.of(2024, 3, 26),
            ),
            weekTargetsUiState = WeekTargetsUiState(
                WeekTargetUiState(
                    value = 130.0,
                    target = 155.0,
                    formattedValue = "130",
                    formattedTarget = "155",
                    unit = null,
                ),
                duration = WeekTargetUiState(
                    value = 3_600.0,
                    target = 5_400.0,
                    formattedValue = "1:00'00",
                    formattedTarget = "1:30'00",
                    unit = null,
                ),
                distance = WeekTargetUiState(
                    value = 5_000.0,
                    target = 10_000.0,
                    formattedValue = "5",
                    formattedTarget = "10",
                    unit = com.stt.android.core.R.string.km,
                ),
                goal = "My dummy goal",
                runningUnit = com.stt.android.core.R.string.km,
                note = "My dummy note",
                compliance = 0.667,
            ),
            workoutOverviewUiState = fakeWorkoutOverViewUiState,
        ),
        Week(
            weekUiState = WeekUiState(
                weekNumber = 2,
                isNextEnabled = false,
                isPreviousEnabled = false,
                weekRange = LocalDate.of(2024, 3, 20)..LocalDate.of(2024, 3, 26),
            ),
            weekTargetsUiState = WeekTargetsUiState(
                WeekTargetUiState(
                    value = 130.0,
                    target = 155.0,
                    formattedValue = "130",
                    formattedTarget = "155",
                    unit = null,
                ),
                duration = WeekTargetUiState(
                    value = 3_600.0,
                    target = 5_400.0,
                    formattedValue = "1:00'00",
                    formattedTarget = "1:30'00",
                    unit = null,
                ),
                distance = WeekTargetUiState(
                    value = 5_000.0,
                    target = 10_000.0,
                    formattedValue = "5",
                    formattedTarget = "10",
                    unit = com.stt.android.core.R.string.km,
                ),
                goal = "My dummy goal",
                runningUnit = com.stt.android.core.R.string.km,
                note = "My dummy note",
                compliance = 0.667,
            ),
            workoutOverviewUiState = fakeWorkoutOverViewUiState,
        ),
        Week(
            weekUiState = WeekUiState(
                weekNumber = 3,
                isNextEnabled = false,
                isPreviousEnabled = false,
                weekRange = LocalDate.of(2024, 3, 20)..LocalDate.of(2024, 3, 26),
            ),
            weekTargetsUiState = WeekTargetsUiState(
                WeekTargetUiState(
                    value = 130.0,
                    target = 155.0,
                    formattedValue = "130",
                    formattedTarget = "155",
                    unit = null,
                ),
                duration = WeekTargetUiState(
                    value = 3_600.0,
                    target = 5_400.0,
                    formattedValue = "1:00'00",
                    formattedTarget = "1:30'00",
                    unit = null,
                ),
                distance = WeekTargetUiState(
                    value = 5_000.0,
                    target = 10_000.0,
                    formattedValue = "5",
                    formattedTarget = "10",
                    unit = com.stt.android.core.R.string.km,
                ),
                goal = "My dummy goal",
                runningUnit = com.stt.android.core.R.string.km,
                note = "My dummy note",
                compliance = 0.667,
            ),
            workoutOverviewUiState = fakeWorkoutOverViewUiState,
        ),
        Week(
            weekUiState = WeekUiState(
                weekNumber = 4,
                isNextEnabled = false,
                isPreviousEnabled = false,
                weekRange = LocalDate.of(2024, 3, 20)..LocalDate.of(2024, 3, 26),
            ),
            weekTargetsUiState = WeekTargetsUiState(
                WeekTargetUiState(
                    value = 130.0,
                    target = 155.0,
                    formattedValue = "130",
                    formattedTarget = "155",
                    unit = null,
                ),
                duration = WeekTargetUiState(
                    value = 3_600.0,
                    target = 5_400.0,
                    formattedValue = "1:00'00",
                    formattedTarget = "1:30'00",
                    unit = null,
                ),
                distance = WeekTargetUiState(
                    value = 5_000.0,
                    target = 10_000.0,
                    formattedValue = "5",
                    formattedTarget = "10",
                    unit = com.stt.android.core.R.string.km,
                ),
                goal = "My dummy goal",
                runningUnit = com.stt.android.core.R.string.km,
                note = "My dummy note",
                compliance = 0.667,
            ),
            workoutOverviewUiState = fakeWorkoutOverViewUiState,
        ),
    ),
    isMetric = true,
    isActive = true,
)
