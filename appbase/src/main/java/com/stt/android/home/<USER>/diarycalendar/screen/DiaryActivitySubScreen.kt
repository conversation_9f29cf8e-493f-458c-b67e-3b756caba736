package com.stt.android.home.diary.diarycalendar.screen

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.workouts.ActivityGroupMapper
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainer
import com.stt.android.home.diary.diarycalendar.components.DiaryActivityTitle
import com.stt.android.home.diary.diarycalendar.components.DiaryCalendarActivityTotals
import com.stt.android.home.diary.diarycalendar.components.DiaryCalendarEmptyState

@Composable
internal fun DiaryActivitySubScreen(
    data: DiaryCalendarListContainer,
    activityGroupMapper: ActivityGroupMapper,
    showBackToCurrent: Boolean,
    modifier: Modifier = Modifier,
) {
    LazyColumn(
        modifier = modifier,
        contentPadding = PaddingValues(bottom = if (showBackToCurrent) 112.dp else MaterialTheme.spacing.small),
    ) {
        item(key = "title") {
            DiaryActivityTitle(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(all = MaterialTheme.spacing.medium),
                timeRange = data.timeRange,
                workoutCount = data.workoutCount,
            )
        }

        if (data.activityStatsWithTotals.isEmpty() && data.loadingComplete) {
            item(key = "emptyState") {
                DiaryCalendarEmptyState(modifier = Modifier.fillMaxWidth())
            }
        } else {
            item(key = "diaryCalendarActivityTotals") {
                DiaryCalendarActivityTotals(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            horizontal = MaterialTheme.spacing.medium,
                            vertical = MaterialTheme.spacing.small,
                        ),
                    values = data.totalsForPeriod,
                )
            }

            // Summary by activity type
            val maxDurationForSingleActivity =
                data.activityStatsWithTotals.maxOfOrNull { it.second.duration } ?: 1.0

            val summaryList = data.activityStatsWithTotals
                .groupBy { it.first.id }
                .flatMap { it.value }
            summaryList.forEachIndexed { index, (activityType, totals) ->
                activitySummaryItem(
                    activityGroupMapper,
                    activityType,
                    index,
                    totals,
                    maxDurationForSingleActivity,
                    totals.workoutIds,
                    data.startDate,
                    data.endDate,
                    data.onSportRowClicked
                )
            }
        }
    }
}
