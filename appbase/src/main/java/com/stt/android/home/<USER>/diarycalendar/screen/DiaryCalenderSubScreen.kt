package com.stt.android.home.diary.diarycalendar.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.workouts.ActivityGroupMapper
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainer
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleData
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleEpoxyController
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleYearEpoxyController
import com.stt.android.home.diary.diarycalendar.components.DiaryBubble
import com.stt.android.home.diary.diarycalendar.components.DiaryBubbleYear
import com.stt.android.home.diary.diarycalendar.components.DiaryCalendarActivityTotals
import com.stt.android.home.diary.diarycalendar.components.DiaryCalendarEmptyState
import com.stt.android.home.diary.diarycalendar.components.DiaryCalendarMap
import com.stt.android.home.diary.diarycalendar.components.DiaryCalendarTitle
import com.stt.android.home.diary.diarycalendar.sharesummary.workoutDaysCount
import com.stt.android.home.mytracks.MyTracksUtils
import com.stt.android.models.MapSelectionModel

@Composable
internal fun DiaryCalendarSubScreen(
    data: DiaryCalendarListContainer,
    activityGroupMapper: ActivityGroupMapper,
    diaryBubbleEpoxyController: DiaryBubbleEpoxyController,
    diaryBubbleYearEpoxyController: DiaryBubbleYearEpoxyController,
    mapSelectionModel: MapSelectionModel,
    amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    myTracksUtils: MyTracksUtils,
    showActivities: Boolean,
    showBackToCurrent: Boolean,
    modifier: Modifier = Modifier,
) {
    val listState = rememberLazyListState()

    LazyColumn(
        modifier = modifier,
        state = listState,
        contentPadding = PaddingValues(bottom = if (showBackToCurrent) 112.dp else MaterialTheme.spacing.small),
    ) {
        item(key = "title") {
            DiaryCalendarTitle(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        start = MaterialTheme.spacing.medium,
                        end = MaterialTheme.spacing.medium,
                        bottom = MaterialTheme.spacing.medium,
                    ),
                timeRange = data.timeRange,
                workoutCount = data.workoutCount,
                onShareClicked = {
                    data.onShareSummaryButtonClicked(
                        data.startDate,
                        data.endDate,
                        data.bubbleData.workoutDaysCount,
                        data.activityStatsWithTotals.size,
                    )
                },
            )
        }
        if (data.granularity == DiaryCalendarListContainer.Granularity.YEAR) {
            item(key = "yearBubbles") {
                DiaryBubbleYear(
                    modifier = Modifier.fillMaxWidth(),
                    bubbleData = data.bubbleData,
                    controller = diaryBubbleYearEpoxyController,
                )
            }
        } else {
            item(key = "bubbles") {
                DiaryBubble(
                    modifier = Modifier.fillMaxWidth(),
                    bubbleData = data.bubbleData.firstOrNull() ?: DiaryBubbleData.EMPTY,
                    controller = diaryBubbleEpoxyController,
                )
            }
        }
        item(key = "diaryCalendarActivityTotals") {
            DiaryCalendarActivityTotals(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        horizontal = MaterialTheme.spacing.medium,
                        vertical = MaterialTheme.spacing.small,
                    ),
                values = data.totalsForPeriod,
            )
        }

        // Summary by activity type
        val maxDurationForSingleActivity =
            data.activityStatsWithTotals.maxOfOrNull { it.second.duration } ?: 1.0
        val summaryList = data.activityStatsWithTotals
            .groupBy { it.first.id }
            .flatMap { it.value }
        summaryList.forEachIndexed { index, (activityType, totals) ->
            activitySummaryItem(
                activityGroupMapper,
                activityType,
                index,
                totals,
                maxDurationForSingleActivity,
                totals.workoutIds,
                data.startDate,
                data.endDate,
                data.onSportRowClicked
            )
        }

        if (data.locations.isNotEmpty()) {
            item(key = "map") {
                DiaryCalendarMap(
                    modifier = Modifier
                        .background(MaterialTheme.colorScheme.surface)
                        .fillMaxWidth()
                        .padding(
                            horizontal = MaterialTheme.spacing.medium,
                            vertical = MaterialTheme.spacing.small,
                        )
                        .aspectRatio(1f),
                    locations = data.locations,
                    routes = data.routes,
                    bounds = data.bounds,
                    granularity = data.granularity,
                    mapSelectionModel = mapSelectionModel,
                    bubbleData = data.bubbleData,
                    myTracksUtils = myTracksUtils,
                    tracker = amplitudeAnalyticsTracker,
                    onClick = data.onMapClicked,
                )
            }
        }

        if (data.activityStatsWithTotals.isEmpty() && data.loadingComplete) {
            item(key = "emptyState") {
                DiaryCalendarEmptyState(modifier = Modifier.fillMaxWidth())
            }
        }
    }

    DisposableEffect(Unit) {
        if (showActivities) {
            listState.requestScrollToItem(index = 3)
        }
        onDispose {}
    }
}
