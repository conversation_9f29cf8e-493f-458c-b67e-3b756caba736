package com.stt.android.home.settings;

import android.content.Context;
import android.content.res.TypedArray;
import androidx.annotation.Nullable;
import androidx.preference.ListPreference;
import android.util.AttributeSet;
import com.stt.android.R;
import timber.log.Timber;

public class TitleListPreference extends ListPreference{
    private CharSequence[] entries;
    private CharSequence[] entryValues;

    public TitleListPreference(Context context) {
        this(context, null);
    }

    public TitleListPreference(Context context, AttributeSet attrs) {
        this(context, attrs, android.R.attr.preferenceStyle);
    }

    public TitleListPreference(Context context, AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, defStyleAttr);
    }

    public TitleListPreference(Context context, AttributeSet attrs, int defStyleAttr,
        int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init(context, attrs);
    }

    private void init(Context context, @Nullable AttributeSet attrs) {
        if (attrs != null) {
            TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.TitleSummaryListPreference);
            entries = a.getTextArray(R.styleable.TitleSummaryListPreference_titleSummaryListPreferenceEntries);
            super.setEntries(entries);
            entryValues = a.getTextArray(R.styleable.TitleSummaryListPreference_titleSummaryListPreferenceEntryValues);
            super.setEntryValues(entryValues);
            a.recycle();
        }
    }


    private int getEntryIndexForValue(CharSequence value) {
        for (int i = 0; i < entryValues.length; ++i) {
            if (entryValues[i].equals(value))
                return i;
        }
        Timber.w("Unknown value: %s", value);
        return 0; // If it's not the expected value, avoid crashing.
    }

    @Override
    protected void onSetInitialValue(boolean restoreValue, Object defaultValue) {
        setSummary(entries[getEntryIndexForValue(getPersistedString((String) defaultValue))]);
        super.onSetInitialValue(restoreValue, defaultValue);
    }

    @Override
    protected void onSetInitialValue(Object defaultValue) {
        setSummary(entries[getEntryIndexForValue(getPersistedString((String) defaultValue))]);
        super.onSetInitialValue(defaultValue);
    }

    @Override
    protected boolean persistString(String value) {
        setSummary(entries[getEntryIndexForValue(value)]);
        return super.persistString(value);
    }
}
