package com.stt.android.home.diary.diarycalendar

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.SnackbarResult
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.ViewCompositionStrategy.DisposeOnLifecycleDestroyed
import androidx.fragment.app.Fragment
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.common.ui.observeK
import com.stt.android.common.ui.observeNotNull
import com.stt.android.common.viewstate.ViewState
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.controllers.CurrentUserController
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.ActivityGroupMapper
import com.stt.android.home.WorkoutBroadcastActionListener
import com.stt.android.home.diary.diarycalendar.DiaryCalendarConstants.DIARY_CALENDAR_DISPLAY_MODE_ACTIVITY
import com.stt.android.home.diary.diarycalendar.DiaryCalendarConstants.DIARY_CALENDAR_DISPLAY_MODE_CALENDAR
import com.stt.android.home.diary.diarycalendar.DiaryCalendarConstants.DIARY_CALENDAR_DISPLAY_MODE_MAP
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleEpoxyController
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleYearEpoxyController
import com.stt.android.home.diary.diarycalendar.screen.DiaryActivitySubScreen
import com.stt.android.home.diary.diarycalendar.screen.DiaryCalendarSubScreen
import com.stt.android.home.diary.diarycalendar.screen.DiaryMapSubScreen
import com.stt.android.home.diary.diarycalendar.sharesummary.DiaryCalendarShareSummaryActivity
import com.stt.android.home.diary.diarycalendar.workoutlist.CalendarWorkoutListActivity
import com.stt.android.home.explore.WorkoutMapNavigator
import com.stt.android.home.mytracks.MyTracksUtils
import com.stt.android.models.MapSelectionModel
import timber.log.Timber
import java.time.LocalDate
import javax.inject.Inject
import com.stt.android.R as BR

// Note: derived classes should use @AndroidEntryPoint annotation
abstract class BaseDiaryCalendarFragment<ViewModel : BaseDiaryCalendarViewModel> :
    Fragment(), DiaryCalendarToDayViewNavigation by BrandDiaryCalendarToDayViewNavigation() {

    abstract val viewModel: ViewModel

    @Inject
    lateinit var workoutBroadcastActionListener: WorkoutBroadcastActionListener

    @Inject
    lateinit var currentUserController: CurrentUserController

    @Inject
    lateinit var workoutMapNavigator: WorkoutMapNavigator

    @Inject
    lateinit var activityGroupMapper: ActivityGroupMapper

    @Inject
    lateinit var diaryBubbleEpoxyController: DiaryBubbleEpoxyController

    @Inject
    lateinit var diaryBubbleYearEpoxyController: DiaryBubbleYearEpoxyController

    @Inject
    lateinit var mapSelectionModel: MapSelectionModel

    @Inject
    lateinit var myTracksUtils: MyTracksUtils

    @Inject
    lateinit var amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker

    private val showActivities: Boolean
        get() = arguments?.getBoolean(ARG_SHOW_ACTIVITIES_LIST, false) == true

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ) = ComposeView(inflater.context).apply {
        setViewCompositionStrategy(DisposeOnLifecycleDestroyed(viewLifecycleOwner))

        val pagerNavigation =
            (parentFragment as? DiaryCalendarPagerNavigationOwner)?.pagerNavigation
                ?: DiaryCalendarPagerNavigation.DUMMY

        setContentWithM3Theme {
            val context = LocalContext.current
            val viewState = viewModel.viewState.observeAsState().value
            val snackbarHostState = remember { SnackbarHostState() }

            Scaffold(
                modifier = Modifier.fillMaxSize(),
                snackbarHost = { SnackbarHost(snackbarHostState) },
                containerColor = MaterialTheme.colorScheme.surface,
            ) { padding ->
                val data = viewState?.data
                if (data != null) {
                    val navigationState by pagerNavigation.navigationState.collectAsState()
                    when (data.displayMode) {
                        DIARY_CALENDAR_DISPLAY_MODE_CALENDAR -> DiaryCalendarSubScreen(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(padding),
                            data = data,
                            activityGroupMapper = activityGroupMapper,
                            diaryBubbleEpoxyController = diaryBubbleEpoxyController,
                            diaryBubbleYearEpoxyController = diaryBubbleYearEpoxyController,
                            mapSelectionModel = mapSelectionModel,
                            amplitudeAnalyticsTracker = amplitudeAnalyticsTracker,
                            myTracksUtils = myTracksUtils,
                            showActivities = showActivities,
                            showBackToCurrent = !navigationState.isDefaultItem,
                        )

                        DIARY_CALENDAR_DISPLAY_MODE_ACTIVITY -> DiaryActivitySubScreen(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(padding),
                            data = data,
                            activityGroupMapper = activityGroupMapper,
                            showBackToCurrent = !navigationState.isDefaultItem,
                        )

                        DIARY_CALENDAR_DISPLAY_MODE_MAP -> DiaryMapSubScreen(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(padding),
                            data = data,
                            mapSelectionModel = mapSelectionModel,
                            amplitudeAnalyticsTracker = amplitudeAnalyticsTracker,
                            myTracksUtils = myTracksUtils,
                            previousPageEnabled = navigationState.hasPreviousPage,
                            nextPageEnabled = navigationState.hasNextPage,
                            onPreviousPage = pagerNavigation::previousPage,
                            onNextPage = pagerNavigation::nextPage,
                        )

                        else -> Unit
                    }
                }
            }

            LaunchedEffect(viewState) {
                if (viewState is ViewState.Error) {
                    val event = viewState.errorEvent
                    val actionLabelRes = if (event.canRetry) {
                        BR.string.retry_action
                    } else if (event.showCloseButton) {
                        BR.string.close
                    } else null
                    snackbarHostState.showSnackbar(
                        message = context.getString(event.errorStringRes),
                        actionLabel = actionLabelRes?.let { context.getString(it) },
                        duration = if (event.showCloseButton || event.canRetry) {
                            SnackbarDuration.Indefinite
                        } else {
                            SnackbarDuration.Long
                        },
                    ).let { result ->
                        if (result == SnackbarResult.ActionPerformed) {
                            if (event.canRetry) {
                                Timber.d("Retry button clicked")
                                viewModel.retryLoading()
                            }
                        }
                    }
                }
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupLiveDataObservers()
    }

    private fun setupLiveDataObservers() {
        viewModel.mapClickedEvent.observeK(viewLifecycleOwner) {
            openWorkoutMap()
        }

        viewModel.sportRowClickedEvent.observeNotNull(viewLifecycleOwner) {
            navigateToCalendarWorkoutList(
                requireContext(),
                it.startDate,
                it.endDate,
                it.workoutHeaderIds,
                it.activityType
            )
        }

        viewModel.shareSummaryButtonClickedEvent.observeNotNull(viewLifecycleOwner) {
            navigateToShareSummaryActivity(
                requireContext(),
                it.startDate,
                it.endDate
            )
        }
    }

    private fun navigateToCalendarWorkoutList(
        context: Context,
        startDate: LocalDate,
        endDate: LocalDate,
        workoutIds: List<Int>,
        activityType: ActivityType
    ) {
        val intent = CalendarWorkoutListActivity.newIntent(
            context,
            startDate,
            endDate,
            workoutIds,
            viewModel.getGranularity(),
            activityType
        )
        context.startActivity(intent)
    }

    private fun navigateToShareSummaryActivity(
        context: Context,
        startDate: LocalDate,
        endDate: LocalDate,
    ) {
        val intent = DiaryCalendarShareSummaryActivity.newIntent(
            context,
            startDate,
            endDate,
            viewModel.getGranularity()
        )
        context.startActivity(intent)
    }

    private fun openWorkoutMap() {
        workoutMapNavigator.startWorkoutMapActivity(
            requireContext(),
            currentUserController.currentUser,
            AnalyticsEvent.CALENDAR_SCREEN,
            false
        )
    }

    fun sendGranularityChangedAnalytics(granularity: String) {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.CALENDAR_CHANGE_CALENDAR_LEVEL,
            AnalyticsProperties().put(
                AnalyticsEventProperty.NEW_LEVEL,
                granularity
            )
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        workoutBroadcastActionListener.listener = object : WorkoutBroadcastActionListener.Listener {
            override fun onSyncFinished() = viewModel.retryLoading()
            override fun onUserStatusChanged() = viewModel.retryLoading()
            override fun onWorkoutSavedOrUpdated() = viewModel.retryLoading()
            override fun onWorkoutSynced() = viewModel.retryLoading()
            override fun onPictureStored() = Unit
        }
        workoutBroadcastActionListener.startListening()
    }

    override fun onDestroy() {
        workoutBroadcastActionListener.stopListening()
        workoutBroadcastActionListener.listener = null
        super.onDestroy()
    }

    companion object {
        // Make sure matches the name in nav graph xml so the last 30 days Fragment
        // without a wrapping ViewPager doesn't need special setup
        const val ARG_SHOW_ACTIVITIES_LIST =
            DiaryCalendarConstants.DIARY_CALENDAR_SCROLL_TO_ACTIVITIES_LIST
        const val ARG_FORCED_DIARY_TAB = "ARG_FORCED_DIARY_TAB"
    }
}
