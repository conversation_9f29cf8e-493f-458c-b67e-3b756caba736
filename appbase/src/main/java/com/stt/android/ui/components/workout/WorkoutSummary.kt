package com.stt.android.ui.components.workout

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import coil3.request.transformations
import coil3.transform.CircleCropTransformation
import com.stt.android.coil.placeholderWithFallback
import com.stt.android.compose.component.ActivityIconFlavor
import com.stt.android.compose.component.SuuntoActivityIcon
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.core.R
import com.stt.android.domain.workout.ActivityType
import com.stt.android.R as BR

@Composable
internal fun WorkoutSummary(
    activityTypeId: Int?,
    activityIconFlavor: ActivityIconFlavor,
    title: String,
    subtitle: String,
    username: String,
    userImageUrl: String?,
    isPrivate: Boolean,
    onUserClick: ((username: String) -> Unit)?,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier,
    ) {
        activityTypeId?.let {
            SuuntoActivityIcon(
                activityTypeId = it,
                flavor = activityIconFlavor,
            )
        }

        Spacer(modifier = Modifier.width(MaterialTheme.spacing.medium))

        Column(modifier = Modifier.weight(1.0F)) {
            WorkoutSummaryTitle(title = title)

            Spacer(modifier = Modifier.height(MaterialTheme.spacing.xxsmall))

            WorkoutSummarySubtitle(subtitle = subtitle, isPrivate = isPrivate)
        }

        userImageUrl?.let {
            WorkoutUserImage(
                username = username,
                imageUrl = userImageUrl,
                onUserClick = onUserClick,
            )
        }
    }
}

@Composable
private fun WorkoutSummaryTitle(
    title: String,
    modifier: Modifier = Modifier,
) {
    Text(
        text = title,
        modifier = modifier,
        style = MaterialTheme.typography.bodyLargeBold,
    )
}

@Composable
private fun WorkoutSummarySubtitle(
    subtitle: String,
    isPrivate: Boolean,
    modifier: Modifier = Modifier,
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier,
    ) {
        Box(modifier = Modifier.weight(1f, fill = false)) {
            Text(
                text = subtitle,
                overflow = TextOverflow.Ellipsis,
                maxLines = 1,
                style = MaterialTheme.typography.body,
                modifier = Modifier
                    .padding(end = MaterialTheme.spacing.xsmall)
                    .align(Alignment.CenterStart)
            )
        }

        if (isPrivate) {
            Icon(
                painter = painterResource(BR.drawable.ic_privacy_12),
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier
                    .size(MaterialTheme.iconSizes.tiny)
            )
        }
    }
}

@Composable
private fun WorkoutUserImage(
    username: String,
    imageUrl: String,
    onUserClick: ((username: String) -> Unit)?,
    modifier: Modifier = Modifier,
) {
    AsyncImage(
        model = ImageRequest.Builder(LocalContext.current)
            .data(imageUrl)
            .crossfade(true)
            .placeholderWithFallback(LocalContext.current, R.drawable.ic_default_profile_image_light)
            .transformations(CircleCropTransformation())
            .build(),
        modifier = modifier
            .clip(CircleShape)
            .size(MaterialTheme.iconSizes.medium)
            .then(onUserClick?.let {
                Modifier.clickableThrottleFirst { it.invoke(username) }
            } ?: Modifier),
        contentDescription = null,
    )
}

@Preview
@Composable
private fun WorkoutSummaryPreview() {
    WorkoutSummary(
        activityTypeId = ActivityType.RUNNING.id,
        activityIconFlavor = ActivityIconFlavor.FILL,
        title = "Running",
        subtitle = "Yesterday, 19:36 • Someone with a super long name that doesn't make any sense",
        username = "",
        userImageUrl = "",
        onUserClick = null,
        isPrivate = true,
    )
}
