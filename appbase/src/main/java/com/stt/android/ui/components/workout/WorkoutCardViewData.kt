package com.stt.android.ui.components.workout

import android.annotation.SuppressLint
import android.content.res.Resources
import android.net.Uri
import androidx.annotation.DrawableRes
import com.stt.android.compose.component.ActivityIconFlavor
import com.stt.android.maps.MapType
import com.stt.android.workoutdetail.tags.TagsData

data class WorkoutCardViewData(
    val coverInfo: List<CoverInfo>,
    val locationName: String,
    @DrawableRes val weatherDrawableRes: Int,
    val temperature: String,
    val windDirection: Float?,
    val windSpeed: String,
    val showAddPhotoButton: <PERSON>olean,
    val showPlayButton: Boolean,
    val title: String,
    val subtitle: String,
    val username: String,
    val userImageUrl: String?,
    val workoutValues: List<WorkoutValue>,
    val description: String,
    val maxDescriptionLines: Int,
    val achievementIcons: List<Int>,
    val tags: TagsData?,
    val isSubscribedToPremium: <PERSON>olean,
    val showReactions: Boolean,
    val commentCount: Int,
    val reactionCount: Int,
    val reactedByCurrentUser: <PERSON>olean,
    val showShareAndEdit: Boolean,
    val commentsToShow: List<Comment>,
    val activityTypeId: Int?,
    val activityIconFlavor: ActivityIconFlavor = ActivityIconFlavor.FILL,
    val isPrivate: Boolean,
) {
    sealed class CoverInfo {
        data class Image(val uri: Uri) : CoverInfo()

        data class Video(
            val uri: Uri,
            val userAgent: String,
        ) : CoverInfo()

        data class Map(
            val mapType: MapType,
            val polyline: String,
            val disableZoomToBounds: Boolean,
        ) : CoverInfo()
    }

    data class WorkoutValue(
        val label: String,
        val value: String,
        val unit: String,
    )

    data class Comment(
        val username: String,
        val userRealName: String,
        val userImageUrl: String,
        val comment: String,
    )

    companion object {
        @SuppressLint("InlinedApi")
        val EMPTY = WorkoutCardViewData(
            coverInfo = emptyList(),
            locationName = "",
            weatherDrawableRes = Resources.ID_NULL,
            temperature = "",
            windDirection = null,
            windSpeed = "",
            showAddPhotoButton = false,
            showPlayButton = false,
            title = " ", // We use a space for title, subtitle, etc. so that it can calculate the correct height.
            subtitle = " ",
            username = " ",
            userImageUrl = null,
            workoutValues = listOf(
                WorkoutValue(
                    label = " ",
                    value = " ",
                    unit = " ",
                ),
            ),
            description = "",
            maxDescriptionLines = Int.MAX_VALUE,
            achievementIcons = emptyList(),
            tags = null,
            isSubscribedToPremium = false,
            showReactions = false,
            commentCount = 0,
            reactionCount = 0,
            reactedByCurrentUser = false,
            showShareAndEdit = false,
            commentsToShow = emptyList(),
            activityTypeId = null,
            activityIconFlavor = ActivityIconFlavor.FILL,
            isPrivate = false,
        )
    }
}

