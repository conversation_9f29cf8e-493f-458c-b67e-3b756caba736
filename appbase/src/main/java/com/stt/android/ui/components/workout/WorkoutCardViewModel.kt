package com.stt.android.ui.components.workout

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Resources
import androidx.lifecycle.ViewModel
import com.amersports.formatter.Unit
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.google.maps.android.PolyUtil
import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.DiveExtensionDataModel
import com.stt.android.controllers.PicturesController
import com.stt.android.controllers.ReactionModel
import com.stt.android.controllers.SlopeSkiDataModel
import com.stt.android.controllers.SummaryExtensionDataModel
import com.stt.android.controllers.VideoModel
import com.stt.android.controllers.WeatherExtensionDataModel
import com.stt.android.controllers.WorkoutCommentController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.achievements.GetAchievementUseCase
import com.stt.android.domain.mapbox.FetchLocationNameUseCase
import com.stt.android.domain.review.ReviewState
import com.stt.android.domain.tags.UserTagsRepository
import com.stt.android.domain.user.ActivityTypeHelper
import com.stt.android.domain.user.GetUserByUsernameUseCase
import com.stt.android.domain.user.ReactionSummary
import com.stt.android.domain.user.User
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workout.SpeedPaceState
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.WeatherExtension
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.domain.workouts.isDiving
import com.stt.android.domain.workouts.isPrivate
import com.stt.android.extensions.getWindSpeedText
import com.stt.android.extensions.isTeamAndRacketSports
import com.stt.android.extensions.loadExtension
import com.stt.android.extensions.weatherDrawableRes
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.remote.UserAgent
import com.stt.android.ui.extensions.areExtensionsNeededForSummaryData
import com.stt.android.ui.extensions.defaultMapType
import com.stt.android.ui.extensions.getSummaryData
import com.stt.android.ui.extensions.supportWorkoutAnalysisOnMap
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.viewmodel.sort
import com.stt.android.workoutdetail.comments.WorkoutComment
import com.stt.android.workoutdetail.tags.TagsData
import com.stt.android.workouts.WorkoutSummaryData
import com.stt.android.workouts.details.values.WorkoutValue
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import javax.inject.Inject

@HiltViewModel
class WorkoutCardViewModel @Inject constructor(
    @ApplicationContext private val appContext: Context,
    @UserAgent private val userAgent: String,
    private val currentUserController: CurrentUserController,
    val isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase,
    private val getUserByUsernameUseCase: GetUserByUsernameUseCase,
    private val picturesController: PicturesController,
    private val videoModel: VideoModel,
    private val reactionModel: ReactionModel,
    private val fetchLocationNameUseCase: FetchLocationNameUseCase,
    private val weatherExtensionDataModel: WeatherExtensionDataModel,
    private val summaryExtensionDataModel: SummaryExtensionDataModel,
    private val diveExtensionDataModel: DiveExtensionDataModel,
    private val slopeSkiDataModel: SlopeSkiDataModel,
    private val getAchievementUseCase: GetAchievementUseCase,
    private val workoutCommentController: WorkoutCommentController,
    private val userTagsRepository: UserTagsRepository,
    private val infoModelFormatter: InfoModelFormatter,
    private val unitConverter: JScienceUnitConverter,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : ViewModel() {
    data class Configuration(
        val showCoverMap: Boolean = false,
        val showCoverImage: Boolean = false,
        val showWeather: Boolean = false,
        val showPlayButton: Boolean = false,
        val showDate: Boolean = true,
        val showUser: Boolean = false,
        val maxDescriptionLines: Int = Int.MAX_VALUE,
        val showAchievements: Boolean = false,
        val showTags: Boolean = false,
        val showReactions: Boolean = false,
        val showComments: Boolean = false,
    )

    @SuppressLint("InlinedApi")
    suspend fun createViewData(
        config: Configuration,
        workoutHeader: WorkoutHeader,
    ): WorkoutCardViewData = withContext(coroutinesDispatchers.computation) {
        val loadCoverInfoAsync = if (config.showCoverImage || config.showCoverMap) {
            async { loadCoverInfo(workoutHeader, config) }
        } else {
            null
        }
        val loadLocationNameAsync = if ((config.showCoverMap || config.showCoverImage) && config.showWeather) {
            async { loadLocationName(workoutHeader) }
        } else {
            null
        }
        val loadWeatherAsync = if ((config.showCoverMap || config.showCoverImage) && config.showWeather) {
            async { loadWeather(workoutHeader)?.toDomainEntity() }
        } else {
            null
        }
        val loadUserAsync = if (config.showUser) {
            async { loadUser(workoutHeader) }
        } else {
            null
        }
        val loadAchievementIconsAsync = if (config.showAchievements) {
            async { loadAchievementIcons(workoutHeader) }
        } else {
            null
        }
        val loadReactionSummaryAsync = if (config.showReactions) {
            async { loadReactionSummary(workoutHeader) }
        } else {
            null
        }
        val loadCommentsAsync = if (config.showComments) {
            async { loadComments(workoutHeader) }
        } else {
            null
        }
        val loadTagsAsync = if (config.showTags) {
            async { loadTags(workoutHeader) }
        } else {
            null
        }
        val loadWorkoutSummaryDataAsync = async { loadWorkoutSummaryData(workoutHeader) }
        val isSubscribedToPremium = isSubscribedToPremiumUseCase.invoke().first()
        val coverInfo = loadCoverInfoAsync?.await() ?: emptyList()
        val locationName = loadLocationNameAsync?.await()
        val weather = loadWeatherAsync?.await()
        val user = loadUserAsync?.await()
        val achievementIcons = loadAchievementIconsAsync?.await().orEmpty()
        val reactionSummary = loadReactionSummaryAsync?.await()
        val comments = loadCommentsAsync?.await().orEmpty()
        val tags = loadTagsAsync?.await()
        val workoutSummaryData = loadWorkoutSummaryDataAsync.await()

        val route = workoutHeader.polyline
            ?.takeIf(String::isNotBlank)
            ?.let(PolyUtil::decode)
            ?: emptyList()
        val isOwnWorkout = workoutHeader.username == currentUserController.username

        WorkoutCardViewData(
            coverInfo = coverInfo,
            locationName = locationName.orEmpty(),
            weatherDrawableRes = weather?.weatherDrawableRes ?: Resources.ID_NULL,
            temperature = weather?.temperature
                ?.let { temperature ->
                    infoModelFormatter.formatValue(SummaryItem.AVGTEMPERATURE, temperature)
                        .toValueString(appContext)
                }.orEmpty(),
            windDirection = weather?.windDirection,
            windSpeed = weather.getWindSpeedText(infoModelFormatter).orEmpty(),
            showAddPhotoButton = config.showCoverImage &&
                isOwnWorkout &&
                coverInfo.none { it is WorkoutCardViewData.CoverInfo.Image || it is WorkoutCardViewData.CoverInfo.Video },
            showPlayButton = config.showCoverMap &&
                config.showPlayButton &&
                workoutHeader.supportWorkoutAnalysisOnMap(route),
            title = appContext.getString(workoutHeader.activityType.localizedStringId),
            subtitle = formatSubtitle(
                context = appContext,
                workoutHeader = workoutHeader,
                user = user,
                showDate = config.showDate,
                showUser = config.showUser,
            ),
            username = workoutHeader.username,
            userImageUrl = if (config.showUser) {
                user?.profileImageUrl.orEmpty()
            } else {
                null
            },
            workoutValues = listOf(
                WorkoutCardViewData.WorkoutValue(
                    label = " ",
                    value = " ",
                    unit = " ",
                ),
            ),
            description = workoutHeader.description.orEmpty(),
            maxDescriptionLines = config.maxDescriptionLines,
            achievementIcons = achievementIcons,
            tags = tags,
            isSubscribedToPremium = isSubscribedToPremium,
            showReactions = config.showReactions,
            commentCount = workoutHeader.commentCount,
            reactionCount = workoutHeader.reactionCount,
            reactedByCurrentUser = reactionSummary?.isUserReacted ?: false,
            showShareAndEdit = isOwnWorkout,
            commentsToShow = comments,
            activityTypeId = workoutHeader.activityType.id,
            isPrivate = workoutHeader.isPrivate,
        ).addWorkoutSummary(workoutHeader, workoutSummaryData)
    }

    private suspend fun loadCoverInfo(
        workoutHeader: WorkoutHeader,
        config: Configuration,
    ): List<WorkoutCardViewData.CoverInfo> = withContext(coroutinesDispatchers.io) {
        runSuspendCatching {
            val isOwnWorkout = workoutHeader.username == currentUserController.username
            buildList {
                if (config.showCoverImage) {
                    videoModel.findByWorkoutId(workoutHeader.id)
                        .forEach { videoInfo ->
                            if (isOwnWorkout || videoInfo.reviewState.isUnknownOrPassed()) {
                                videoInfo.getUri(appContext)
                                    ?.let { uri ->
                                        WorkoutCardViewData.CoverInfo.Video(
                                            uri = uri,
                                            userAgent = userAgent,
                                        )
                                    }
                                    ?.let(::add)
                            }
                        }

                    picturesController.findByWorkoutId(workoutHeader.id)
                        .forEach { imageInfo ->
                            if (isOwnWorkout || imageInfo.reviewState.isUnknownOrPassed()) {
                                imageInfo.getFeedUri(appContext)
                                    .let(WorkoutCardViewData.CoverInfo::Image)
                                    .let(::add)
                            }
                        }
                }

                if (config.showCoverMap) {
                    workoutHeader.polyline
                        ?.takeUnless(String::isEmpty)
                        ?.let { polyline ->
                            WorkoutCardViewData.CoverInfo.Map(
                                mapType = workoutHeader.defaultMapType,
                                polyline = polyline,
                                disableZoomToBounds = workoutHeader.isDiving,
                            )
                        }
                        ?.let(::add)
                }
            }
        }.getOrElse { e ->
            Timber.w(e, "Failed to load cover info")
            emptyList()
        }
    }

    private suspend fun loadWeather(
        workoutHeader: WorkoutHeader,
    ): WeatherExtension? = withContext(coroutinesDispatchers.io) {
        runCatching {
            weatherExtensionDataModel.findByWorkoutId(workoutHeader.id)
        }.getOrElse { e ->
            Timber.w(e, "Failed to load weather")
            null
        }
    }

    private suspend fun loadLocationName(
        workoutHeader: WorkoutHeader,
    ): String? = runSuspendCatching {
        workoutHeader.startPosition
            ?.let { startPosition ->
                fetchLocationNameUseCase(
                    FetchLocationNameUseCase.Params(
                        latitude = startPosition.latitude,
                        longitude = startPosition.longitude,
                        useCoarseAccuracy = true,
                        useCacheOnly = false,
                    )
                )?.city
            }
    }.getOrElse { e ->
        Timber.w(e, "Failed to load location name")
        null
    }

    private suspend fun loadUser(workoutHeader: WorkoutHeader): User? = runSuspendCatching {
        getUserByUsernameUseCase.getUserByUsername(
            username = workoutHeader.username,
            queryRemoteIfNeeded = true,
        )
    }.getOrElse { e ->
        Timber.w(e, "Failed to load user")
        null
    }

    private suspend fun loadAchievementIcons(workoutHeader: WorkoutHeader): List<Int> = runSuspendCatching {
        val achievement = workoutHeader.key
            ?.let { getAchievementUseCase(it) }
            ?: return@runSuspendCatching emptyList()
        buildList {
            repeat(achievement.personalBestAchievements.size) {
                add(R.drawable.achievement_trophy_icon)
            }
            repeat(achievement.cumulativeAchievements.size) {
                add(R.drawable.achievement_star_icon)
            }
        }
    }.getOrElse { e ->
        Timber.w(e, "Failed to load achievement")
        emptyList()
    }

    private suspend fun loadReactionSummary(
        workoutHeader: WorkoutHeader,
    ): ReactionSummary? = withContext(coroutinesDispatchers.io) {
        runSuspendCatching {
            workoutHeader.key
                ?.let { reactionModel.findSummary(it, ReactionSummary.REACTION_LIKE) }
        }.getOrElse { e ->
            Timber.w(e, "Failed to load reaction")
            null
        }
    }

    private suspend fun loadComments(
        workoutHeader: WorkoutHeader,
    ): List<WorkoutCardViewData.Comment> = withContext(coroutinesDispatchers.io) {
        if (workoutHeader.commentCount == 0) {
            emptyList()
        } else {
            workoutHeader.key
                ?.let(workoutCommentController::find)
                ?.sortedBy(WorkoutComment::getTimestamp)
                ?.takeLast(MAX_NUMBER_OF_COMMENTS_TO_SHOW)
                ?.map { workoutComment ->
                    WorkoutCardViewData.Comment(
                        username = workoutComment.username,
                        userRealName = workoutComment.realNameOrUsername,
                        userImageUrl = workoutComment.profilePictureUrl.orEmpty(),
                        comment = workoutComment.message,
                    )
                }
                ?: emptyList()
        }
    }

    private suspend fun loadTags(
        workoutHeader: WorkoutHeader,
    ): TagsData? = withContext(coroutinesDispatchers.io) {
        val isOwnWorkout = workoutHeader.username == currentUserController.username
        val isSubscribedToPremium = isSubscribedToPremiumUseCase.invoke().first()
        val suuntoTags = workoutHeader.suuntoTags
            .filter { suuntoTag ->
                isSubscribedToPremium || suuntoTag.editable
            }.sort()
        val userTags = (
            workoutHeader.userTags.takeUnless(List<*>::isEmpty)
                ?: userTagsRepository.getAllUserTagsForWorkoutId(workoutHeader.id)
            )
            .sort()
        if (suuntoTags.isNotEmpty() || userTags.isNotEmpty()) {
            TagsData(
                deviceTag = null,
                suuntoTags = suuntoTags,
                userTags = userTags,
                isOwnWorkout = isOwnWorkout,
            )
        } else {
            null
        }
    }

    private suspend fun loadWorkoutSummaryData(
        workoutHeader: WorkoutHeader,
    ): WorkoutSummaryData? = withContext(coroutinesDispatchers.io) {
        runSuspendCatching {
            loadExtensionsIfNeeded(workoutHeader)
                .let { extensions ->
                    workoutHeader.getSummaryData(
                        extensions = extensions,
                        infoModelFormatter = infoModelFormatter,
                    )
                }.takeUnless { workoutSummaryData ->
                    workoutSummaryData.left == null
                }
        }.getOrElse { e ->
            Timber.w(e, "Failed to load workout summary data")
            null
        }
    }

    private suspend fun loadExtensionsIfNeeded(
        workoutHeader: WorkoutHeader,
    ): List<WorkoutExtension> = withContext(coroutinesDispatchers.io) {
        if (!workoutHeader.areExtensionsNeededForSummaryData()) {
            return@withContext emptyList()
        }

        val summaryExtensionAsync = async(coroutinesDispatchers.io) {
            summaryExtensionDataModel.loadExtension(workoutHeader)
        }
        val skiExtensionAsync = async(coroutinesDispatchers.io) {
            slopeSkiDataModel.loadExtension(workoutHeader)
        }
        val diveExtension = diveExtensionDataModel.loadExtension(workoutHeader)
        val summaryExtension = summaryExtensionAsync.await()
        val skiExtension = skiExtensionAsync.await()

        listOfNotNull(summaryExtension, skiExtension, diveExtension)
    }

    private suspend fun WorkoutCardViewData.addWorkoutSummary(
        workoutHeader: WorkoutHeader,
        workoutSummaryData: WorkoutSummaryData?,
    ): WorkoutCardViewData = workoutSummaryData?.let {
        copy(
            workoutValues = listOfNotNull(
                workoutSummaryData.left?.toWorkoutCardValue(),
                workoutSummaryData.middle?.toWorkoutCardValue(),
                workoutSummaryData.right?.toWorkoutCardValue(),
            ),
        )
    } ?: addDefaultValues(workoutHeader)

    private fun WorkoutValue.toWorkoutCardValue(): WorkoutCardViewData.WorkoutValue = WorkoutCardViewData.WorkoutValue(
        label = label,
        value = value.orEmpty(),
        unit = getUnitLabel(appContext),
    )

    private suspend fun WorkoutCardViewData.addDefaultValues(
        workoutHeader: WorkoutHeader,
    ): WorkoutCardViewData = withContext(coroutinesDispatchers.computation) {
        val firstWorkoutValue = WorkoutCardViewData.WorkoutValue(
            label = appContext.getString(R.string.duration),
            value = infoModelFormatter
                .formatValue(SummaryItem.DURATION, workoutHeader.totalTime)
                .value
                .orEmpty(),
            unit = "",
        )

        val secondWorkoutValue: WorkoutCardViewData.WorkoutValue
        val thirdWorkoutValue: WorkoutCardViewData.WorkoutValue
        val activityType = workoutHeader.activityType
        if ((activityType.isIndoor || activityType.isTeamAndRacketSports) &&
            activityType != ActivityType.GOLF &&
            !activityType.isSwimming
        ) {
            secondWorkoutValue = infoModelFormatter.formatValue(
                SummaryItem.ENERGY,
                unitConverter.convert(workoutHeader.energyConsumption, Unit.KCAL, Unit.J),
            ).toWorkoutCardValue()

            thirdWorkoutValue = infoModelFormatter.formatValue(
                SummaryItem.AVGHEARTRATE,
                unitConverter.convert(workoutHeader.heartRateAverage, Unit.RPM, Unit.HZ),
            ).toWorkoutCardValue()
        } else if (activityType.usesNauticalUnits) {
            secondWorkoutValue = infoModelFormatter
                .formatValue(SummaryItem.NAUTICALDISTANCE, workoutHeader.totalDistance)
                .toWorkoutCardValue()

            thirdWorkoutValue = infoModelFormatter.formatValue(
                SummaryItem.AVGNAUTICALSPEED,
                workoutHeader.avgSpeed,
            ).toWorkoutCardValue()
        } else if (activityType.isSwimming) {
            secondWorkoutValue = infoModelFormatter
                .formatValue(SummaryItem.SWIMDISTANCE, workoutHeader.totalDistance)
                .toWorkoutCardValue()

            thirdWorkoutValue = infoModelFormatter
                .formatValue(SummaryItem.AVGSWIMPACE, workoutHeader.avgSpeed)
                .toWorkoutCardValue()
        } else {
            secondWorkoutValue = infoModelFormatter
                .formatValue(SummaryItem.DISTANCE, workoutHeader.totalDistance)
                .toWorkoutCardValue()

            val speedPaceState = ActivityTypeHelper.getSpeedPaceState(false, appContext, activityType)
            when (speedPaceState) {
                SpeedPaceState.SPEED -> {
                    thirdWorkoutValue = infoModelFormatter
                        .formatValue(SummaryItem.AVGSPEED, workoutHeader.avgSpeed)
                        .toWorkoutCardValue()
                }
                SpeedPaceState.SPEEDKNOTS -> {
                    thirdWorkoutValue = infoModelFormatter.formatValue(SummaryItem.AVGNAUTICALSPEED, workoutHeader.avgSpeed)
                        .toWorkoutCardValue()
                }
                SpeedPaceState.PACE -> {
                    thirdWorkoutValue = infoModelFormatter
                        .formatValue(SummaryItem.AVGPACE, workoutHeader.avgSpeed)
                        .toWorkoutCardValue()
                }
            }
        }

        copy(
            workoutValues = listOf(firstWorkoutValue, secondWorkoutValue, thirdWorkoutValue),
        )
    }

    companion object {
        private const val MAX_NUMBER_OF_COMMENTS_TO_SHOW = 2

        private fun WorkoutValue.toValueString(context: Context): String = "$value ${getUnitLabel(context)}"

        fun formatSubtitle(
            context: Context,
            workoutHeader: WorkoutHeader,
            user: User?,
            showDate: Boolean,
            showUser: Boolean,
        ): String = buildString {
            append(formatDateTime(context, workoutHeader, showDate))

            if (showUser && user != null) {
                append(" • ")
                append(user.realNameOrUsername)
            }
        }

        private fun formatDateTime(context: Context, workoutHeader: WorkoutHeader, showDate: Boolean): String {
            if (!showDate) {
                return TextFormatter.formatTime(context, workoutHeader.startTime)
            }

            val workoutDate = LocalDate.ofInstant(
                Instant.ofEpochMilli(workoutHeader.startTime),
                ZoneId.systemDefault(),
            )
            val today = LocalDate.now()
            return if (today == workoutDate) {
                // The workout was done today.
                // For workouts from today we intentionally use the stop time instead of the
                // start time so that the relative format feels more natural.
                TextFormatter.formatRelativeDateSpan(context.resources, workoutHeader.stopTime)
                    .toString()
            } else if (today.minusDays(1) == workoutDate) {
                // The workout was done yesterday.
                context.getString(R.string.yesterday_at, TextFormatter.formatTime(context, workoutHeader.startTime))
            } else {
                TextFormatter.formatDateTime(context, workoutHeader.startTime)
            }
        }

        private fun ReviewState?.isUnknownOrPassed(): Boolean =
            this == null || this == ReviewState.PASS
    }
}
