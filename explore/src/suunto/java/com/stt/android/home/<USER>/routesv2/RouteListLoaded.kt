package com.stt.android.home.explore.routesv2

import androidx.annotation.StringRes
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.home.explore.R
import com.stt.android.home.explore.toproutes.filter.SortFilter
import com.stt.android.home.explore.toproutes.filter.TopRouteFilter
import com.stt.android.mapping.InfoModelFormatter

@Composable
fun RouteListLoaded(
    searchQuery: String,
    @StringRes emptyResId: Int,
    topRouteFilter: TopRouteFilter,
    routesContainer: LibraryRoutesContainer,
    infoModelFormatter: InfoModelFormatter,
    measurementUnit: MeasurementUnit,
    modifier: Modifier = Modifier,
) {
    var filterTopRoutes by remember { mutableStateOf(routesContainer.routes) }

    LaunchedEffect(searchQuery, topRouteFilter, routesContainer) {
        filterTopRoutes = getFilterRouteList(
            routesContainer.routes,
            searchQuery,
            topRouteFilter,
            measurementUnit
        )
    }

    when {
        routesContainer.routes.isEmpty() -> {
            RouteEmptyContent(
                emptyResId = emptyResId,
                modifier = modifier
            )
        }

        filterTopRoutes.isEmpty() -> {
            RouteFilterEmptyContent()
        }

        else -> {
            LazyColumn(
                modifier = modifier.fillMaxSize(),
            ) {
                items(filterTopRoutes) { routeItem ->
                    RouteCard(
                        routeItem = routeItem,
                        infoModelFormatter = infoModelFormatter,
                        measurementUnit = measurementUnit,
                        onShareClick = routesContainer.onShareClicked,
                        onSavedClick = { favoriteSaved ->
                            (routeItem as? PopularRouteItem)?.let {
                                it.isLatestFavoriteSaved = favoriteSaved
                                routesContainer.onRouteFavoriteClicked?.invoke(it)
                            }
                        },
                        onRouteClick = {
                            routesContainer.onRouteClicked(routeItem)
                        },
                        onAddRouteToWatch = routesContainer.onAddToWatchToggled
                    )
                }
            }
        }
    }
}

private fun getFilterRouteList(
    routeItems: List<LibraryRouteItem>,
    searchQuery: String,
    topRouteFilter: TopRouteFilter,
    measurementUnit: MeasurementUnit,
): List<LibraryRouteItem> {
    val distanceRange = topRouteFilter.distanceFilter.let {
        it.getLeftInMeter(measurementUnit)..it.getRightInMeter(measurementUnit)
    }
    return routeItems
        .asSequence()
        .filter { route ->
            searchQuery.isEmpty() || route.name.contains(searchQuery, ignoreCase = true)
        }
        .filter { route ->
            topRouteFilter.activityTypes?.let { types ->
                route.activityIds.any { it in types }
            } ?: true
        }
        .filter { route ->
            route.totalDistance in distanceRange
        }
        .sortedWith(
            when (topRouteFilter.sortFilter) {
                SortFilter.Latest -> compareByDescending { it.createdDate }
                SortFilter.Nearest -> compareBy { it.distanceFromCurrentLocation }
                SortFilter.UseInWatch -> compareByDescending { it.watchEnabled }
                else -> compareBy { 0 }
            }
        )
        .toList()
}

@Preview(backgroundColor = 0xFFFFFF, showBackground = true)
@Composable
private fun RouteEmptyContentPreview() {
    M3AppTheme {
        RouteEmptyContent(
            emptyResId = R.string.popular_route_empty_tip
        )
    }
}


