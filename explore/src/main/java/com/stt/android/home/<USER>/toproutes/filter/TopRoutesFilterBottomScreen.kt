package com.stt.android.home.explore.toproutes.filter

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.rememberNestedScrollInteropConnection
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.explore.R
import com.stt.android.home.explore.toproutes.carousel.RouteFeature
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.utils.takeIfNotEmpty

@Composable
fun TopRoutesFilterBottomScreen(
    viewModel: TopRoutesFilterViewModel,
    selectedActivityTypes: List<ActivityType>?,
    visibleFeatures: List<RouteFeature>,
    measurementUnit: MeasurementUnit,
    infoModelFormatter: InfoModelFormatter,
    onItemClick: (RouteFeature) -> Unit,
    selectedActivityChanged: (List<ActivityType>?) -> Unit,
    modifier: Modifier = Modifier,
) {
    val viewData by viewModel.topRouteFilterViewData.collectAsState()

    var isFilterShown by rememberSaveable { mutableStateOf(false) }
    var filterChanged by rememberSaveable { mutableStateOf(false) }
    var latestTopRouteFilter by remember {
        mutableStateOf(TopRouteFilter(activityTypes = selectedActivityTypes))
    }
    val sortFilterList = remember {
        listOf(SortFilter.MostPopular, SortFilter.Nearest)
    }

    var filterTopRoutes by remember { mutableStateOf(visibleFeatures) }

    LaunchedEffect(visibleFeatures, latestTopRouteFilter) {
        val activityTypes = (latestTopRouteFilter.activityTypes?.takeIfNotEmpty()
            ?: viewModel.getDefaultTopRoutesActivityTypeList()).toList()
        filterTopRoutes = filterAndSortRoutes(
            activityTypes,
            measurementUnit,
            visibleFeatures,
            latestTopRouteFilter
        )
    }

    LaunchedEffect(filterTopRoutes) {
        viewModel.loadRoute(filterTopRoutes)
    }

    val nestedScrollConnection = rememberNestedScrollInteropConnection()

    Column(
        modifier = modifier
            .fillMaxWidth()
            .nestedScroll(nestedScrollConnection)
    ) {
        PopularRouteFilter(
            isOpen = isFilterShown,
            showResetIcon = filterChanged,
            onReset = {
                filterChanged = false
                latestTopRouteFilter = TopRouteFilter()
                selectedActivityChanged(latestTopRouteFilter.activityTypes)
            },
            onClick = {
                isFilterShown = !isFilterShown
            }
        )

        if (isFilterShown) {
            TopRoutesFilterScreen(
                sortFilterList = sortFilterList,
                allTypes = viewModel.getDefaultTopRoutesActivityTypeList(),
                topRouteFilter = latestTopRouteFilter,
                measurementUnit = measurementUnit,
                onFilterChanged = {
                    filterChanged = true
                    latestTopRouteFilter = it
                    selectedActivityChanged(latestTopRouteFilter.activityTypes)
                },
            )
            HorizontalDivider(color = MaterialTheme.colorScheme.lightGrey)
        }

        Box(modifier = Modifier.weight(1f)) {
            FilterTopRoutesListScreen(
                viewData = viewData,
                routeFeatures = filterTopRoutes,
                infoModelFormatter = infoModelFormatter,
                onItemClick = onItemClick,
                onLoadRoute = { feature ->
                    viewModel.loadRouteIfNeeded(feature)
                }
            )
        }
    }
}

fun filterAndSortRoutes(
    activityTypes: List<ActivityType>,
    measurementUnit: MeasurementUnit,
    routes: List<RouteFeature>,
    filter: TopRouteFilter
): List<RouteFeature> {
    val distanceMinInMeter = filter.distanceFilter.getLeftInMeter(measurementUnit)
    val distanceMaxInMeter = filter.distanceFilter.getRightInMeter(measurementUnit)

    val filtered = routes.filter {
        it.activityType in activityTypes &&
            it.distance in distanceMinInMeter..distanceMaxInMeter
    }

    return when (filter.sortFilter) {
        SortFilter.MostPopular -> filtered.sortedByDescending { it.popularity }
        SortFilter.Nearest -> filtered.sortedBy { it.awayYouDistance }
        else -> filtered
    }
}

@Composable
fun PopularRouteFilter(
    isOpen: Boolean,
    showResetIcon: Boolean,
    onReset: () -> Unit,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    showFilterIcon: Boolean = true,
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .fillMaxWidth()
            .padding(MaterialTheme.spacing.medium)
    ) {
        Text(
            text = stringResource(id = R.string.top_route_popular_routes),
            style = MaterialTheme.typography.bodyLargeBold,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.weight(1f)
        )

        if (showResetIcon) {
            ResetButton(
                onClick = onReset,
            )

            Spacer(modifier = Modifier.width(MaterialTheme.spacing.medium))
        }

        if (showFilterIcon) {
            Icon(
                painter = if (isOpen) {
                    painterResource(R.drawable.ic_filter_expand)
                } else {
                    painterResource(R.drawable.ic_filter_collapse)
                },
                contentDescription = null,
                tint = MaterialTheme.colorScheme.nearBlack,
                modifier = Modifier
                    .size(MaterialTheme.iconSizes.small)
                    .clip(CircleShape)
                    .clickableThrottleFirst(onClick = onClick)
            )
        }
    }
}

@Preview
@Composable
private fun ResetButtonPreview() {
    ResetButton(
        onClick = {}
    )
}
