package com.stt.android.home.explore.routes;

import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory;
import com.stt.android.backgroundwork.WorkerKey;
import com.stt.android.data.Local;
import com.stt.android.data.Remote;
import com.stt.android.data.routes.BaseRouteRemoteSyncJob;
import com.stt.android.data.routes.PolylineDecoder;
import com.stt.android.data.routes.RouteDataSource;
import com.stt.android.data.routes.RouteLocalDataSource;
import com.stt.android.data.routes.RouteRemoteDataSource;
import com.stt.android.data.routes.RouteRemoteSyncJob;
import com.stt.android.data.routes.TopRouteCache;
import com.stt.android.data.routes.TopRouteCacheImpl;
import com.stt.android.data.routes.popular.PopularRouteDataSource;
import com.stt.android.data.routes.popular.PopularRouteLocalDataSource;
import com.stt.android.data.routes.popular.PopularRouteRemoteSyncJob;
import com.stt.android.data.source.local.DaoFactory;
import com.stt.android.data.source.local.routes.RouteDao;
import com.stt.android.data.source.local.routes.popular.PopularRouteDao;
import com.stt.android.domain.routes.RouteTool;
import com.stt.android.home.explore.GoogleMapsPolylineDecoder;
import com.stt.android.home.explore.routes.planner.RouteAnalyticsTracker;
import com.stt.android.home.explore.routes.planner.RoutePlannerUtils;
import dagger.Binds;
import dagger.Module;
import dagger.Provides;
import dagger.multibindings.IntoMap;

@Module
public abstract class RouteModule {
    @Provides
    public static RouteDao provideRouteDao(DaoFactory daoFactory) {
        return daoFactory.getRouteDao();
    }

    @Binds
    @Local
    public abstract RouteDataSource bindRouteLocalDataSource(RouteLocalDataSource routeLocalDataSource);

    @Binds
    @Remote
    public abstract RouteDataSource bindRouteRemoteDataSource(RouteRemoteDataSource routeRemoteDataSource);

    @Binds
    public abstract PolylineDecoder bindGoogleMapsPolylineDecoder(
        GoogleMapsPolylineDecoder googleMapsPolylineDecoder);

    @Binds
    public abstract BaseRouteRemoteSyncJob.ForegroundInfoBuilder bindRouteRemoteSyncJobForegroundInfoBuilder(
        RouteRemoteSyncJobForegroundInfoBuilderImpl routeRemoteSyncJobForegroundInfoBuilderImpl
    );

    @Binds
    abstract RouteTool provideRouteTool(RoutePlannerUtils routePlannerUtils);

    @Binds
    public abstract RouteAnalytics bindRouteAnalytics(RouteAnalyticsTracker routeAnalyticsTracker);

    @Binds
    public abstract TopRouteCache bindTopRouteCache(TopRouteCacheImpl impl);

    @Provides
    public static PopularRouteDao providePopularRouteDao(DaoFactory daoFactory) {
        return daoFactory.getPopularRouteDao();
    }

    @Binds
    @IntoMap
    @WorkerKey(RouteRemoteSyncJob.class)
    public abstract CoroutineWorkerAssistedFactory bindRouteRemoteSyncJob(RouteRemoteSyncJob.Factory routeRemoteSyncJobFactory);

    @Binds
    @Local
    public abstract PopularRouteDataSource bindPopularRouteLocalDataSource(PopularRouteLocalDataSource popularRouteLocalDataSource);

    @Binds
    @IntoMap
    @WorkerKey(PopularRouteRemoteSyncJob.class)
    public abstract CoroutineWorkerAssistedFactory bindPopularRouteRemoteSyncJob(PopularRouteRemoteSyncJob.Factory popularRouteRemoteSyncJobFactory);
}
