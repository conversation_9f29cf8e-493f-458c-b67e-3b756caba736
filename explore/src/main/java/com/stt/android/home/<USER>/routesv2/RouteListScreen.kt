package com.stt.android.home.explore.routesv2

import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.home.explore.R
import com.stt.android.home.explore.toproutes.filter.SortFilter
import com.stt.android.home.explore.toproutes.filter.TopRouteFilter
import com.stt.android.home.explore.toproutes.filter.TopRoutesFilterScreen
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.maps.TopRouteType

@Composable
fun RouteListScreen(
    viewModel: RouteListViewModel,
    topRouteTypes: List<TopRouteType>,
    infoModelFormatter: InfoModelFormatter,
    measurementUnit: MeasurementUnit,
    modifier: Modifier = Modifier,
) {
    val myRouteViewData = viewModel.myRouteListRouteViewData.collectAsState().value
    val popularRouteViewData = viewModel.popularRouteListRouteViewData.collectAsState().value
    val currentPage = viewModel.currentRoutePage.collectAsState().value

    var isFilterShown by rememberSaveable { mutableStateOf(false) }
    var filterChanged by rememberSaveable { mutableStateOf(false) }
    var searchQuery by rememberSaveable { mutableStateOf("") }
    var showActivityFilter by rememberSaveable {
        mutableStateOf(currentPage == RoutesPage.POPULAR)
    }
    var latestTopRouteFilter by remember(currentPage) {
        mutableStateOf(TopRouteFilter(sortFilter = SortFilter.Latest))
    }
    val sortFilterList = remember(currentPage) {
        if (currentPage == RoutesPage.MINE) {
            listOf(SortFilter.Latest, SortFilter.Nearest, SortFilter.UseInWatch)
        } else {
            listOf(SortFilter.Latest, SortFilter.Nearest)
        }
    }
    var showFilterIcon by rememberSaveable { mutableStateOf(false) }
    LaunchedEffect(currentPage, myRouteViewData, popularRouteViewData) {
        showFilterIcon = when (currentPage) {
            RoutesPage.MINE -> if (myRouteViewData is RouteViewData.Loaded) {
                myRouteViewData.routesContainer.routes.isNotEmpty()
            } else {
                false
            }

            RoutesPage.POPULAR -> if (popularRouteViewData is RouteViewData.Loaded) {
                popularRouteViewData.routesContainer.routes.isNotEmpty()
            } else {
                false
            }
        }
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.onPrimary)
    ) {
        RoutesFilterBar(
            modifier = Modifier.padding(vertical = MaterialTheme.spacing.medium),
            showFilterIcon = showFilterIcon,
            currentRoutesPage = currentPage,
            isFilterExpand = !isFilterShown,
            showResetIcon = filterChanged,
            onQueryChange = { query ->
                searchQuery = query
            },
            onFilterReset = {
                filterChanged = false
                latestTopRouteFilter = TopRouteFilter(sortFilter = SortFilter.Latest)
            },
            onFilterClick = {
                isFilterShown = !isFilterShown
            },
            onRoutePageChanged = { routePage ->
                filterChanged = false
                isFilterShown = false
                latestTopRouteFilter = TopRouteFilter(sortFilter = SortFilter.Latest)
                showActivityFilter = routePage == RoutesPage.POPULAR
                viewModel.handleRoutePageChanged(routePage)
            }
        )

        if (isFilterShown) {
            TopRoutesFilterScreen(
                sortFilterList = sortFilterList,
                showActivityFilter = showActivityFilter,
                allTypes = topRouteTypes.map { it.activityType },
                topRouteFilter = latestTopRouteFilter,
                measurementUnit = measurementUnit,
                onFilterChanged = {
                    filterChanged = true
                    latestTopRouteFilter = it
                },
            )
        }

        when (currentPage) {
            RoutesPage.MINE -> MyRouteListScreen(
                viewData = myRouteViewData,
                searchQuery = searchQuery,
                topRouteFilter = latestTopRouteFilter,
                infoModelFormatter = infoModelFormatter,
                measurementUnit = measurementUnit
            )

            RoutesPage.POPULAR -> PopularListScreen(
                viewData = popularRouteViewData,
                searchQuery = searchQuery,
                topRouteFilter = latestTopRouteFilter,
                infoModelFormatter = infoModelFormatter,
                measurementUnit = measurementUnit
            )
        }
    }
}

@Composable
internal fun RouteEmptyContent(
    @StringRes emptyResId: Int,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .padding(MaterialTheme.spacing.medium)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.weight(0.2f))
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Icon(
                    painter = painterResource(R.drawable.ic_any_road_outline),
                    tint = MaterialTheme.colorScheme.mediumGrey,
                    contentDescription = null
                )

                Text(
                    modifier = Modifier.padding(MaterialTheme.spacing.medium),
                    text = stringResource(emptyResId),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurface,
                )
            }
            Spacer(modifier = Modifier.weight(0.75f))
        }
    }
}

@Composable
internal fun RouteFilterEmptyContent(
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(MaterialTheme.spacing.medium)
    ) {
        Text(
            text = stringResource(R.string.filter_with_no_result),
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface
        )
    }
}

