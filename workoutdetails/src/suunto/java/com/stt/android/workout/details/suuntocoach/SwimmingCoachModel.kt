package com.stt.android.workout.details.suuntocoach

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.ComposeView
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.compose.theme.AppTheme
import com.stt.android.diary.insights.coach.CoachFeedbackUiState
import com.stt.android.diary.insights.coach.CoachUiState
import com.stt.android.diary.insights.coach.TrainingHubCoach
import com.stt.android.workout.details.R
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList

@EpoxyModelClass
abstract class SwimmingCoachModel : EpoxyModelWithHolder<SwimmingCoachViewHolder>() {

    override fun getDefaultLayout() = R.layout.model_swimmig_coach

    @EpoxyAttribute
    lateinit var coachValue: List<CoachValueItem>

    override fun bind(holder: SwimmingCoachViewHolder) {
        with(holder) {
            swimmingCoachView.setContent {
                AppTheme {
                    var coachState by remember {
                        mutableStateOf(
                            CoachUiState(
                                items = coachValue.map {
                                    CoachFeedbackUiState(
                                        it.titleRes,
                                        persistentListOf(it.feedbackRes)
                                    )
                                }.toPersistentList(),
                                isExpanded = false
                            )
                        )
                    }
                    TrainingHubCoach(
                        coachUiState = coachState, toggleViewMoreOrLess = {
                            coachState = coachState.copy(isExpanded = !coachState.isExpanded)
                        },
                        swimmingCoach = true
                    )
                }
            }
        }
    }
}

class SwimmingCoachViewHolder : KotlinEpoxyHolder() {
    val swimmingCoachView by bind<ComposeView>(R.id.swimming_coach_view)
}
