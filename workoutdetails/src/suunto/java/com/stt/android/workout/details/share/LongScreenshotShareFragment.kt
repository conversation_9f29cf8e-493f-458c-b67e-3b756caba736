package com.stt.android.workout.details.share

import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Lifecycle
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import coil3.load
import coil3.request.transformations
import coil3.transform.CircleCropTransformation
import com.stt.android.coil.placeholderWithFallback
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.core.R
import com.stt.android.coroutines.launchOnLifecycle
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.extensions.combineLatest
import com.stt.android.home.diary.diarycalendar.activitygroups.ActivityTypeToGroupMapper
import com.stt.android.home.diary.diarycalendar.activitygroups.colorDrawableRes
import com.stt.android.home.explore.routes.RouteView
import com.stt.android.multimedia.sportie.SportieSelection
import com.stt.android.multimedia.sportie.SportieShareSource
import com.stt.android.multimedia.sportie.SportieShareType
import com.stt.android.ui.components.WeatherConditionsView
import com.stt.android.ui.utils.setOnClickListenerThrottled
import com.stt.android.utils.PermissionUtils
import com.stt.android.utils.STTConstants
import com.stt.android.workout.details.BaseWorkoutDetailsController
import com.stt.android.workout.details.BaseWorkoutDetailsFragment
import com.stt.android.workout.details.CoverImageData
import com.stt.android.workout.details.databinding.FragmentLongScreenshotShareBinding
import com.stt.android.workouts.sharepreview.ShareLinkViewModel
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareHelper
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareTargetListDialogFragment
import dagger.hilt.android.AndroidEntryPoint
import pub.devrel.easypermissions.AfterPermissionGranted
import timber.log.Timber
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import javax.inject.Inject
import com.stt.android.R as BaseR

@AndroidEntryPoint
open class LongScreenshotShareFragment : BaseWorkoutDetailsFragment() {

    companion object {
        const val TAG = "FragmentLongScreenshotShare"
        const val TARGET_LIST_DIALOG_TAG = "WorkoutShareTargetListDialogFragment"
        private const val NUMBERPHOTOADD = 0
        const val STORAGE_PERMISSION_REQUEST_CODE_FOR_SHARE = 110
        internal const val CURRENT_INDEX = "current_index"
        const val EXTRA_3D_VIDEO_LINK_SHARING = "EXTRA_3D_VIDEO_LINK_SHARING"

        fun newInstance(
            workoutHeaderId: Int,
            sportieShareSource: SportieShareSource?,
            currentIndex: Int,
            enable3dVideoLinkSharing: Boolean,
        ): LongScreenshotShareFragment {
            val longScreenshotShareFragment = LongScreenshotShareFragment()
            val args = Bundle()
            args.putInt(STTConstants.ExtraKeys.WORKOUT_ID, workoutHeaderId)
            args.putParcelable(
                MultipleWaysWorkoutShareActivity.EXTRA_SHARE_SOURCE,
                sportieShareSource
            )
            args.putInt(CURRENT_INDEX, currentIndex)
            args.putBoolean(EXTRA_3D_VIDEO_LINK_SHARING, enable3dVideoLinkSharing)
            longScreenshotShareFragment.arguments = args
            return longScreenshotShareFragment
        }
    }

    private var _binding: FragmentLongScreenshotShareBinding? = null
    private val binding get() = _binding!!

    @Inject
    lateinit var workoutShareHelper: WorkoutShareHelper

    @Inject
    lateinit var workoutDetailController: LongScreenshotShareWorkoutDetailsController

    private val longScreenshotViewModel: LongScreenshotShareViewModel by activityViewModels()

    private val shareLinkViewModel: ShareLinkViewModel by activityViewModels()

    private val sportieShareSource by lazy {
        val source =
            arguments?.getParcelable(MultipleWaysWorkoutShareActivity.EXTRA_SHARE_SOURCE) as SportieShareSource?
        source ?: SportieShareSource.UNKNOWN
    }

    private val workoutHeaderId: Int by lazy {
        arguments?.getInt(STTConstants.ExtraKeys.WORKOUT_ID) ?: 0
    }

    private val currentIndex: Int by lazy {
        arguments?.getInt(CURRENT_INDEX) ?: 0
    }

    override val workoutDetailsController: BaseWorkoutDetailsController
        get() = workoutDetailController

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentLongScreenshotShareBinding.inflate(layoutInflater)
        return _binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
    }

    override fun getRouteView(): RouteView = binding.routeView

    override fun getWeatherConditionsView(): WeatherConditionsView = binding.weatherConditionsView

    override fun getWorkoutDetailRV(): RecyclerView = binding.list
    override fun getWorkoutCoverImagePager(): ViewPager2 = binding.coverImagePager

    override fun loadData() {
        longScreenshotViewModel.loadUserInfo()
        viewModel.setLongScreenshotLayout(true)
        longScreenshotViewModel.loadWorkoutHeader(workoutHeaderId)
        viewModel.loadDataIfNeeded()
        viewModel.showMultisportPartActivity(
            arguments?.get(ARGUMENT_MULTISPORT_PART_ACTIVITY) as? MultisportPartActivity
        )
        advancedLapsViewModel.loadData(longScreenshotLayout = true)
    }

    override fun setShareButtonView(coverImageData: CoverImageData?) {
        // do nothing
    }

    override fun setShowWorkoutPlaybackButton(coverImageData: CoverImageData?) {
        // do nothing
    }

    override fun initView() {
        super.initView()
        coverImagePagerController.longScreenshotLayout = true
        launchOnLifecycle(Lifecycle.State.RESUMED) {
            longScreenshotViewModel.workoutHeaderState.collect { workoutHeader ->
                if (workoutHeader == null) {
                    Timber.w("found error,workoutHeader can't be null")
                    return@collect
                }
                initEvent(workoutHeader)
                bindCoverImagePager(workoutHeader)
                with(binding) {
                    bindActivityType(workoutHeader)
                    bindShareLink(workoutHeader.isSynced)
                }
            }
        }
        with(binding) {
            bindUserInfo()
            bindShareImage()
        }
    }

    private fun FragmentLongScreenshotShareBinding.bindActivityType(workoutHeader: WorkoutHeader) {
        activityType.text = workoutHeader.activityType.getLocalizedName(resources)
        val dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm")
        activityDate.text = dateTimeFormatter.format(
            LocalDateTime.ofInstant(
                Instant.ofEpochMilli(workoutHeader.startTime),
                ZoneId.systemDefault()
            )
        )
        val watchNameStr =
            activity?.intent?.getStringExtra(MultipleWaysWorkoutShareActivity.WATCH_NAME)
        // watchName is used to determine whether the workout is recorded by the watch or the mobile phone
        if (watchNameStr.isNullOrEmpty()) {
            watchName.visibility = View.GONE
        } else {
            watchName.text = watchNameStr
        }
        // set activity type icon
        val bgResId =
            ActivityTypeToGroupMapper().activityTypeIdToGroup(workoutHeader.activityType.id).colorDrawableRes
        activityTypeImage.setBackgroundResource(bgResId)
        activityTypeImage.setImageResource(workoutHeader.activityType.iconId)
    }

    private fun FragmentLongScreenshotShareBinding.bindShareLink(enabled: Boolean) {
        launchOnLifecycle(Lifecycle.State.RESUMED) {
            shareLinkViewModel.shareLinkConfigFlow.collect { config ->
                val enable3dVideoLinkSharing = arguments?.getBoolean(EXTRA_3D_VIDEO_LINK_SHARING) == true
                share3DLink.isVisible = enable3dVideoLinkSharing && enabled && config.show3DLink
                shareSummaryLink.isVisible = enabled && config.showSummaryLink

                share3DLink.setOnClickListenerThrottled {
                    longScreenshotViewModel.workoutHeaderState.value?.let {
                        longScreenshotViewModel.prepareShareLink(
                            isSummary = false
                        )
                    }
                }
                shareSummaryLink.setOnClickListenerThrottled {
                    longScreenshotViewModel.prepareShareLink(
                        isSummary = true
                    )
                }
            }

        }
    }

    private fun bindCoverImagePager(workoutHeader: WorkoutHeader) {
        val hasMapView = !workoutHeader.isPolylineEmpty
        viewModel.currentPhotoPagerPosition = if (hasMapView) {
            currentIndex - 1
        } else {
            currentIndex
        }
        getWorkoutCoverImagePager().currentItem = viewModel.currentPhotoPagerPosition
    }

    private fun FragmentLongScreenshotShareBinding.bindUserInfo() {
        launchOnLifecycle(Lifecycle.State.RESUMED) {
            longScreenshotViewModel.userInfoState.collect {
                it?.let { user ->
                    userName.text = user.realNameOrUsername
                    user.profileImageUrl?.let { imageUrl ->
                        userProfileIv.load(imageUrl) {
                            transformations(CircleCropTransformation())
                            placeholderWithFallback(userProfileIv.context, R.drawable.ic_default_profile_image_light)
                        }
                    }
                }
            }
        }
    }

    private fun FragmentLongScreenshotShareBinding.bindShareImage() {
        // when data is loaded, share button can click
        combineLatest(viewModel.viewState, advancedLapsViewModel.viewState).observe(
            viewLifecycleOwner
        ) {
            longScreenshotViewModel.setShareImageBtnEnabled(it.first.isLoaded() && it.second.isLoaded())
        }
        shareImageBtn.setOnClickListenerThrottled {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                longScreenshotViewModel.longScreenshotShare(nestedScrollview)
            } else {
                onPermissionRequired(
                    this@LongScreenshotShareFragment,
                    STORAGE_PERMISSION_REQUEST_CODE_FOR_SHARE,
                    PermissionUtils.STORAGE_PERMISSIONS,
                    BaseR.string.storage_permission_rationale_share,
                    ::onStoragePermissionGrantedForShare
                )
            }
        }
        launchOnLifecycle(Lifecycle.State.RESUMED) {
            longScreenshotViewModel.shareImageEnable.collect {
                shareImageBtn.isEnabled = it
            }
        }
    }

    override fun initCoverImageLoaderView(coverImagePager: ViewPager2) {
        super.initCoverImageLoaderView(coverImagePager)
        // longScreenshot can't scroll
        coverImagePager.isUserInputEnabled = false
    }

    private fun initEvent(workoutHeader: WorkoutHeader) {
        launchOnLifecycle(Lifecycle.State.RESUMED) {
            longScreenshotViewModel.prepareShareLinkEvent.collect { (loading, isSummary) ->
                if (!loading) {
                    showLoading()
                } else {
                    hideLoading()
                    // share link
                    // china flavor should show different platform items, other jump to the system share
                    if (workoutShareHelper.hasCustomIntentHandling()) {
                        WorkoutShareTargetListDialogFragment.newInstanceForLinkSharing(
                            workoutHeader,
                            if (isSummary) SportieShareSource.WORKOUT_SUMMARY else sportieShareSource,
                            NUMBERPHOTOADD
                        ).show(childFragmentManager, TARGET_LIST_DIALOG_TAG)
                    } else {
                        workoutShareHelper.sendImplicitWorkoutLinkShareIntent(
                            requireActivity(),
                            workoutHeader,
                            if (isSummary) SportieShareSource.WORKOUT_SUMMARY else sportieShareSource,
                            NUMBERPHOTOADD
                        )
                    }
                }
            }
        }
        launchOnLifecycle(Lifecycle.State.RESUMED) {
            longScreenshotViewModel.shareLinkErrorEvent.collect { throwable ->
                hideLoading()
                ErrorEvent.get(throwable::class).showErrorSnackbar()
            }
        }
        launchOnLifecycle(Lifecycle.State.RESUMED) {
            longScreenshotViewModel.longScreenshotShareEvent.collect { uri ->
                WorkoutShareTargetListDialogFragment.newInstanceForImageSharing(
                    workoutHeader,
                    uri,
                    SportieSelection(
                        sportieShareType = getScreenshotShareType()
                    ),
                    NUMBERPHOTOADD
                ).show(childFragmentManager, TARGET_LIST_DIALOG_TAG)
            }
        }
    }

    protected open fun getScreenshotShareType(): SportieShareType = SportieShareType.LONG_SCREENSHOT

    @AfterPermissionGranted(STORAGE_PERMISSION_REQUEST_CODE_FOR_SHARE)
    private fun onStoragePermissionGrantedForShare() {
        (requireActivity() as AppCompatActivity).run {
            longScreenshotViewModel.longScreenshotShare(binding.nestedScrollview)
        }
    }
}
