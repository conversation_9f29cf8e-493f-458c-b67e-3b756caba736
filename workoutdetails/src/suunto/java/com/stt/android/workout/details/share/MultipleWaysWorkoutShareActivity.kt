package com.stt.android.workout.details.share

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Parcelable
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityOptionsCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayout.OnTabSelectedListener
import com.google.android.material.tabs.TabLayoutMediator
import com.stt.android.infomodel.SummaryItem
import com.stt.android.maps.MapSnapshotter
import com.stt.android.maps.SuuntoMaps
import com.stt.android.maps.mapbox.MapboxMapsProvider
import com.stt.android.multimedia.sportie.SportieShareSource
import com.stt.android.sharingplatform.SharingResultState
import com.stt.android.utils.STTConstants
import com.stt.android.workout.details.R
import com.stt.android.workout.details.databinding.ActivityMultipleWorkoutShareWaysBinding
import com.stt.android.workouts.sharepreview.WorkoutSharePreviewActivity
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareHelper
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject
import com.stt.android.R as BaseR

@AndroidEntryPoint
class MultipleWaysWorkoutShareActivity : AppCompatActivity() {

    private var binding: ActivityMultipleWorkoutShareWaysBinding? = null

    @Inject
    lateinit var suuntoMaps: SuuntoMaps

    @Inject
    lateinit var mapSnapshotter: MapSnapshotter

    @Inject
    lateinit var workoutShareHelper: WorkoutShareHelper

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMultipleWorkoutShareWaysBinding.inflate(layoutInflater)
        setContentView(binding?.root)
        setSupportActionBar(binding?.toolbar)
        supportActionBar?.apply {
            title = getString(R.string.share_title)
        }
        binding?.toolbar?.setNavigationOnClickListener {
            onBackPressedDispatcher.onBackPressed()
        }
        initView()

        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                mapSnapshotter.runSnapshotterEngine(applicationContext)
            }
        }
    }

    @SuppressLint("CommitTransaction")
    private fun initView() {
        val workoutHeaderId =
            intent.getIntExtra(STTConstants.ExtraKeys.WORKOUT_ID, 0)
        val currentIndex = intent.getIntExtra(WorkoutSharePreviewActivity.CURRENT_ITEM_INDEX, 0)
        val workoutDetail = intent.getParcelableExtra<SportieShareSource>(
            WorkoutSharePreviewActivity.EXTRA_SHARE_SOURCE
        )
        val currentSummaryItems =
            intent.getStringArrayListExtra(WorkoutSharePreviewActivity.CURRENT_SUMMARY_ITEMS)
                ?: arrayListOf()
        val byScreenshot = workoutDetail == SportieShareSource.WORKOUT_DETAILS_SCREENSHOT
        val supportVideoShare = intent.getBooleanExtra(SUPPORT_VIDEO_SHARE, false)
            && suuntoMaps.defaultProvider?.name == MapboxMapsProvider.NAME
            && !VIDEO_SHARE_BLACKLIST.contains(Build.MODEL)
            && !isTensorProcessor() // app freeze when playback on tensor processors
        binding?.apply {
            shareViewPager.offscreenPageLimit = 2
            val fragmentShareAdapter = FragmentShareAdapter(
                this@MultipleWaysWorkoutShareActivity,
                workoutHeaderId,
                currentIndex,
                workoutDetail,
                currentSummaryItems,
                workoutShareHelper.supportLongScreenshot(),
                supportVideoShare,
            )
            shareViewPager.adapter = fragmentShareAdapter
            val mediator = TabLayoutMediator(
                workoutShareTab,
                shareViewPager
            ) { tab, pos ->
                tab.text = getString(fragmentShareAdapter.getItemTitle(pos))
            }
            mediator.attach()
            workoutShareTab.addOnTabSelectedListener(object : OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    invalidateMenu()
                    shareViewPager.setCurrentItem(tab?.position ?: 0, false)
                }

                override fun onTabUnselected(tab: TabLayout.Tab?) {}

                override fun onTabReselected(tab: TabLayout.Tab?) {}
            })
            shareViewPager.isUserInputEnabled = false
            if (byScreenshot) shareViewPager.setCurrentItem(1, false)
        }
        workoutShareHelper.setWeChatShareResultHandler(::handleShareResult)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        val fragments = supportFragmentManager.fragments
        if (fragments.isNotEmpty()) {
            for (item in fragments) {
                item.onActivityResult(requestCode, resultCode, data)
            }
        }
        // handle weibo share result
        data?.let {
            workoutShareHelper.handleWeiboShareResult(it, ::handleShareResult)
        }
    }

    private fun handleShareResult(shareResultState: SharingResultState) {
        val message = when (shareResultState) {
            SharingResultState.Success -> getString(BaseR.string.share_suucess)
            SharingResultState.Fail -> getString(BaseR.string.share_fail)
            SharingResultState.Cancel -> getString(BaseR.string.share_cancel)
        }
        showSnackbar(message)
    }

    private fun showSnackbar(message: String) {
        binding?.root?.let { Snackbar.make(it, message, Snackbar.LENGTH_SHORT).show() }
    }

    override fun onDestroy() {
        super.onDestroy()
        workoutShareHelper.setWeChatShareResultHandler(null)
        workoutShareHelper.handleWeiboShareResult(intent, null)
    }

    companion object {
        const val CURRENT_ITEM_INDEX = "com.stt.android.CURRENT_ITEM_INDEX"
        const val CURRENT_SUMMARY_ITEMS = "com.stt.android.CURRENT_SUMMARY_ITEMS"
        const val EXTRA_SHARE_SOURCE = "EXTRA_SHARE_SOURCE"
        const val WORKOUT_ID = "workoutId"
        const val WATCH_NAME = "watch_name"
        const val SUPPORT_VIDEO_SHARE = "support_video_share"

        private val VIDEO_SHARE_BLACKLIST = setOf(
            "M2011K2C", // xiaomi 11
        )

        private fun isTensorProcessor(): Boolean {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) return false
            if (!Build.SOC_MANUFACTURER.equals("Google", ignoreCase = true)) return false
            return Build.SOC_MODEL.contains("Tensor", ignoreCase = true)
        }

        @JvmStatic
        fun getIntent(
            workoutHeaderId: Int,
            context: Context,
            itemIndex: Int,
            workoutDetails: SportieShareSource,
            defaultSummaryItems: List<SummaryItem> = emptyList(),
            watchName: String,
            supportVideoShare: Boolean,
        ): androidx.core.util.Pair<Intent, ActivityOptionsCompat> {
            val intent = Intent(context, MultipleWaysWorkoutShareActivity::class.java)
            intent.putExtra(CURRENT_ITEM_INDEX, itemIndex)
            intent.putExtra(EXTRA_SHARE_SOURCE, workoutDetails as Parcelable)
            // WorkoutDetailsViewModelNew use it, can't remove
            intent.putExtra(WORKOUT_ID, workoutHeaderId)
            intent.putExtra(STTConstants.ExtraKeys.WORKOUT_ID, workoutHeaderId)
            intent.putExtra(WATCH_NAME, watchName)
            intent.putStringArrayListExtra(
                CURRENT_SUMMARY_ITEMS,
                ArrayList(defaultSummaryItems.map { it.name }),
            )
            intent.putExtra(SUPPORT_VIDEO_SHARE, supportVideoShare)

            return androidx.core.util.Pair(
                intent,
                ActivityOptionsCompat.makeCustomAnimation(
                    context,
                    BaseR.anim.activity_open_enter,
                    BaseR.anim.activity_open_exit
                )
            )
        }
    }
}
